var crypto_utils=function(t){"use strict";function e(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function r(t,...e){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new Error(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}function n(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}const i="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0,o=t=>t instanceof Uint8Array,s=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),f=(t,e)=>t<<32-e|t>>>e;
/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function a(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}(t)),!o(t))throw new Error("expected Uint8Array, got "+typeof t);return t}let c=class{clone(){return this._cloneInto()}};function u(t){const e=e=>t().update(a(e)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function h(t=32){if(i&&"function"==typeof i.getRandomValues)return i.getRandomValues(new Uint8Array(t));throw new Error("crypto.getRandomValues must be defined")}let d=class extends c{constructor(t,e,r,n){super(),this.blockLen=t,this.outputLen=e,this.padOffset=r,this.isLE=n,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=s(this.buffer)}update(t){n(this);const{view:e,buffer:r,blockLen:i}=this,o=(t=a(t)).length;for(let n=0;n<o;){const f=Math.min(i-this.pos,o-n);if(f!==i)r.set(t.subarray(n,n+f),this.pos),this.pos+=f,n+=f,this.pos===i&&(this.process(e,0),this.pos=0);else{const e=s(t);for(;i<=o-n;n+=i)this.process(e,n)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){n(this),function(t,e){r(t);const n=e.outputLen;if(t.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}(t,this),this.finished=!0;const{buffer:e,view:i,blockLen:o,isLE:f}=this;let{pos:a}=this;e[a++]=128,this.buffer.subarray(a).fill(0),this.padOffset>o-a&&(this.process(i,0),a=0);for(let t=a;t<o;t++)e[t]=0;!function(t,e,r,n){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,r,n);const i=BigInt(32),o=BigInt(4294967295),s=Number(r>>i&o),f=Number(r&o),a=n?4:0,c=n?0:4;t.setUint32(e+a,s,n),t.setUint32(e+c,f,n)}(i,o-8,BigInt(8*this.length),f),this.process(i,0);const c=s(t),u=this.outputLen;if(u%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const h=u/4,d=this.get();if(h>d.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<h;t++)c.setUint32(4*t,d[t],f)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const r=t.slice(0,e);return this.destroy(),r}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:r,length:n,finished:i,destroyed:o,pos:s}=this;return t.length=n,t.pos=s,t.finished=i,t.destroyed=o,n%e&&t.buffer.set(r),t}};const l=(t,e,r)=>t&e^t&r^e&r,g=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),w=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),p=new Uint32Array(64);let y=class extends d{constructor(){super(64,32,8,!1),this.A=0|w[0],this.B=0|w[1],this.C=0|w[2],this.D=0|w[3],this.E=0|w[4],this.F=0|w[5],this.G=0|w[6],this.H=0|w[7]}get(){const{A:t,B:e,C:r,D:n,E:i,F:o,G:s,H:f}=this;return[t,e,r,n,i,o,s,f]}set(t,e,r,n,i,o,s,f){this.A=0|t,this.B=0|e,this.C=0|r,this.D=0|n,this.E=0|i,this.F=0|o,this.G=0|s,this.H=0|f}process(t,e){for(let r=0;r<16;r++,e+=4)p[r]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=p[t-15],r=p[t-2],n=f(e,7)^f(e,18)^e>>>3,i=f(r,17)^f(r,19)^r>>>10;p[t]=i+p[t-7]+n+p[t-16]|0}let{A:r,B:n,C:i,D:o,E:s,F:a,G:c,H:u}=this;for(let t=0;t<64;t++){const e=u+(f(s,6)^f(s,11)^f(s,25))+((h=s)&a^~h&c)+g[t]+p[t]|0,d=(f(r,2)^f(r,13)^f(r,22))+l(r,n,i)|0;u=c,c=a,a=s,s=o+e|0,o=i,i=n,n=r,r=e+d|0}var h;r=r+this.A|0,n=n+this.B|0,i=i+this.C|0,o=o+this.D|0,s=s+this.E|0,a=a+this.F|0,c=c+this.G|0,u=u+this.H|0,this.set(r,n,i,o,s,a,c,u)}roundClean(){p.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}};const b=u((()=>new y)),m=BigInt(0),x=BigInt(1),E=BigInt(2),v=t=>t instanceof Uint8Array,B=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function A(t){if(!v(t))throw new Error("Uint8Array expected");let e="";for(let r=0;r<t.length;r++)e+=B[t[r]];return e}function _(t){const e=t.toString(16);return 1&e.length?`0${e}`:e}function I(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);return BigInt(""===t?"0":`0x${t}`)}function U(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);const e=t.length;if(e%2)throw new Error("padded hex string expected, got unpadded hex of length "+e);const r=new Uint8Array(e/2);for(let e=0;e<r.length;e++){const n=2*e,i=t.slice(n,n+2),o=Number.parseInt(i,16);if(Number.isNaN(o)||o<0)throw new Error("Invalid byte sequence");r[e]=o}return r}function S(t){return I(A(t))}function L(t){if(!v(t))throw new Error("Uint8Array expected");return I(A(Uint8Array.from(t).reverse()))}function $(t,e){return U(t.toString(16).padStart(2*e,"0"))}function k(t,e){return $(t,e).reverse()}function H(t,e,r){let n;if("string"==typeof e)try{n=U(e)}catch(r){throw new Error(`${t} must be valid hex string, got "${e}". Cause: ${r}`)}else{if(!v(e))throw new Error(`${t} must be hex string or Uint8Array`);n=Uint8Array.from(e)}const i=n.length;if("number"==typeof r&&i!==r)throw new Error(`${t} expected ${r} bytes, got ${i}`);return n}function O(...t){const e=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));let r=0;return t.forEach((t=>{if(!v(t))throw new Error("Uint8Array expected");e.set(t,r),r+=t.length})),e}const N=t=>(E<<BigInt(t-1))-x,R=t=>new Uint8Array(t),T=t=>Uint8Array.from(t);function j(t,e,r){if("number"!=typeof t||t<2)throw new Error("hashLen must be a number");if("number"!=typeof e||e<2)throw new Error("qByteLen must be a number");if("function"!=typeof r)throw new Error("hmacFn must be a function");let n=R(t),i=R(t),o=0;const s=()=>{n.fill(1),i.fill(0),o=0},f=(...t)=>r(i,n,...t),a=(t=R())=>{i=f(T([0]),t),n=f(),0!==t.length&&(i=f(T([1]),t),n=f())},c=()=>{if(o++>=1e3)throw new Error("drbg: tried 1000 values");let t=0;const r=[];for(;t<e;){n=f();const e=n.slice();r.push(e),t+=n.length}return O(...r)};return(t,e)=>{let r;for(s(),a(t);!(r=e(c()));)a();return s(),r}}const C={bigint:t=>"bigint"==typeof t,function:t=>"function"==typeof t,boolean:t=>"boolean"==typeof t,string:t=>"string"==typeof t,stringOrUint8Array:t=>"string"==typeof t||t instanceof Uint8Array,isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>"function"==typeof t&&Number.isSafeInteger(t.outputLen)};function z(t,e,r={}){const n=(e,r,n)=>{const i=C[r];if("function"!=typeof i)throw new Error(`Invalid validator "${r}", expected function`);const o=t[e];if(!(n&&void 0===o||i(o,t)))throw new Error(`Invalid param ${String(e)}=${o} (${typeof o}), expected ${r}`)};for(const[t,r]of Object.entries(e))n(t,r,!1);for(const[t,e]of Object.entries(r))n(t,e,!0);return t}var P=Object.freeze({__proto__:null,bitGet:function(t,e){return t>>BigInt(e)&x},bitLen:function(t){let e;for(e=0;t>m;t>>=x,e+=1);return e},bitMask:N,bitSet:(t,e,r)=>t|(r?x:m)<<BigInt(e),bytesToHex:A,bytesToNumberBE:S,bytesToNumberLE:L,concatBytes:O,createHmacDrbg:j,ensureBytes:H,equalBytes:function(t,e){if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++)if(t[r]!==e[r])return!1;return!0},hexToBytes:U,hexToNumber:I,numberToBytesBE:$,numberToBytesLE:k,numberToHexUnpadded:_,numberToVarBytesBE:function(t){return U(_(t))},utf8ToBytes:function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))},validateObject:z});
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const q=BigInt(0),F=BigInt(1),V=BigInt(2),D=BigInt(3),G=BigInt(4),Z=BigInt(5),Y=BigInt(8);function W(t,e){const r=t%e;return r>=q?r:e+r}function K(t,e,r){if(r<=q||e<q)throw new Error("Expected power/modulo > 0");if(r===F)return q;let n=F;for(;e>q;)e&F&&(n=n*t%r),t=t*t%r,e>>=F;return n}function M(t,e,r){let n=t;for(;e-- >q;)n*=n,n%=r;return n}function J(t,e){if(t===q||e<=q)throw new Error(`invert: expected positive integers, got n=${t} mod=${e}`);let r=W(t,e),n=e,i=q,o=F;for(;r!==q;){const t=n%r,e=i-o*(n/r);n=r,r=t,i=o,o=e}if(n!==F)throw new Error("invert: does not exist");return W(i,e)}function X(t){if(t%G===D){const e=(t+F)/G;return function(t,r){const n=t.pow(r,e);if(!t.eql(t.sqr(n),r))throw new Error("Cannot find square root");return n}}if(t%Y===Z){const e=(t-Z)/Y;return function(t,r){const n=t.mul(r,V),i=t.pow(n,e),o=t.mul(r,i),s=t.mul(t.mul(o,V),i),f=t.mul(o,t.sub(s,t.ONE));if(!t.eql(t.sqr(f),r))throw new Error("Cannot find square root");return f}}return function(t){const e=(t-F)/V;let r,n,i;for(r=t-F,n=0;r%V===q;r/=V,n++);for(i=V;i<t&&K(i,e,t)!==t-F;i++);if(1===n){const e=(t+F)/G;return function(t,r){const n=t.pow(r,e);if(!t.eql(t.sqr(n),r))throw new Error("Cannot find square root");return n}}const o=(r+F)/V;return function(t,s){if(t.pow(s,e)===t.neg(t.ONE))throw new Error("Cannot find square root");let f=n,a=t.pow(t.mul(t.ONE,i),r),c=t.pow(s,o),u=t.pow(s,r);for(;!t.eql(u,t.ONE);){if(t.eql(u,t.ZERO))return t.ZERO;let e=1;for(let r=t.sqr(u);e<f&&!t.eql(r,t.ONE);e++)r=t.sqr(r);const r=t.pow(a,F<<BigInt(f-e-1));a=t.sqr(r),c=t.mul(c,r),u=t.mul(u,a),f=e}return c}}(t)}BigInt(9),BigInt(16);const Q=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function tt(t,e){const r=void 0!==e?e:t.toString(2).length;return{nBitLength:r,nByteLength:Math.ceil(r/8)}}function et(t,e,r=!1,n={}){if(t<=q)throw new Error(`Expected Field ORDER > 0, got ${t}`);const{nBitLength:i,nByteLength:o}=tt(t,e);if(o>2048)throw new Error("Field lengths over 2048 bytes are not supported");const s=X(t),f=Object.freeze({ORDER:t,BITS:i,BYTES:o,MASK:N(i),ZERO:q,ONE:F,create:e=>W(e,t),isValid:e=>{if("bigint"!=typeof e)throw new Error("Invalid field element: expected bigint, got "+typeof e);return q<=e&&e<t},is0:t=>t===q,isOdd:t=>(t&F)===F,neg:e=>W(-e,t),eql:(t,e)=>t===e,sqr:e=>W(e*e,t),add:(e,r)=>W(e+r,t),sub:(e,r)=>W(e-r,t),mul:(e,r)=>W(e*r,t),pow:(t,e)=>function(t,e,r){if(r<q)throw new Error("Expected power > 0");if(r===q)return t.ONE;if(r===F)return e;let n=t.ONE,i=e;for(;r>q;)r&F&&(n=t.mul(n,i)),i=t.sqr(i),r>>=F;return n}(f,t,e),div:(e,r)=>W(e*J(r,t),t),sqrN:t=>t*t,addN:(t,e)=>t+e,subN:(t,e)=>t-e,mulN:(t,e)=>t*e,inv:e=>J(e,t),sqrt:n.sqrt||(t=>s(f,t)),invertBatch:t=>function(t,e){const r=new Array(e.length),n=e.reduce(((e,n,i)=>t.is0(n)?e:(r[i]=e,t.mul(e,n))),t.ONE),i=t.inv(n);return e.reduceRight(((e,n,i)=>t.is0(n)?e:(r[i]=t.mul(e,r[i]),t.mul(e,n))),i),r}(f,t),cmov:(t,e,r)=>r?e:t,toBytes:t=>r?k(t,o):$(t,o),fromBytes:t=>{if(t.length!==o)throw new Error(`Fp.fromBytes: expected ${o}, got ${t.length}`);return r?L(t):S(t)}});return Object.freeze(f)}function rt(t){if("bigint"!=typeof t)throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function nt(t){const e=rt(t);return e+Math.ceil(e/2)}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const it=BigInt(0),ot=BigInt(1);function st(t){return z(t.Fp,Q.reduce(((t,e)=>(t[e]="function",t)),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"})),z(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...tt(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const{bytesToNumberBE:ft,hexToBytes:at}=P,ct={Err:class extends Error{constructor(t=""){super(t)}},_parseInt(t){const{Err:e}=ct;if(t.length<2||2!==t[0])throw new e("Invalid signature integer tag");const r=t[1],n=t.subarray(2,r+2);if(!r||n.length!==r)throw new e("Invalid signature integer: wrong length");if(128&n[0])throw new e("Invalid signature integer: negative");if(0===n[0]&&!(128&n[1]))throw new e("Invalid signature integer: unnecessary leading zero");return{d:ft(n),l:t.subarray(r+2)}},toSig(t){const{Err:e}=ct,r="string"==typeof t?at(t):t;if(!(r instanceof Uint8Array))throw new Error("ui8a expected");let n=r.length;if(n<2||48!=r[0])throw new e("Invalid signature tag");if(r[1]!==n-2)throw new e("Invalid signature: incorrect length");const{d:i,l:o}=ct._parseInt(r.subarray(2)),{d:s,l:f}=ct._parseInt(o);if(f.length)throw new e("Invalid signature: left bytes after parsing");return{r:i,s:s}},hexFromSig(t){const e=t=>8&Number.parseInt(t[0],16)?"00"+t:t,r=t=>{const e=t.toString(16);return 1&e.length?`0${e}`:e},n=e(r(t.s)),i=e(r(t.r)),o=n.length/2,s=i.length/2,f=r(o),a=r(s);return`30${r(s+o+4)}02${a}${i}02${f}${n}`}},ut=BigInt(0),ht=BigInt(1);BigInt(2);const dt=BigInt(3);function lt(t){const e=function(t){const e=st(t);z(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:r,Fp:n,a:i}=e;if(r){if(!n.eql(i,n.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if("object"!=typeof r||"bigint"!=typeof r.beta||"function"!=typeof r.splitScalar)throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...e})}(t),{Fp:r}=e,n=e.toBytes||((t,e,n)=>{const i=e.toAffine();return O(Uint8Array.from([4]),r.toBytes(i.x),r.toBytes(i.y))}),i=e.fromBytes||(t=>{const e=t.subarray(1);return{x:r.fromBytes(e.subarray(0,r.BYTES)),y:r.fromBytes(e.subarray(r.BYTES,2*r.BYTES))}});function o(t){const{a:n,b:i}=e,o=r.sqr(t),s=r.mul(o,t);return r.add(r.add(s,r.mul(t,n)),i)}if(!r.eql(r.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function s(t){return"bigint"==typeof t&&ut<t&&t<e.n}function f(t){if(!s(t))throw new Error("Expected valid bigint: 0 < bigint < curve.n")}function a(t){const{allowedPrivateKeyLengths:r,nByteLength:n,wrapPrivateKey:i,n:o}=e;if(r&&"bigint"!=typeof t){if(t instanceof Uint8Array&&(t=A(t)),"string"!=typeof t||!r.includes(t.length))throw new Error("Invalid key");t=t.padStart(2*n,"0")}let s;try{s="bigint"==typeof t?t:S(H("private key",t,n))}catch(e){throw new Error(`private key must be ${n} bytes, hex or bigint, not ${typeof t}`)}return i&&(s=W(s,o)),f(s),s}const c=new Map;function u(t){if(!(t instanceof h))throw new Error("ProjectivePoint expected")}class h{constructor(t,e,n){if(this.px=t,this.py=e,this.pz=n,null==t||!r.isValid(t))throw new Error("x required");if(null==e||!r.isValid(e))throw new Error("y required");if(null==n||!r.isValid(n))throw new Error("z required")}static fromAffine(t){const{x:e,y:n}=t||{};if(!t||!r.isValid(e)||!r.isValid(n))throw new Error("invalid affine point");if(t instanceof h)throw new Error("projective point not allowed");const i=t=>r.eql(t,r.ZERO);return i(e)&&i(n)?h.ZERO:new h(e,n,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(t){const e=r.invertBatch(t.map((t=>t.pz)));return t.map(((t,r)=>t.toAffine(e[r]))).map(h.fromAffine)}static fromHex(t){const e=h.fromAffine(i(H("pointHex",t)));return e.assertValidity(),e}static fromPrivateKey(t){return h.BASE.multiply(a(t))}_setWindowSize(t){this._WINDOW_SIZE=t,c.delete(this)}assertValidity(){if(this.is0()){if(e.allowInfinityPoint&&!r.is0(this.py))return;throw new Error("bad point: ZERO")}const{x:t,y:n}=this.toAffine();if(!r.isValid(t)||!r.isValid(n))throw new Error("bad point: x or y not FE");const i=r.sqr(n),s=o(t);if(!r.eql(i,s))throw new Error("bad point: equation left != right");if(!this.isTorsionFree())throw new Error("bad point: not in prime-order subgroup")}hasEvenY(){const{y:t}=this.toAffine();if(r.isOdd)return!r.isOdd(t);throw new Error("Field doesn't support isOdd")}equals(t){u(t);const{px:e,py:n,pz:i}=this,{px:o,py:s,pz:f}=t,a=r.eql(r.mul(e,f),r.mul(o,i)),c=r.eql(r.mul(n,f),r.mul(s,i));return a&&c}negate(){return new h(this.px,r.neg(this.py),this.pz)}double(){const{a:t,b:n}=e,i=r.mul(n,dt),{px:o,py:s,pz:f}=this;let a=r.ZERO,c=r.ZERO,u=r.ZERO,d=r.mul(o,o),l=r.mul(s,s),g=r.mul(f,f),w=r.mul(o,s);return w=r.add(w,w),u=r.mul(o,f),u=r.add(u,u),a=r.mul(t,u),c=r.mul(i,g),c=r.add(a,c),a=r.sub(l,c),c=r.add(l,c),c=r.mul(a,c),a=r.mul(w,a),u=r.mul(i,u),g=r.mul(t,g),w=r.sub(d,g),w=r.mul(t,w),w=r.add(w,u),u=r.add(d,d),d=r.add(u,d),d=r.add(d,g),d=r.mul(d,w),c=r.add(c,d),g=r.mul(s,f),g=r.add(g,g),d=r.mul(g,w),a=r.sub(a,d),u=r.mul(g,l),u=r.add(u,u),u=r.add(u,u),new h(a,c,u)}add(t){u(t);const{px:n,py:i,pz:o}=this,{px:s,py:f,pz:a}=t;let c=r.ZERO,d=r.ZERO,l=r.ZERO;const g=e.a,w=r.mul(e.b,dt);let p=r.mul(n,s),y=r.mul(i,f),b=r.mul(o,a),m=r.add(n,i),x=r.add(s,f);m=r.mul(m,x),x=r.add(p,y),m=r.sub(m,x),x=r.add(n,o);let E=r.add(s,a);return x=r.mul(x,E),E=r.add(p,b),x=r.sub(x,E),E=r.add(i,o),c=r.add(f,a),E=r.mul(E,c),c=r.add(y,b),E=r.sub(E,c),l=r.mul(g,x),c=r.mul(w,b),l=r.add(c,l),c=r.sub(y,l),l=r.add(y,l),d=r.mul(c,l),y=r.add(p,p),y=r.add(y,p),b=r.mul(g,b),x=r.mul(w,x),y=r.add(y,b),b=r.sub(p,b),b=r.mul(g,b),x=r.add(x,b),p=r.mul(y,x),d=r.add(d,p),p=r.mul(E,x),c=r.mul(m,c),c=r.sub(c,p),p=r.mul(m,y),l=r.mul(E,l),l=r.add(l,p),new h(c,d,l)}subtract(t){return this.add(t.negate())}is0(){return this.equals(h.ZERO)}wNAF(t){return l.wNAFCached(this,c,t,(t=>{const e=r.invertBatch(t.map((t=>t.pz)));return t.map(((t,r)=>t.toAffine(e[r]))).map(h.fromAffine)}))}multiplyUnsafe(t){const n=h.ZERO;if(t===ut)return n;if(f(t),t===ht)return this;const{endo:i}=e;if(!i)return l.unsafeLadder(this,t);let{k1neg:o,k1:s,k2neg:a,k2:c}=i.splitScalar(t),u=n,d=n,g=this;for(;s>ut||c>ut;)s&ht&&(u=u.add(g)),c&ht&&(d=d.add(g)),g=g.double(),s>>=ht,c>>=ht;return o&&(u=u.negate()),a&&(d=d.negate()),d=new h(r.mul(d.px,i.beta),d.py,d.pz),u.add(d)}multiply(t){f(t);let n,i,o=t;const{endo:s}=e;if(s){const{k1neg:t,k1:e,k2neg:f,k2:a}=s.splitScalar(o);let{p:c,f:u}=this.wNAF(e),{p:d,f:g}=this.wNAF(a);c=l.constTimeNegate(t,c),d=l.constTimeNegate(f,d),d=new h(r.mul(d.px,s.beta),d.py,d.pz),n=c.add(d),i=u.add(g)}else{const{p:t,f:e}=this.wNAF(o);n=t,i=e}return h.normalizeZ([n,i])[0]}multiplyAndAddUnsafe(t,e,r){const n=h.BASE,i=(t,e)=>e!==ut&&e!==ht&&t.equals(n)?t.multiply(e):t.multiplyUnsafe(e),o=i(this,e).add(i(t,r));return o.is0()?void 0:o}toAffine(t){const{px:e,py:n,pz:i}=this,o=this.is0();null==t&&(t=o?r.ONE:r.inv(i));const s=r.mul(e,t),f=r.mul(n,t),a=r.mul(i,t);if(o)return{x:r.ZERO,y:r.ZERO};if(!r.eql(a,r.ONE))throw new Error("invZ was invalid");return{x:s,y:f}}isTorsionFree(){const{h:t,isTorsionFree:r}=e;if(t===ht)return!0;if(r)return r(h,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:t,clearCofactor:r}=e;return t===ht?this:r?r(h,this):this.multiplyUnsafe(e.h)}toRawBytes(t=!0){return this.assertValidity(),n(h,this,t)}toHex(t=!0){return A(this.toRawBytes(t))}}h.BASE=new h(e.Gx,e.Gy,r.ONE),h.ZERO=new h(r.ZERO,r.ONE,r.ZERO);const d=e.nBitLength,l=function(t,e){const r=(t,e)=>{const r=e.negate();return t?r:e},n=t=>({windows:Math.ceil(e/t)+1,windowSize:2**(t-1)});return{constTimeNegate:r,unsafeLadder(e,r){let n=t.ZERO,i=e;for(;r>it;)r&ot&&(n=n.add(i)),i=i.double(),r>>=ot;return n},precomputeWindow(t,e){const{windows:r,windowSize:i}=n(e),o=[];let s=t,f=s;for(let t=0;t<r;t++){f=s,o.push(f);for(let t=1;t<i;t++)f=f.add(s),o.push(f);s=f.double()}return o},wNAF(e,i,o){const{windows:s,windowSize:f}=n(e);let a=t.ZERO,c=t.BASE;const u=BigInt(2**e-1),h=2**e,d=BigInt(e);for(let t=0;t<s;t++){const e=t*f;let n=Number(o&u);o>>=d,n>f&&(n-=h,o+=ot);const s=e,l=e+Math.abs(n)-1,g=t%2!=0,w=n<0;0===n?c=c.add(r(g,i[s])):a=a.add(r(w,i[l]))}return{p:a,f:c}},wNAFCached(t,e,r,n){const i=t._WINDOW_SIZE||1;let o=e.get(t);return o||(o=this.precomputeWindow(t,i),1!==i&&e.set(t,n(o))),this.wNAF(i,o,r)}}}(h,e.endo?Math.ceil(d/2):d);return{CURVE:e,ProjectivePoint:h,normPrivateKeyToScalar:a,weierstrassEquation:o,isWithinCurveOrder:s}}function gt(t){const e=function(t){const e=st(t);return z(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}(t),{Fp:r,n:n}=e,i=r.BYTES+1,o=2*r.BYTES+1;function s(t){return W(t,n)}function f(t){return J(t,n)}const{ProjectivePoint:a,normPrivateKeyToScalar:c,weierstrassEquation:u,isWithinCurveOrder:h}=lt({...e,toBytes(t,e,n){const i=e.toAffine(),o=r.toBytes(i.x),s=O;return n?s(Uint8Array.from([e.hasEvenY()?2:3]),o):s(Uint8Array.from([4]),o,r.toBytes(i.y))},fromBytes(t){const e=t.length,n=t[0],s=t.subarray(1);if(e!==i||2!==n&&3!==n){if(e===o&&4===n){return{x:r.fromBytes(s.subarray(0,r.BYTES)),y:r.fromBytes(s.subarray(r.BYTES,2*r.BYTES))}}throw new Error(`Point of length ${e} was invalid. Expected ${i} compressed bytes or ${o} uncompressed bytes`)}{const t=S(s);if(!(ut<(f=t)&&f<r.ORDER))throw new Error("Point is not on curve");const e=u(t);let i=r.sqrt(e);return 1==(1&n)!==((i&ht)===ht)&&(i=r.neg(i)),{x:t,y:i}}var f}}),d=t=>A($(t,e.nByteLength));function l(t){return t>n>>ht}const g=(t,e,r)=>S(t.slice(e,r));class w{constructor(t,e,r){this.r=t,this.s=e,this.recovery=r,this.assertValidity()}static fromCompact(t){const r=e.nByteLength;return t=H("compactSignature",t,2*r),new w(g(t,0,r),g(t,r,2*r))}static fromDER(t){const{r:e,s:r}=ct.toSig(H("DER",t));return new w(e,r)}assertValidity(){if(!h(this.r))throw new Error("r must be 0 < r < CURVE.n");if(!h(this.s))throw new Error("s must be 0 < s < CURVE.n")}addRecoveryBit(t){return new w(this.r,this.s,t)}recoverPublicKey(t){const{r:n,s:i,recovery:o}=this,c=m(H("msgHash",t));if(null==o||![0,1,2,3].includes(o))throw new Error("recovery id invalid");const u=2===o||3===o?n+e.n:n;if(u>=r.ORDER)throw new Error("recovery id 2 or 3 invalid");const h=0==(1&o)?"02":"03",l=a.fromHex(h+d(u)),g=f(u),w=s(-c*g),p=s(i*g),y=a.BASE.multiplyAndAddUnsafe(l,w,p);if(!y)throw new Error("point at infinify");return y.assertValidity(),y}hasHighS(){return l(this.s)}normalizeS(){return this.hasHighS()?new w(this.r,s(-this.s),this.recovery):this}toDERRawBytes(){return U(this.toDERHex())}toDERHex(){return ct.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return U(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const p={isValidPrivateKey(t){try{return c(t),!0}catch(t){return!1}},normPrivateKeyToScalar:c,randomPrivateKey:()=>{const t=nt(e.n);return function(t,e,r=!1){const n=t.length,i=rt(e),o=nt(e);if(n<16||n<o||n>1024)throw new Error(`expected ${o}-1024 bytes of input, got ${n}`);const s=W(r?S(t):L(t),e-F)+F;return r?k(s,i):$(s,i)}(e.randomBytes(t),e.n)},precompute:(t=8,e=a.BASE)=>(e._setWindowSize(t),e.multiply(BigInt(3)),e)};function y(t){const e=t instanceof Uint8Array,r="string"==typeof t,n=(e||r)&&t.length;return e?n===i||n===o:r?n===2*i||n===2*o:t instanceof a}const b=e.bits2int||function(t){const r=S(t),n=8*t.length-e.nBitLength;return n>0?r>>BigInt(n):r},m=e.bits2int_modN||function(t){return s(b(t))},x=N(e.nBitLength);function E(t){if("bigint"!=typeof t)throw new Error("bigint expected");if(!(ut<=t&&t<x))throw new Error(`bigint expected < 2^${e.nBitLength}`);return $(t,e.nByteLength)}function v(t,n,i=B){if(["recovered","canonical"].some((t=>t in i)))throw new Error("sign() legacy options not supported");const{hash:o,randomBytes:u}=e;let{lowS:d,prehash:g,extraEntropy:p}=i;null==d&&(d=!0),t=H("msgHash",t),g&&(t=H("prehashed msgHash",o(t)));const y=m(t),x=c(n),v=[E(x),E(y)];if(null!=p){const t=!0===p?u(r.BYTES):p;v.push(H("extraEntropy",t))}const A=O(...v),_=y;return{seed:A,k2sig:function(t){const e=b(t);if(!h(e))return;const r=f(e),n=a.BASE.multiply(e).toAffine(),i=s(n.x);if(i===ut)return;const o=s(r*s(_+i*x));if(o===ut)return;let c=(n.x===i?0:2)|Number(n.y&ht),u=o;return d&&l(o)&&(u=function(t){return l(t)?s(-t):t}(o),c^=1),new w(i,u,c)}}}const B={lowS:e.lowS,prehash:!1},_={lowS:e.lowS,prehash:!1};return a.BASE._setWindowSize(8),{CURVE:e,getPublicKey:function(t,e=!0){return a.fromPrivateKey(t).toRawBytes(e)},getSharedSecret:function(t,e,r=!0){if(y(t))throw new Error("first arg must be private key");if(!y(e))throw new Error("second arg must be public key");return a.fromHex(e).multiply(c(t)).toRawBytes(r)},sign:function(t,r,n=B){const{seed:i,k2sig:o}=v(t,r,n),s=e;return j(s.hash.outputLen,s.nByteLength,s.hmac)(i,o)},verify:function(t,r,n,i=_){const o=t;if(r=H("msgHash",r),n=H("publicKey",n),"strict"in i)throw new Error("options.strict was renamed to lowS");const{lowS:c,prehash:u}=i;let h,d;try{if("string"==typeof o||o instanceof Uint8Array)try{h=w.fromDER(o)}catch(t){if(!(t instanceof ct.Err))throw t;h=w.fromCompact(o)}else{if("object"!=typeof o||"bigint"!=typeof o.r||"bigint"!=typeof o.s)throw new Error("PARSE");{const{r:t,s:e}=o;h=new w(t,e)}}d=a.fromHex(n)}catch(t){if("PARSE"===t.message)throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(c&&h.hasHighS())return!1;u&&(r=e.hash(r));const{r:l,s:g}=h,p=m(r),y=f(g),b=s(p*y),x=s(l*y),E=a.BASE.multiplyAndAddUnsafe(d,b,x)?.toAffine();return!!E&&s(E.x)===l},ProjectivePoint:a,Signature:w,utils:p}}BigInt(4);class wt extends c{constructor(t,r){super(),this.finished=!1,this.destroyed=!1,function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");e(t.outputLen),e(t.blockLen)}(t);const n=a(r);if(this.iHash=t.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const i=this.blockLen,o=new Uint8Array(i);o.set(n.length>i?t.create().update(n).digest():n);for(let t=0;t<o.length;t++)o[t]^=54;this.iHash.update(o),this.oHash=t.create();for(let t=0;t<o.length;t++)o[t]^=106;this.oHash.update(o),o.fill(0)}update(t){return n(this),this.iHash.update(t),this}digestInto(t){n(this),r(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:r,finished:n,destroyed:i,blockLen:o,outputLen:s}=this;return t.finished=n,t.destroyed=i,t.blockLen=o,t.outputLen=s,t.oHash=e._cloneInto(t.oHash),t.iHash=r._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const pt=(t,e,r)=>new wt(t,e).update(r).digest();
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
function yt(t){return{hash:t,hmac:(e,...r)=>pt(t,e,function(...t){const e=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));let r=0;return t.forEach((t=>{if(!o(t))throw new Error("Uint8Array expected");e.set(t,r),r+=t.length})),e}(...r)),randomBytes:h}}pt.create=(t,e)=>new wt(t,e);
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const bt=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),mt=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),xt=BigInt(1),Et=BigInt(2),vt=(t,e)=>(t+e/Et)/e;function Bt(t){const e=bt,r=BigInt(3),n=BigInt(6),i=BigInt(11),o=BigInt(22),s=BigInt(23),f=BigInt(44),a=BigInt(88),c=t*t*t%e,u=c*c*t%e,h=M(u,r,e)*u%e,d=M(h,r,e)*u%e,l=M(d,Et,e)*c%e,g=M(l,i,e)*l%e,w=M(g,o,e)*g%e,p=M(w,f,e)*w%e,y=M(p,a,e)*p%e,b=M(y,f,e)*w%e,m=M(b,r,e)*u%e,x=M(m,s,e)*g%e,E=M(x,n,e)*c%e,v=M(E,Et,e);if(!At.eql(At.sqr(v),t))throw new Error("Cannot find square root");return v}const At=et(bt,void 0,void 0,{sqrt:Bt}),_t=function(t,e){const r=e=>gt({...t,...yt(e)});return Object.freeze({...r(e),create:r})}({a:BigInt(0),b:BigInt(7),Fp:At,n:mt,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{const e=mt,r=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-xt*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),o=r,s=BigInt("0x100000000000000000000000000000000"),f=vt(o*t,e),a=vt(-n*t,e);let c=W(t-f*r-a*i,e),u=W(-f*n-a*o,e);const h=c>s,d=u>s;if(h&&(c=e-c),d&&(u=e-u),c>s||u>s)throw new Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:h,k1:c,k2neg:d,k2:u}}}},b),It=BigInt(0),Ut=t=>"bigint"==typeof t&&It<t&&t<bt,St=t=>"bigint"==typeof t&&It<t&&t<mt,Lt={};function $t(t,...e){let r=Lt[t];if(void 0===r){const e=b(Uint8Array.from(t,(t=>t.charCodeAt(0))));r=O(e,e),Lt[t]=r}return b(O(r,...e))}const kt=t=>t.toRawBytes(!0).slice(1),Ht=t=>$(t,32),Ot=t=>W(t,bt),Nt=t=>W(t,mt),Rt=_t.ProjectivePoint,Tt=(t,e,r)=>Rt.BASE.multiplyAndAddUnsafe(t,e,r);function jt(t){let e=_t.utils.normPrivateKeyToScalar(t),r=Rt.fromPrivateKey(e);return{scalar:r.hasEvenY()?e:Nt(-e),bytes:kt(r)}}function Ct(t){if(!Ut(t))throw new Error("bad x: need 0 < x < p");const e=Ot(t*t);let r=Bt(Ot(e*t+BigInt(7)));r%Et!==It&&(r=Ot(-r));const n=new Rt(t,r,xt);return n.assertValidity(),n}function zt(...t){return Nt(S($t("BIP0340/challenge",...t)))}function Pt(t){return jt(t).bytes}function qt(t,e,r=h(32)){const n=H("message",t),{bytes:i,scalar:o}=jt(e),s=H("auxRand",r,32),f=Ht(o^S($t("BIP0340/aux",s))),a=$t("BIP0340/nonce",f,i,n),c=Nt(S(a));if(c===It)throw new Error("sign failed: k is zero");const{bytes:u,scalar:d}=jt(c),l=zt(u,i,n),g=new Uint8Array(64);if(g.set(u,0),g.set(Ht(Nt(d+l*o)),32),!Ft(g,n,i))throw new Error("sign: Invalid signature produced");return g}function Ft(t,e,r){const n=H("signature",t,64),i=H("message",e),o=H("publicKey",r,32);try{const t=Ct(S(o)),e=S(n.subarray(0,32));if(!Ut(e))return!1;const r=S(n.subarray(32,64));if(!St(r))return!1;const s=zt(Ht(e),kt(t),i),f=Tt(t,r,Nt(-s));return!(!f||!f.hasEvenY()||f.toAffine().x!==e)}catch(t){return!1}}const Vt=(()=>({getPublicKey:Pt,sign:qt,verify:Ft,utils:{randomPrivateKey:_t.utils.randomPrivateKey,lift_x:Ct,pointToBytes:kt,numberToBytesBE:$,bytesToNumberBE:S,taggedHash:$t,mod:W}}))(),Dt={throws:!1,xonly:!0};function Gt(t={}){return{...Dt,...t}}function Zt(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function Yt(t,e){!function(t,...e){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new Error(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}(t);const r=e.outputLen;if(t.length<r)throw new Error(`digestInto() expects output buffer of length at least ${r}`)}
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Wt=t=>t instanceof Uint8Array,Kt=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),Mt=(t,e)=>t<<32-e|t>>>e;if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function Jt(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}(t)),!Wt(t))throw new Error("expected Uint8Array, got "+typeof t);return t}class Xt{clone(){return this._cloneInto()}}function Qt(t){const e=e=>t().update(Jt(e)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}class te extends Xt{constructor(t,e,r,n){super(),this.blockLen=t,this.outputLen=e,this.padOffset=r,this.isLE=n,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=Kt(this.buffer)}update(t){Zt(this);const{view:e,buffer:r,blockLen:n}=this,i=(t=Jt(t)).length;for(let o=0;o<i;){const s=Math.min(n-this.pos,i-o);if(s!==n)r.set(t.subarray(o,o+s),this.pos),this.pos+=s,o+=s,this.pos===n&&(this.process(e,0),this.pos=0);else{const e=Kt(t);for(;n<=i-o;o+=n)this.process(e,o)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){Zt(this),Yt(t,this),this.finished=!0;const{buffer:e,view:r,blockLen:n,isLE:i}=this;let{pos:o}=this;e[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>n-o&&(this.process(r,0),o=0);for(let t=o;t<n;t++)e[t]=0;!function(t,e,r,n){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,r,n);const i=BigInt(32),o=BigInt(4294967295),s=Number(r>>i&o),f=Number(r&o),a=n?4:0,c=n?0:4;t.setUint32(e+a,s,n),t.setUint32(e+c,f,n)}(r,n-8,BigInt(8*this.length),i),this.process(r,0);const s=Kt(t),f=this.outputLen;if(f%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const a=f/4,c=this.get();if(a>c.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<a;t++)s.setUint32(4*t,c[t],i)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const r=t.slice(0,e);return this.destroy(),r}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:r,length:n,finished:i,destroyed:o,pos:s}=this;return t.length=n,t.pos=s,t.finished=i,t.destroyed=o,n%e&&t.buffer.set(r),t}}const ee=(t,e,r)=>t&e^t&r^e&r,re=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),ne=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),ie=new Uint32Array(64);class oe extends te{constructor(){super(64,32,8,!1),this.A=0|ne[0],this.B=0|ne[1],this.C=0|ne[2],this.D=0|ne[3],this.E=0|ne[4],this.F=0|ne[5],this.G=0|ne[6],this.H=0|ne[7]}get(){const{A:t,B:e,C:r,D:n,E:i,F:o,G:s,H:f}=this;return[t,e,r,n,i,o,s,f]}set(t,e,r,n,i,o,s,f){this.A=0|t,this.B=0|e,this.C=0|r,this.D=0|n,this.E=0|i,this.F=0|o,this.G=0|s,this.H=0|f}process(t,e){for(let r=0;r<16;r++,e+=4)ie[r]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=ie[t-15],r=ie[t-2],n=Mt(e,7)^Mt(e,18)^e>>>3,i=Mt(r,17)^Mt(r,19)^r>>>10;ie[t]=i+ie[t-7]+n+ie[t-16]|0}let{A:r,B:n,C:i,D:o,E:s,F:f,G:a,H:c}=this;for(let t=0;t<64;t++){const e=c+(Mt(s,6)^Mt(s,11)^Mt(s,25))+((u=s)&f^~u&a)+re[t]+ie[t]|0,h=(Mt(r,2)^Mt(r,13)^Mt(r,22))+ee(r,n,i)|0;c=a,a=f,f=s,s=o+e|0,o=i,i=n,n=r,r=e+h|0}var u;r=r+this.A|0,n=n+this.B|0,i=i+this.C|0,o=o+this.D|0,s=s+this.E|0,f=f+this.F|0,a=a+this.G|0,c=c+this.H|0,this.set(r,n,i,o,s,f,a,c)}roundClean(){ie.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const se=Qt((()=>new oe));
/*! scure-base - MIT License (c) 2022 Paul Miller (paulmillr.com) */function fe(...t){const e=(t,e)=>r=>t(e(r));return{encode:Array.from(t).reverse().reduce(((t,r)=>t?e(t,r.encode):r.encode),void 0),decode:t.reduce(((t,r)=>t?e(t,r.decode):r.decode),void 0)}}function ae(t){return{encode:e=>{if(!Array.isArray(e)||e.length&&"number"!=typeof e[0])throw new Error("alphabet.encode input should be an array of numbers");return e.map((e=>{if(e<0||e>=t.length)throw new Error(`Digit index outside alphabet: ${e} (alphabet: ${t.length})`);return t[e]}))},decode:e=>{if(!Array.isArray(e)||e.length&&"string"!=typeof e[0])throw new Error("alphabet.decode input should be array of strings");return e.map((e=>{if("string"!=typeof e)throw new Error(`alphabet.decode: not string element=${e}`);const r=t.indexOf(e);if(-1===r)throw new Error(`Unknown letter: "${e}". Allowed: ${t}`);return r}))}}}function ce(t=""){if("string"!=typeof t)throw new Error("join separator should be string");return{encode:e=>{if(!Array.isArray(e)||e.length&&"string"!=typeof e[0])throw new Error("join.encode input should be array of strings");for(let t of e)if("string"!=typeof t)throw new Error(`join.encode: non-string input=${t}`);return e.join(t)},decode:e=>{if("string"!=typeof e)throw new Error("join.decode input should be string");return e.split(t)}}}function ue(t,e="="){if("string"!=typeof e)throw new Error("padding chr should be string");return{encode(r){if(!Array.isArray(r)||r.length&&"string"!=typeof r[0])throw new Error("padding.encode input should be array of strings");for(let t of r)if("string"!=typeof t)throw new Error(`padding.encode: non-string input=${t}`);for(;r.length*t%8;)r.push(e);return r},decode(r){if(!Array.isArray(r)||r.length&&"string"!=typeof r[0])throw new Error("padding.encode input should be array of strings");for(let t of r)if("string"!=typeof t)throw new Error(`padding.decode: non-string input=${t}`);let n=r.length;if(n*t%8)throw new Error("Invalid padding: string should have whole number of bytes");for(;n>0&&r[n-1]===e;n--)if(!((n-1)*t%8))throw new Error("Invalid padding: string has too much padding");return r.slice(0,n)}}}function he(t,e,r){if(e<2)throw new Error(`convertRadix: wrong from=${e}, base cannot be less than 2`);if(r<2)throw new Error(`convertRadix: wrong to=${r}, base cannot be less than 2`);if(!Array.isArray(t))throw new Error("convertRadix: data should be array");if(!t.length)return[];let n=0;const i=[],o=Array.from(t);for(o.forEach((t=>{if(t<0||t>=e)throw new Error(`Wrong integer: ${t}`)}));;){let t=0,s=!0;for(let i=n;i<o.length;i++){const f=o[i],a=e*t+f;if(!Number.isSafeInteger(a)||e*t/e!==t||a-f!=e*t)throw new Error("convertRadix: carry overflow");t=a%r;const c=Math.floor(a/r);if(o[i]=c,!Number.isSafeInteger(c)||c*r+t!==a)throw new Error("convertRadix: carry overflow");s&&(c?s=!1:n=i)}if(i.push(t),s)break}for(let e=0;e<t.length-1&&0===t[e];e++)i.push(0);return i.reverse()}const de=(t,e)=>e?de(e,t%e):t,le=(t,e)=>t+(e-de(t,e));function ge(t,e,r,n){if(!Array.isArray(t))throw new Error("convertRadix2: data should be array");if(e<=0||e>32)throw new Error(`convertRadix2: wrong from=${e}`);if(r<=0||r>32)throw new Error(`convertRadix2: wrong to=${r}`);if(le(e,r)>32)throw new Error(`convertRadix2: carry overflow from=${e} to=${r} carryBits=${le(e,r)}`);let i=0,o=0;const s=2**r-1,f=[];for(const n of t){if(n>=2**e)throw new Error(`convertRadix2: invalid data word=${n} from=${e}`);if(i=i<<e|n,o+e>32)throw new Error(`convertRadix2: carry overflow pos=${o} from=${e}`);for(o+=e;o>=r;o-=r)f.push((i>>o-r&s)>>>0);i&=2**o-1}if(i=i<<r-o&s,!n&&o>=e)throw new Error("Excess padding");if(!n&&i)throw new Error(`Non-zero padding: ${i}`);return n&&o>0&&f.push(i>>>0),f}function we(t,e=!1){if(t<=0||t>32)throw new Error("radix2: bits should be in (0..32]");if(le(8,t)>32||le(t,8)>32)throw new Error("radix2: carry overflow");return{encode:r=>{if(!(r instanceof Uint8Array))throw new Error("radix2.encode input should be Uint8Array");return ge(Array.from(r),8,t,!e)},decode:r=>{if(!Array.isArray(r)||r.length&&"number"!=typeof r[0])throw new Error("radix2.decode input should be array of strings");return Uint8Array.from(ge(r,t,8,e))}}}function pe(t){if("function"!=typeof t)throw new Error("unsafeWrapper fn should be function");return function(...e){try{return t.apply(null,e)}catch(t){}}}const ye=fe(we(6),ae("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"),ue(6),ce("")),be=fe(we(6),ae("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"),ue(6),ce("")),me=t=>{return fe((e=58,{encode:t=>{if(!(t instanceof Uint8Array))throw new Error("radix.encode input should be Uint8Array");return he(Array.from(t),256,e)},decode:t=>{if(!Array.isArray(t)||t.length&&"number"!=typeof t[0])throw new Error("radix.decode input should be array of strings");return Uint8Array.from(he(t,e,256))}}),ae(t),ce(""));var e},xe=me("123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"),Ee=t=>fe(function(t,e){if("function"!=typeof e)throw new Error("checksum fn should be function");return{encode(r){if(!(r instanceof Uint8Array))throw new Error("checksum.encode: input should be Uint8Array");const n=e(r).slice(0,t),i=new Uint8Array(r.length+t);return i.set(r),i.set(n,r.length),i},decode(r){if(!(r instanceof Uint8Array))throw new Error("checksum.decode: input should be Uint8Array");const n=r.slice(0,-t),i=e(n).slice(0,t),o=r.slice(-t);for(let e=0;e<t;e++)if(i[e]!==o[e])throw new Error("Invalid checksum");return n}}}(4,(e=>t(t(e)))),xe),ve=fe(ae("qpzry9x8gf2tvdw0s3jn54khce6mua7l"),ce("")),Be=[996825010,642813549,513874426,1027748829,705979059];function Ae(t){const e=t>>25;let r=(33554431&t)<<5;for(let t=0;t<Be.length;t++)1==(e>>t&1)&&(r^=Be[t]);return r}function _e(t,e,r=1){const n=t.length;let i=1;for(let e=0;e<n;e++){const r=t.charCodeAt(e);if(r<33||r>126)throw new Error(`Invalid prefix (${t})`);i=Ae(i)^r>>5}i=Ae(i);for(let e=0;e<n;e++)i=Ae(i)^31&t.charCodeAt(e);for(let t of e)i=Ae(i)^t;for(let t=0;t<6;t++)i=Ae(i);return i^=r,ve.encode(ge([i%2**30],30,5,!1))}function Ie(t){const e="bech32"===t?1:734539939,r=we(5),n=r.decode,i=r.encode,o=pe(n);function s(t,r=90){if("string"!=typeof t)throw new Error("bech32.decode input should be string, not "+typeof t);if(t.length<8||!1!==r&&t.length>r)throw new TypeError(`Wrong string length: ${t.length} (${t}). Expected (8..${r})`);const n=t.toLowerCase();if(t!==n&&t!==t.toUpperCase())throw new Error("String must be lowercase or uppercase");const i=(t=n).lastIndexOf("1");if(0===i||-1===i)throw new Error('Letter "1" must be present between prefix and data only');const o=t.slice(0,i),s=t.slice(i+1);if(s.length<6)throw new Error("Data must be at least 6 characters long");const f=ve.decode(s).slice(0,-6),a=_e(o,f,e);if(!s.endsWith(a))throw new Error(`Invalid checksum in ${t}: expected "${a}"`);return{prefix:o,words:f}}return{encode:function(t,r,n=90){if("string"!=typeof t)throw new Error("bech32.encode prefix should be string, not "+typeof t);if(!Array.isArray(r)||r.length&&"number"!=typeof r[0])throw new Error("bech32.encode words should be array of numbers, not "+typeof r);const i=t.length+7+r.length;if(!1!==n&&i>n)throw new TypeError(`Length ${i} exceeds limit ${n}`);const o=t.toLowerCase(),s=_e(o,r,e);return`${o}1${ve.encode(r)}${s}`},decode:s,decodeToBytes:function(t){const{prefix:e,words:r}=s(t,!1);return{prefix:e,words:r,bytes:n(r)}},decodeUnsafe:pe(s),fromWords:n,fromWordsUnsafe:o,toWords:i}}const Ue=Ie("bech32"),Se=Ie("bech32m"),Le={b58chk:{encode:t=>Ee(se).encode(t),decode:t=>Ee(se).decode(t)},base64:{encode:t=>ye.encode(t),decode:t=>ye.decode(t)},b64url:{encode:t=>be.encode(t),decode:t=>be.decode(t)},bech32:{to_words:Ue.toWords,to_bytes:Ue.fromWords,encode:(t,e,r=!1)=>Ue.encode(t,e,r),decode:(t,e=!1)=>{const{prefix:r,words:n}=Ue.decode(t,e);return{prefix:r,words:n}}},bech32m:{to_words:Se.toWords,to_bytes:Se.fromWords,encode:(t,e,r=!1)=>Se.encode(t,e,r),decode:(t,e=!1)=>{const{prefix:r,words:n}=Se.decode(t,e);return{prefix:r,words:n}}}};function $e(t){if(t>Number.MAX_SAFE_INTEGER)throw new TypeError("Number exceeds safe bounds!")}function ke(t,e){if(t!==e)throw new TypeError(`Bech32 prefix does not match: ${t} !== ${e}`)}const He=BigInt(0),Oe=BigInt(255),Ne=BigInt(256);function Re(t,e,r="be"){void 0===e&&(e=function(t){if(t<=0xffn)return 1;if(t<=0xffffn)return 2;if(t<=0xffffffffn)return 4;if(t<=0xffffffffffffffffn)return 8;if(t<=0xffffffffffffffffffffffffffffffffn)return 16;if(t<=0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn)return 32;throw new TypeError("Must specify a fixed buffer size for bigints greater than 32 bytes.")}(t));const n="le"===r,i=new ArrayBuffer(e),o=new DataView(i);let s=n?0:e-1;for(;t>He;){const e=t&Oe,r=Number(e);n?o.setUint8(s++,r):o.setUint8(s--,r),t=(t-e)/Ne}return new Uint8Array(i)}function Te(t,e,r="be"){void 0===e&&(e=function(t){if(t<=255)return 1;if(t<=65535)return 2;if(t<=4294967295)return 4;throw new TypeError("Numbers larger than 4 bytes must specify a fixed size!")}(t));const n="le"===r,i=new ArrayBuffer(e),o=new DataView(i);let s=n?0:e-1;for(;t>0;){const e=255&t;n?o.setUint8(s++,t):o.setUint8(s--,t),t=(t-e)/256}return new Uint8Array(i)}const je=new TextEncoder,Ce=new TextDecoder;function ze(t){return je.encode(t)}function Pe(t){return Ce.decode(t)}function qe(t,e){!function(t){if(null!==t.match(/[^a-fA-f0-9]/))throw new TypeError("Invalid characters in hex string: "+t);if(t.length%2!=0)throw new Error(`Length of hex string is invalid: ${t.length}`)}(t);const r=t.length/2;if(void 0===e&&(e=r),r>e)throw new TypeError(`Hex string is larger than array size: ${r} > ${e}`);return e}const{getRandomValues:Fe}=crypto??globalThis.crypto??window.crypto;function Ve(t){return null===t.match(/[^a-fA-f0-9]/)&&t.length%2==0}function De(t){return!("string"!=typeof t||!Ve(t))||("number"==typeof t||"bigint"==typeof t||t instanceof Uint8Array||!(!Array.isArray(t)||!t.every((t=>"number"==typeof t))))}function Ge(t,e,r="be"){void 0===e&&(e=t.length),function(t,e){if(t.length>e)throw new TypeError(`Data is larger than array size: ${t.length} > ${e}`)}(t,e);const n=new Uint8Array(e).fill(0),i="be"===r?0:e-t.length;return n.set(t,i),n}function Ze(t){let e,r=0;const n=t.reduce(((t,e)=>t+e.length),0),i=new Uint8Array(n);for(e=0;e<t.length;e++){const n=t[e];i.set(n,r),r+=n.length}return i}function Ye(t,e){return"bigint"==typeof e?`${e}n`:e}function We(t,e){return"string"==typeof e&&/n$/.test(e)?BigInt(e.slice(0,-1)):e}function Ke(t,e,r){if(t instanceof ArrayBuffer)return new Uint8Array(t);if(t instanceof Uint8Array)return Ge(t,e,r);if(Array.isArray(t)){return Ze(t.map((t=>Ke(t,e,r))))}if("string"==typeof t)return function(t,e,r="le"){e=qe(t,e);const n="le"===r,i=new ArrayBuffer(e),o=new DataView(i);let s=n?0:e-1;for(let e=0;e<t.length;e+=2){const r=t.substring(e,e+2),i=parseInt(r,16);n?o.setUint8(s++,i):o.setUint8(s--,i)}return new Uint8Array(i)}(t,e,r);if("bigint"==typeof t)return Re(t,e,r);if("number"==typeof t)return Te(t,e,r);if("boolean"==typeof t)return Uint8Array.of(t?1:0);throw new TypeError("Unsupported format:"+String(typeof t))}class Me extends Uint8Array{static{this.num=Je}static{this.big=Qe}static{this.bin=Xe}static{this.raw=tr}static{this.str=er}static{this.hex=rr}static{this.bytes=hr}static{this.json=nr}static{this.base64=ir}static{this.b64url=or}static{this.bech32=sr}static{this.bech32m=fr}static{this.b58chk=ar}static{this.encode=ze}static{this.decode=Pe}static{this.parse=cr}static{this.is_bytes=De}static{this.is_hex=Ve}static random(t=32){const e=function(t=32){if("function"==typeof Fe)return crypto.getRandomValues(new Uint8Array(t));throw new Error("Crypto module missing getRandomValues!")}(t);return new Me(e,t)}static now(t=4){const e=Math.floor(Date.now()/1e3);return new Me(e,t)}constructor(t,e,r){if(t instanceof Me&&void 0===e)return t;super(Ke(t,e,r))}get arr(){return[...this]}get num(){return this.to_num()}get big(){return this.to_big()}get str(){return this.to_str()}get hex(){return this.to_hex()}get raw(){return new Uint8Array(this)}get bin(){return this.to_bin()}get b58chk(){return this.to_b58chk()}get base64(){return this.to_base64()}get b64url(){return this.to_b64url()}get digest(){return this.to_hash()}get id(){return this.to_hash().hex}get stream(){return new ur(this)}to_num(t="be"){return function(t){let e=0;for(let r=t.length-1;r>=0;r--)e=256*e+t[r],$e(e);return e}("be"===t?this.reverse():this)}to_big(t="be"){return function(t){let e=BigInt(0);for(let r=t.length-1;r>=0;r--)e=e*Ne+BigInt(t[r]);return BigInt(e)}("be"===t?this.reverse():this)}to_bin(){return function(t){const e=new Array(8*t.length);let r=0;for(const n of t){if(n>255)throw new Error(`Invalid byte value: ${n}. Byte values must be between 0 and 255.`);for(let t=7;t>=0;t--,r++)e[r]=n>>t&1}return e.join("")}(this)}to_hash(){const t=se(this);return new Me(t)}to_json(t){void 0===t&&(t=We);const e=Pe(this);return JSON.parse(e,t)}to_bech32(t,e){const{encode:r,to_words:n}=Le.bech32;return r(t,n(this),e)}to_bech32m(t,e){const{encode:r,to_words:n}=Le.bech32m;return r(t,n(this),e)}to_str(){return Pe(this)}to_hex(){return function(t){let e="";for(let r=0;r<t.length;r++)e+=t[r].toString(16).padStart(2,"0");return e}(this)}to_bytes(){return new Uint8Array(this)}to_b58chk(){return Le.b58chk.encode(this)}to_base64(){return Le.base64.encode(this)}to_b64url(){return Le.b64url.encode(this)}append(t){return Me.join([this,Me.bytes(t)])}prepend(t){return Me.join([Me.bytes(t),this])}reverse(){const t=new Uint8Array(this).reverse();return new Me(t)}slice(t,e){const r=new Uint8Array(this).slice(t,e);return new Me(r)}set(t,e){this.set(t,e)}subarray(t,e){const r=new Uint8Array(this).subarray(t,e);return new Me(r)}write(t,e){const r=Me.bytes(t);this.set(r,e)}add_varint(t){const e=Me.calc_varint(this.length,t);return Me.join([e,this])}static from(t){return new Me(Uint8Array.from(t))}static of(...t){return new Me(Uint8Array.of(...t))}static join(t){const e=Ze(t.map((t=>Me.bytes(t))));return new Me(e)}static sort(t,e){const r=t.map((t=>hr(t,e).hex));return r.sort(),r.map((t=>Me.hex(t,e)))}static calc_varint(t,e){if(t<253)return Me.num(t,1);if(t<65536)return Me.of(253,...Me.num(t,2,e));if(t<4294967296)return Me.of(254,...Me.num(t,4,e));if(BigInt(t)<0x10000000000000000n)return Me.of(255,...Me.num(t,8,e));throw new Error(`Value is too large: ${t}`)}}function Je(t,e,r){return new Me(t,e,r)}function Xe(t,e,r){return new Me(function(t){const e=t.split("").map(Number);if(e.length%8!=0)throw new Error(`Binary array is invalid length: ${t.length}`);const r=new Uint8Array(e.length/8);for(let t=0,n=0;t<e.length;t+=8,n++){let i=0;for(let r=0;r<8;r++)i|=e[t+r]<<7-r;r[n]=i}return r}(t),e,r)}function Qe(t,e,r){return new Me(t,e,r)}function tr(t,e,r){return new Me(t,e,r)}function er(t,e,r){return new Me(ze(t),e,r)}function rr(t,e,r){return new Me(t,e,r)}function nr(t,e){void 0===e&&(e=Ye);const r=JSON.stringify(t,e);return new Me(ze(r))}function ir(t){return new Me(Le.base64.decode(t))}function or(t){return new Me(Le.b64url.decode(t))}function sr(t,e,r){const{decode:n,to_bytes:i}=Le.bech32,{prefix:o,words:s}=n(t,e),f=i(s);return"string"==typeof r&&ke(o,r),new Me(f)}function fr(t,e,r){const{decode:n,to_bytes:i}=Le.bech32m,{prefix:o,words:s}=n(t,e),f=i(s);return"string"==typeof r&&ke(o,r),new Me(f)}function ar(t){return new Me(Le.b58chk.decode(t))}function cr(t,e,r){const n=function(t,e,r){const n=t.length,i=r/e;if(r%e!=0)throw new TypeError(`Invalid parameters: ${r} % ${e} !== 0`);if(n!==r)throw new TypeError(`Invalid data stream: ${n} !== ${r}`);if(n%e!=0)throw new TypeError(`Invalid data stream: ${n} % ${e} !== 0`);const o=new Array(i);for(let r=0;r<i;r++){const n=r*e;o[r]=t.subarray(n,n+e)}return o}(Ke(t),e,r);return n.map((t=>Me.bytes(t)))}class ur{constructor(t){this.data=Me.bytes(t),this.size=this.data.length}peek(t){if(t>this.size)throw new Error(`Size greater than stream: ${t} > ${this.size}`);return new Me(this.data.slice(0,t))}read(t){const e=this.peek(t);return this.data=this.data.slice(t),this.size=this.data.length,e}read_varint(t){const e=this.read(1).num;switch(!0){case e>=0&&e<253:return e;case 253===e:return this.read(2).to_num(t);case 254===e:return this.read(4).to_num(t);case 255===e:return this.read(8).to_num(t);default:throw new Error(`Varint is out of range: ${e}`)}}}function hr(t,e,r){return new Me(t,e,r)}const dr=_t.CURVE,lr=dr.n,gr=dr.p,wr={x:dr.Gx,y:dr.Gy},pr=BigInt(0),yr=BigInt(1),br=BigInt(2),mr=BigInt(3),xr=BigInt(4);var Er=Object.freeze({__proto__:null,_0n:pr,_1n:yr,_2n:br,_3n:mr,_4n:xr,_G:wr,_N:lr,_P:gr});const vr=t=>W(t,lr);var Br=Object.freeze({__proto__:null,in_field:t=>"bigint"==typeof t&&pr<t&&t<lr,invert:J,mod:W,modN:vr,modP:t=>W(t,gr),mod_bytes:function(t){const e=Me.bytes(t).big;return Me.big(vr(e),32)},on_curve:t=>"bigint"==typeof t&&pr<t&&t<gr,pow:K,pow2:M,powN:(t,e)=>K(t,e,lr)});function Ar(t,e){if(!1===t)throw new Error(e??"Assertion failed!")}function _r(t,e=!1){if(!e)return!1;throw new Error(t)}function Ir(t,e,r){const n=Me.bytes(t);return n.length===e||_r(`Invalid byte size: ${n.hex} !== ${e}`,r)}function Ur(t,e){return"bigint"==typeof t&&pr<t&&t<gr||_r("x value is not on the curve!",e),!0}function Sr(t,e){return"bigint"==typeof t&&pr<t&&t<lr||_r("x value is not in the field!",e),!0}function Lr(t,e){if(void 0===e){if(!t.startsWith("m"))throw new Error("You need to specify a chain-code for a non-root path.")}else if(32!==Me.bytes(e).length)throw new Error("Chain code must be 32 bytes!")}function $r(t){if(""!==t&&null===t.match(/^(m)?(\/)?(\w+'?\/)*\w+'?$/))throw new Error("Provided path string is invalid: "+t)}function kr(t){if(t>2147483648)throw new TypeError("Index value must not exceed 31 bits.")}var Hr=Object.freeze({__proto__:null,exists:function(t){if(void 0===t)throw new TypeError("Input is undefined!");if(null===t)throw new TypeError("Input is null!")},fail:_r,in_field:Sr,ok:Ar,on_curve:Ur,size:Ir,valid_chain:Lr,valid_derive_state:function(t,e){if(t&&!e)throw new Error("Cannot derive hardedened paths when is_private is false!")},valid_hash:function(t){if(null===t.match(/^[0-9a-fA-F]{64}$/))throw new Error("Provided hash string is invalid: "+t)},valid_index:kr,valid_path:$r,valid_pubkey:function(t){if(33!==Me.bytes(t).length)throw new TypeError("Index value must not exceed 31 bits.")}});const Or=et(lr,32,!0),Nr=_t.ProjectivePoint,Rr=Or;class Tr extends Uint8Array{static{this.N=lr}static add(t){return t.map((t=>Tr.mod(t))).reduce(((t,e)=>t.add(e)))}static mod(t){return new Tr(t)}static mul(t){return t.map((t=>Tr.mod(t))).reduce(((t,e)=>t.mul(e)))}static is_valid(t,e){return Sr(Me.bytes(t,32).big,e)}constructor(t){const e=vr(function(t){if(t instanceof Tr)return t.big;if(t instanceof jr)return t.x.big;if(t instanceof Uint8Array)return Me.raw(t).big;if("string"==typeof t)return Me.hex(t).big;if("number"==typeof t)return Me.num(t).big;if("bigint"==typeof t)return BigInt(t);throw TypeError("Invalid input type:"+typeof t)}(t));Tr.is_valid(e,!0),super(Me.big(e,32),32)}get buff(){return new Me(this)}get raw(){return this.buff.raw}get big(){return this.buff.big}get hex(){return this.buff.hex}get point(){return this.generate()}get hasOddY(){return this.point.hasOddY}get negated(){return this.hasOddY?this.negate():this}gt(t){return new Tr(t).big>this.big}lt(t){return new Tr(t).big<this.big}eq(t){return new Tr(t).big===this.big}ne(t){return new Tr(t).big!==this.big}add(t){const e=Tr.mod(t),r=Rr.add(this.big,e.big);return new Tr(r)}sub(t){const e=Tr.mod(t),r=Rr.sub(this.big,e.big);return new Tr(r)}mul(t){const e=Tr.mod(t),r=Rr.mul(this.big,e.big);return new Tr(r)}pow(t){const e=Tr.mod(t),r=Rr.pow(this.big,e.big);return new Tr(r)}div(t){const e=Tr.mod(t),r=Rr.div(this.big,e.big);return new Tr(r)}negate(){return new Tr(Tr.N-this.big)}generate(){const t=_t.ProjectivePoint.BASE.multiply(this.big);return jr.import(t)}}class jr{static{this.P=gr}static{this.G=new jr(wr.x,wr.y)}static{this.curve=_t.CURVE}static{this.base=_t.ProjectivePoint.BASE}static from_x(t,e=!1){let r=function(t){if(t instanceof Tr)return t.point.buff;if(t instanceof jr)return t.buff;if(t instanceof Uint8Array||"string"==typeof t)return Me.bytes(t);if("number"==typeof t||"bigint"==typeof t)return Me.bytes(t,32);throw new TypeError("Unknown type: "+typeof t)}(t);32===r.length?r=r.prepend(2):e&&(r[0]=2),Ir(r,33);const n=Nr.fromHex(r.hex);return n.assertValidity(),new jr(n.x,n.y)}static generate(t){const e=Tr.mod(t),r=jr.base.multiply(e.big);return jr.import(r)}static{this.mul=jr.generate}static import(t){const e=t instanceof jr?{x:t.x.big,y:t.y.big}:{x:t.x,y:t.y};return new jr(e.x,e.y)}constructor(t,e){this._p=new Nr(t,e,1n),this.p.assertValidity()}get p(){return this._p}get x(){return Me.big(this.p.x,32)}get y(){return Me.big(this.p.y,32)}get buff(){return Me.raw(this.p.toRawBytes(!0))}get raw(){return this.buff.raw}get hex(){return this.buff.hex}get hasEvenY(){return this.p.hasEvenY()}get hasOddY(){return!this.p.hasEvenY()}get negated(){return this.hasOddY?this.negate():this}eq(t){const e=t instanceof jr?t:jr.from_x(t);return this.x.big===e.x.big&&this.y.big===e.y.big}add(t){return t instanceof jr?jr.import(this.p.add(t.p)):jr.import(this.p.add(jr.generate(t).p))}sub(t){return t instanceof jr?jr.import(this.p.subtract(t.p)):jr.import(this.p.subtract(jr.generate(t).p))}mul(t){return t instanceof jr?jr.import(this.p.multiply(t.x.big)):jr.import(this.p.multiply(Tr.mod(t).big))}negate(){return jr.import(this.p.negate())}}const Cr=BigInt(2**32-1),zr=BigInt(32);function Pr(t,e=!1){return e?{h:Number(t&Cr),l:Number(t>>zr&Cr)}:{h:0|Number(t>>zr&Cr),l:0|Number(t&Cr)}}var qr={fromBig:Pr,split:function(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:o,l:s}=Pr(t[i],e);[r[i],n[i]]=[o,s]}return[r,n]},toBig:(t,e)=>BigInt(t>>>0)<<zr|BigInt(e>>>0),shrSH:(t,e,r)=>t>>>r,shrSL:(t,e,r)=>t<<32-r|e>>>r,rotrSH:(t,e,r)=>t>>>r|e<<32-r,rotrSL:(t,e,r)=>t<<32-r|e>>>r,rotrBH:(t,e,r)=>t<<64-r|e>>>r-32,rotrBL:(t,e,r)=>t>>>r-32|e<<64-r,rotr32H:(t,e)=>e,rotr32L:(t,e)=>t,rotlSH:(t,e,r)=>t<<r|e>>>32-r,rotlSL:(t,e,r)=>e<<r|t>>>32-r,rotlBH:(t,e,r)=>e<<r-32|t>>>64-r,rotlBL:(t,e,r)=>t<<r-32|e>>>64-r,add:function(t,e,r,n){const i=(e>>>0)+(n>>>0);return{h:t+r+(i/2**32|0)|0,l:0|i}},add3L:(t,e,r)=>(t>>>0)+(e>>>0)+(r>>>0),add3H:(t,e,r,n)=>e+r+n+(t/2**32|0)|0,add4L:(t,e,r,n)=>(t>>>0)+(e>>>0)+(r>>>0)+(n>>>0),add4H:(t,e,r,n,i)=>e+r+n+i+(t/2**32|0)|0,add5H:(t,e,r,n,i,o)=>e+r+n+i+o+(t/2**32|0)|0,add5L:(t,e,r,n,i)=>(t>>>0)+(e>>>0)+(r>>>0)+(n>>>0)+(i>>>0)};const[Fr,Vr]=(()=>qr.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map((t=>BigInt(t)))))(),Dr=new Uint32Array(80),Gr=new Uint32Array(80);class Zr extends d{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:t,Al:e,Bh:r,Bl:n,Ch:i,Cl:o,Dh:s,Dl:f,Eh:a,El:c,Fh:u,Fl:h,Gh:d,Gl:l,Hh:g,Hl:w}=this;return[t,e,r,n,i,o,s,f,a,c,u,h,d,l,g,w]}set(t,e,r,n,i,o,s,f,a,c,u,h,d,l,g,w){this.Ah=0|t,this.Al=0|e,this.Bh=0|r,this.Bl=0|n,this.Ch=0|i,this.Cl=0|o,this.Dh=0|s,this.Dl=0|f,this.Eh=0|a,this.El=0|c,this.Fh=0|u,this.Fl=0|h,this.Gh=0|d,this.Gl=0|l,this.Hh=0|g,this.Hl=0|w}process(t,e){for(let r=0;r<16;r++,e+=4)Dr[r]=t.getUint32(e),Gr[r]=t.getUint32(e+=4);for(let t=16;t<80;t++){const e=0|Dr[t-15],r=0|Gr[t-15],n=qr.rotrSH(e,r,1)^qr.rotrSH(e,r,8)^qr.shrSH(e,r,7),i=qr.rotrSL(e,r,1)^qr.rotrSL(e,r,8)^qr.shrSL(e,r,7),o=0|Dr[t-2],s=0|Gr[t-2],f=qr.rotrSH(o,s,19)^qr.rotrBH(o,s,61)^qr.shrSH(o,s,6),a=qr.rotrSL(o,s,19)^qr.rotrBL(o,s,61)^qr.shrSL(o,s,6),c=qr.add4L(i,a,Gr[t-7],Gr[t-16]),u=qr.add4H(c,n,f,Dr[t-7],Dr[t-16]);Dr[t]=0|u,Gr[t]=0|c}let{Ah:r,Al:n,Bh:i,Bl:o,Ch:s,Cl:f,Dh:a,Dl:c,Eh:u,El:h,Fh:d,Fl:l,Gh:g,Gl:w,Hh:p,Hl:y}=this;for(let t=0;t<80;t++){const e=qr.rotrSH(u,h,14)^qr.rotrSH(u,h,18)^qr.rotrBH(u,h,41),b=qr.rotrSL(u,h,14)^qr.rotrSL(u,h,18)^qr.rotrBL(u,h,41),m=u&d^~u&g,x=h&l^~h&w,E=qr.add5L(y,b,x,Vr[t],Gr[t]),v=qr.add5H(E,p,e,m,Fr[t],Dr[t]),B=0|E,A=qr.rotrSH(r,n,28)^qr.rotrBH(r,n,34)^qr.rotrBH(r,n,39),_=qr.rotrSL(r,n,28)^qr.rotrBL(r,n,34)^qr.rotrBL(r,n,39),I=r&i^r&s^i&s,U=n&o^n&f^o&f;p=0|g,y=0|w,g=0|d,w=0|l,d=0|u,l=0|h,({h:u,l:h}=qr.add(0|a,0|c,0|v,0|B)),a=0|s,c=0|f,s=0|i,f=0|o,i=0|r,o=0|n;const S=qr.add3L(B,_,U);r=qr.add3H(S,v,A,I),n=0|S}({h:r,l:n}=qr.add(0|this.Ah,0|this.Al,0|r,0|n)),({h:i,l:o}=qr.add(0|this.Bh,0|this.Bl,0|i,0|o)),({h:s,l:f}=qr.add(0|this.Ch,0|this.Cl,0|s,0|f)),({h:a,l:c}=qr.add(0|this.Dh,0|this.Dl,0|a,0|c)),({h:u,l:h}=qr.add(0|this.Eh,0|this.El,0|u,0|h)),({h:d,l:l}=qr.add(0|this.Fh,0|this.Fl,0|d,0|l)),({h:g,l:w}=qr.add(0|this.Gh,0|this.Gl,0|g,0|w)),({h:p,l:y}=qr.add(0|this.Hh,0|this.Hl,0|p,0|y)),this.set(r,n,i,o,s,f,a,c,u,h,d,l,g,w,p,y)}roundClean(){Dr.fill(0),Gr.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const Yr=u((()=>new Zr)),Wr=new Uint8Array([7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8]),Kr=Uint8Array.from({length:16},((t,e)=>e));let Mr=[Kr],Jr=[Kr.map((t=>(9*t+5)%16))];for(let t=0;t<4;t++)for(let e of[Mr,Jr])e.push(e[t].map((t=>Wr[t])));const Xr=[[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8],[12,13,11,15,6,9,9,7,12,15,11,13,7,8,7,7],[13,15,14,11,7,7,6,8,13,14,13,12,5,5,6,9],[14,11,12,14,8,6,5,5,15,12,15,14,9,9,8,6],[15,12,13,13,9,5,8,6,14,11,12,11,8,6,5,5]].map((t=>new Uint8Array(t))),Qr=Mr.map(((t,e)=>t.map((t=>Xr[e][t])))),tn=Jr.map(((t,e)=>t.map((t=>Xr[e][t])))),en=new Uint32Array([0,1518500249,1859775393,2400959708,2840853838]),rn=new Uint32Array([1352829926,1548603684,1836072691,2053994217,0]),nn=(t,e)=>t<<e|t>>>32-e;function on(t,e,r,n){return 0===t?e^r^n:1===t?e&r|~e&n:2===t?(e|~r)^n:3===t?e&n|r&~n:e^(r|~n)}const sn=new Uint32Array(16);class fn extends d{constructor(){super(64,20,8,!0),this.h0=1732584193,this.h1=-271733879,this.h2=-1732584194,this.h3=271733878,this.h4=-1009589776}get(){const{h0:t,h1:e,h2:r,h3:n,h4:i}=this;return[t,e,r,n,i]}set(t,e,r,n,i){this.h0=0|t,this.h1=0|e,this.h2=0|r,this.h3=0|n,this.h4=0|i}process(t,e){for(let r=0;r<16;r++,e+=4)sn[r]=t.getUint32(e,!0);let r=0|this.h0,n=r,i=0|this.h1,o=i,s=0|this.h2,f=s,a=0|this.h3,c=a,u=0|this.h4,h=u;for(let t=0;t<5;t++){const e=4-t,d=en[t],l=rn[t],g=Mr[t],w=Jr[t],p=Qr[t],y=tn[t];for(let e=0;e<16;e++){const n=nn(r+on(t,i,s,a)+sn[g[e]]+d,p[e])+u|0;r=u,u=a,a=0|nn(s,10),s=i,i=n}for(let t=0;t<16;t++){const r=nn(n+on(e,o,f,c)+sn[w[t]]+l,y[t])+h|0;n=h,h=c,c=0|nn(f,10),f=o,o=r}}this.set(this.h1+s+c|0,this.h2+a+h|0,this.h3+u+n|0,this.h4+r+o|0,this.h0+i+f|0)}roundClean(){sn.fill(0)}destroy(){this.destroyed=!0,this.buffer.fill(0),this.set(0,0,0,0,0)}}const an=u((()=>new fn));function cn(t){const e=Me.bytes(t);return Me.raw(an(b(e)))}function un(t,e){const r=Me.bytes(t),n=Me.bytes(e);return Me.raw(pt(Yr,r,n))}function hn(t){const e=Me.str(t).digest;return Me.join([e,e])}function dn(t,...e){const r=hn(t);return Me.join([r,...e]).digest}var ln=Object.freeze({__proto__:null,digest:dn,hash160:cn,hash256:function(t){const e=Me.bytes(t);return Me.raw(b(b(e)))},hmac256:function(t,e){const r=Me.bytes(t),n=Me.bytes(e);return Me.raw(pt(b,r,n))},hmac512:un,ripe160:function(t){const e=Me.bytes(t);return Me.raw(Yr(e))},sha256:function(t){const e=Me.bytes(t);return Me.raw(b(e))},sha512:function(t){const e=Me.bytes(t);return Me.raw(Yr(e))},taghash:hn});function gn(t){return Me.random(t)}function wn(t){switch(typeof t){case"object":return JSON.stringify(t);case"string":return t;case"bigint":case"number":return t.toString();case"boolean":return String(t);default:throw new TypeError("Content type not supported: "+typeof t)}}var pn=Object.freeze({__proto__:null,increment_buffer:function(t){let e=t.length-1;for(;e>=0;e--)if(t[e]<255)return t.set([t[e]+1],e),t;throw TypeError("Unable to increment buffer: "+t.toString())},random:gn,stringify:wn});function yn(t,e=!1){const r=Tr.mod(t);return e?r.negated.buff:r.buff}function bn(t,e=!1){const r=Tr.mod(t).point;return e?r.x:r.buff}function mn(t,e,r){const n=yn(t,r);return[n,bn(n,e)]}function xn(t){const e=Me.bytes(t);if(32===e.length)return e;if(33===e.length)return e.slice(1,33);throw new TypeError(`Invalid key length: ${e.length}`)}function En(t,e=!1){const r=Me.bytes(t);if(32===r.length)return r.prepend(2);if(33===r.length)return e&&(r[0]=2),r;throw new TypeError(`Invalid key size: ${r.length}`)}var vn=Object.freeze({__proto__:null,convert_32:xn,convert_33:En,gen_keypair:function(t,e){return mn(gn(32),t,e)},gen_seckey:function(t){return yn(gn(32),t)},get_keypair:mn,get_pubkey:bn,get_seckey:yn,is_even_pub:function(t){const e=Me.bytes(t);switch(!0){case 32===e.length:case 33===e.length&&2===e[0]:return!0;case 33===e.length&&3===e[0]:return!1;default:throw new TypeError(`Invalid public key: ${e.hex}`)}},negate_seckey:function(t,e){const r=Tr.mod(t);return e?r.negate().buff:r.buff},tweak_pubkey:function(t,e=[],r=!1){let n=jr.from_x(t,r);for(const t of e)n=n.add(t),r&&(n=n.negated);return r?n.x:n.buff},tweak_seckey:function(t,e=[],r=!1){let n=Tr.mod(t);for(const t of e)n=n.add(t),r&&(n=n.negated);return n.buff}});function Bn(t,e){const r=jr.from_x(e),n=Tr.mod(t);return r.mul(n).buff}var An=Object.freeze({__proto__:null,get_shared_code:function(t,e,r,n="ecdh/hmac512"){const i=yn(t),o=bn(i),s=Me.bytes(e),f=hn(n),a=Bn(i,e),c=[o.hex,s.hex].sort();return un(a,Me.join([...f,...c,r]))},get_shared_key:Bn});const _n=/^[0-9]{0,10}$/,In=/^[0-9a-zA-Z_&?=]{64}$/;function Un(t,e,r,n=!1){Lr(t,r);const i=Me.bytes(e);let o,s=void 0!==r?Me.bytes(r):Me.str("Bitcoin seed"),f=null,a=null;if(t.startsWith("m")){const t=Ln(s,i);s=t[1],a=t[0],o=bn(a,!1)}else n?(Ir(e,32),a=i,o=bn(a,!1)):(Ir(i,33),o=i);const c=Sn(t);for(const[t,e]of c){const r=e&&null!==a?Me.join([0,a,t]):Me.join([o,t]),[n,i]=Ln(s,r);s=Me.raw(i),f=o,null!==a?(a=Tr.mod(a).add(n).buff,o=bn(a,!1),Sr(a.big,!0)):(o=jr.from_x(o).add(n).buff,Ur(o.slice(1).big,!0))}return{seckey:a,pubkey:o,code:s,path:t,prev:f}}function Sn(t){$r(t);const e=[];let r=t.split("/");"m"!==r[0]&&""!==r[0]||(r=r.slice(1));for(let t of r){let r=!1;if("'"===t.slice(-1)&&(r=!0,t=t.slice(0,-1)),null!==t.match(_n)){let n=parseInt(t,10);kr(n),r&&(n+=2147483648),e.push([Me.num(n,4),r])}else{if(null===t.match(In))throw new Error("Invalid path segment:"+t);{let n=Me.str(t);r&&(n=n.prepend(128)),e.push([n.digest,r])}}}return e}function Ln(t,e){const r=un(t,e);return[r.slice(0,32),r.slice(32)]}function $n(t){const e=Me.b58chk(t).stream,r=e.read(4).num,n=e.read(1).num,i=e.read(4).num,o=e.read(4).num,s=e.read(32).hex,f=e.read(1).num,a=e.read(32).hex,c=0===f?a:void 0,u=0===f?bn(a).hex:Me.join([f,a]).hex;if(e.size>0)throw new TypeError("Unparsed data remaining in buffer!");return{prefix:r,depth:n,fprint:i,index:o,code:s,type:f,key:a,seckey:c,pubkey:u}}var kn=Object.freeze({__proto__:null,decode_extkey:$n,derive:Un,encode_extkey:function(t,e){const{seckey:r,pubkey:n,code:i,prev:o,path:s}=t,f="number"==typeof e?Me.num(e,4):null!==r?76066276:76067358,a=Sn(s),c=a.at(-1),u=Me.num(a.length,1),h=null!==o?cn(o).slice(0,4):Me.num(0,4),d=void 0!==c?c[0].slice(-4,4):Me.num(0,4),l=null!==r?r.prepend(0):n;return Me.join([f,u,h,d,i,l]).to_b58chk()},generate_code:Ln,parse_extkey:function(t,e=""){const{code:r,type:n,key:i}=$n(t),o=0===n;return Un(e,o?i:Me.join([n,i]),r,o)},parse_tweaks:Sn});function Hn(t,e,r){const n=Gt(r),{adaptor:i,tweak:o,xonly:s}=n,f=Me.bytes(t);let a=Tr.mod(e);void 0!==o&&(s&&(a=a.negated),a=a.add(o));const c=a.point,u=s?a.negated:a,h=Nn(f,u,n);let d=Tr.mod(h);void 0!==i&&(s&&(d=d.negated),d=d.add(i));const l=d.point,g=s?d.negated.big:d.big,w=dn("BIP0340/challenge",l.x.raw,c.x.raw,f),p=Tr.mod(w),y=Tr.mod(g+p.big*u.big),b=s?l.x.raw:l.raw;return Me.join([b,y.raw])}function On(t,e,r,n){const{throws:i}=Gt(n),o=Me.bytes(e),s=Me.bytes(t);if(s.length<64)return _r("Signature length is too small: "+String(s.length),i);Ir(r,32);const f=jr.from_x(r),a=s.subarray(0,32),c=jr.from_x(a),u=s.subarray(32,64),h=Tr.mod(u).point,d=dn("BIP0340/challenge",c.x,f.x,o),l=Tr.mod(d),g=f.mul(l.big),w=h.sub(g);return c.hasOddY?_r("Signature R value has odd Y coordinate!",i):c.x.big===pr?_r("Signature R value is infinite!",i):c.x.big!==w.x.big?_r(`Signature is invalid! R: ${c.x.hex} r:${w.x.hex}`,i):c.x.big===w.x.big}function Nn(t,e,r){const{aux:n,nonce:i,nonce_tweaks:o=[],recovery:s,xonly:f}=Gt(r);let a;if(void 0!==i)a=Me.bytes(i);else if(void 0!==s)a=Bn(e,s);else{const t=dn("BIP0340/aux",(null===n?Me.num(0,32):n)??Me.random(32)),r=Me.bytes(e).big^t.big;a=Me.join([r,bn(e,f)])}let c=Tr.mod(dn("BIP0340/nonce",a,Me.bytes(t)));return o.forEach((t=>{c=c.add(t).negated})),c.buff}var Rn=Object.freeze({__proto__:null,gen_nonce:Nn,recover:function(t,e,r,n){const i=Me.bytes(t),o=Me.bytes(e),s=Me.bytes(r),f=dn("BIP0340/nonce",Bn(n,r),e),a=dn("BIP0340/challenge",i.slice(0,32),xn(s),o),c=new Tr(a),u=new Tr(f).negated;return new Tr(i.slice(32,64)).sub(u).div(c).buff},sign:Hn,verify:On});const Tn={kind:2e4,stamp:0,tags:[]};function jn(t){const[e,r]=t.split("?"),n=Me.hex(e).stream;return Ar(160===n.size),{ref:n.read(32).hex,pub:n.read(32).hex,pid:n.read(32).hex,sig:n.read(64).hex,params:zn(r)}}function Cn(t=[]){const e=t.map((t=>[String(t[0]),String(t[1])]));return 0!==t.length?"?"+new URLSearchParams(e).toString():""}function zn(t){return"string"==typeof t?[...new URLSearchParams(t)]:[]}function Pn(t=[]){const{kind:e,stamp:r,...n}=Object.fromEntries(t);return{tags:Object.entries(n).map((([t,e])=>[t,String(e)])),kind:void 0!==e?Number(e):Tn.kind,stamp:void 0!==r?Number(r):Tn.stamp}}var qn=Object.freeze({__proto__:null,create_event:function(t,e){const r=wn(e),{pub:n,pid:i,sig:o,params:s}=jn(t),{kind:f,stamp:a,tags:c}=Pn(s);return{kind:f,content:r,tags:c,pubkey:n,id:i,sig:o,created_at:a}},create_proof:function(t,e,r,n){const{kind:i,stamp:o,tags:s}=Pn(r??[]),f=wn(e),a=bn(t,!0).hex,c=Me.str(f).digest,u=[0,a,o,i,s,f],h=Me.json(u).digest,d=Hn(h,t,n);return Me.join([c,a,h,d]).hex+Cn(r)},decode_params:zn,encode_params:Cn,parse_config:Pn,parse_proof:jn,parse_proofs:function(t){return t.map((t=>jn(t)))},validate_proof:function(t){return/^[0-9a-fA-F]{320}(?:\?[A-Za-z0-9_]+=[A-Za-z0-9_]+(?:&[A-Za-z0-9_]+=[A-Za-z0-9_]+)*)?$/.test(t)},verify_proof:function(t,e,r){const{throws:n=!1}=r??{},{ref:i,pub:o,pid:s,sig:f,params:a}=jn(t),{kind:c,stamp:u,tags:h}=Pn(a),d=wn(e);if(Me.str(d).digest.hex!==i)return _r("Content hash does not match reference hash!",n);const l=[0,o,u,c,h,d];return Me.json(l).digest.hex!==s?_r("Proof hash does not equal proof id!",n):!!On(f,s,o)||_r("Proof signature is invalid!",n)}});const Fn=_t.ProjectivePoint,Vn=Fn;function Dn(t){return new Fn(t.x,t.y,yr).hasEvenY()}function Gn(t){const e=t;return"object"==typeof e&&null!==e&&"bigint"==typeof e.x&&"bigint"==typeof e.y}function Zn(t){if(!Gn(t))return!1;const e=new Fn(t.x,t.y,yr);try{return e.assertValidity(),!0}catch{return!1}}var Yn=Object.freeze({__proto__:null,Noble:Vn,add:function(t,e){if(null===t)return e;if(null===e)return t;const r=new Fn(t.x,t.y,yr),n=new Fn(e.x,e.y,yr);try{const t=r.add(n);return t.assertValidity(),{x:t.x,y:t.y}}catch{return null}},assert_valid:function(t){if(!Zn(t))throw new Error("ECC point is invalid: "+String(t))},eq:function(t,e){return null===t&&null===e||null!==t&&null!==e&&(t.x===e.x&&t.y===e.y)},gen:function(t){const e=Me.bytes(t),r=Fn.BASE.multiply(e.big);return r.assertValidity(),{x:r.x,y:r.y}},is_even:Dn,is_point:Gn,is_valid:Zn,lift_x:function(t,e=!1){const r=En(t,e),n=Fn.fromHex(r.hex);return n.assertValidity(),{x:n.x,y:n.y}},mul:function(t,e){if(null===t)return null;try{const r=Me.bytes(e),n=new Fn(t.x,t.y,yr).multiply(r.big);return n.assertValidity(),{x:n.x,y:n.y}}catch{return null}},negate:function(t){const e=new Fn(t.x,t.y,yr);try{const t=e.negate();return t.assertValidity(),{x:t.x,y:t.y}}catch{return null}},sub:function(t,e){if(null===t)return e;if(null===e)return t;const r=new Fn(t.x,t.y,yr),n=new Fn(e.x,e.y,yr);try{const t=r.subtract(n);return t.assertValidity(),{x:t.x,y:t.y}}catch{return null}},to_bytes:function(t){const e=Me.big(t.x,32),r=Dn(t)?2:3;return Me.join([r,e])}});const Wn={secp:_t,schnorr:Vt};return t.CONST=Er,t.Field=Tr,t.Point=jr,t.assert=Hr,t.ecdh=An,t.fd=Rr,t.hash=ln,t.hd=kn,t.keys=vn,t.math=Br,t.noble=Wn,t.proof=qn,t.pt=Yn,t.sign_config=Gt,t.signer=Rn,t.util=pn,t}({});
//# sourceMappingURL=browser.js.map
