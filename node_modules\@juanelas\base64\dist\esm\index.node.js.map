{"version": 3, "file": "index.node.js", "sources": ["../../src/ts/index.ts"], "sourcesContent": ["/**\n * Base64url for both node.js and brwser javascript. It can work with ArrayBuffer|TypedArray|Buffer\n *\n * @remarks Bowser code obtained from https://github.com/panva/jose/blob/main/src/runtime/browser/base64url.ts\n * @packageDocumentation\n */\n\nimport { base64Encode, base64Decode } from './browser-base64.js'\n\n/**\n * A TypedArray object describes an array-like view of an underlying binary data buffer.\n */\nexport type TypedArray = Int8Array | Uint8Array | Uint8ClampedArray | Int16Array | Uint16Array | Int32Array | Uint32Array | Float32Array | Float64Array | BigInt64Array | BigUint64Array\n\n/**\n * Base64Url encoding of a buffer input or a string (UTF16 in browsers, UTF8 in node)\n * @param input\n * @param urlsafe - if true Base64 URL encoding is used ('+' and '/' are replaced by '-', '_')\n * @param padding - if false, padding (trailing '=') is removed\n * @returns a string with the base64-encoded representation of the input\n */\nexport function encode (input: ArrayBufferLike | TypedArray | Buffer | string, urlsafe: boolean = false, padding: boolean = true): string {\n  let base64 = ''\n  if (IS_BROWSER) {\n    const bytes = (typeof input === 'string')\n      ? (new TextEncoder()).encode(input)\n      : new Uint8Array(input)\n    base64 = base64Encode(bytes)\n  } else {\n    const bytes = (typeof input === 'string')\n      ? Buffer.from(input, 'utf8')\n      : Buffer.from(input)\n    base64 = bytes.toString('base64')\n  }\n  if (urlsafe) base64 = base64ToBase64url(base64)\n  if (!padding) base64 = removeBase64Padding(base64)\n  return base64\n}\n\n/**\n * Base64url decoding (binary output) of base64url-encoded string\n * @param base64 - a base64 string\n * @param stringOutput - if true a UTF16 (browser) or UTF8 (node) string is returned\n * @returns a buffer or unicode string\n */\nexport function decode (base64: string): Uint8Array\nexport function decode (base64: string, stringOutput: undefined): Uint8Array\nexport function decode (base64: string, stringOutput: false): Uint8Array\nexport function decode (base64: string, stringOutput: true): string\nexport function decode (base64: string, stringOutput: boolean): Uint8Array | string\nexport function decode (base64: string, stringOutput: undefined | boolean = false): Uint8Array | string {\n  if (IS_BROWSER) {\n    let urlsafe = false\n    if (/^[0-9a-zA-Z_-]+={0,2}$/.test(base64)) {\n      urlsafe = true\n    } else if (!/^[0-9a-zA-Z+/]*={0,2}$/.test(base64)) {\n      throw new Error('Not a valid base64 input')\n    }\n    if (urlsafe) base64 = base64urlToBase64(base64)\n    const bytes = base64Decode(base64)\n    return stringOutput\n      ? (new TextDecoder()).decode(bytes)\n      : bytes\n  } else {\n    const buffer = Buffer.from(base64, 'base64')\n    return stringOutput\n      ? buffer.toString('utf8')\n      : new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.length)\n  }\n}\n\nfunction base64ToBase64url (base64: string): string {\n  return base64.replace(/\\+/g, '-').replace(/\\//g, '_')\n}\n\nfunction base64urlToBase64 (base64url: string): string {\n  return base64url.replace(/-/g, '+').replace(/_/g, '/').replace(/=/g, '')\n}\n\nfunction removeBase64Padding (str: string): string {\n  return str.replace(/=/g, '')\n}\n"], "names": ["encode", "input", "urlsafe", "padding", "base64", "<PERSON><PERSON><PERSON>", "from", "toString", "replace", "base64ToBase64url", "decode", "stringOutput", "buffer", "Uint8Array", "byteOffset", "length"], "mappings": "AAqBM,SAAUA,EAAQC,EAAuDC,GAAmB,EAAOC,GAAmB,GAC1H,IAAIC,EAAS,GAUXA,GAHgC,iBAAVH,EAClBI,OAAOC,KAAKL,EAAO,QACnBI,OAAOC,KAAKL,IACDM,SAAS,UAI1B,OAFIL,IAASE,EAqCf,SAA4BA,GAC1B,OAAOA,EAAOI,QAAQ,MAAO,KAAKA,QAAQ,MAAO,IACnD,CAvCwBC,CAAkBL,IACnCD,IAASC,EAA6BA,EA6ChCI,QAAQ,KAAM,KA5ClBJ,CACT,UAagBM,EAAQN,EAAgBO,GAAoC,GAanE,CACL,MAAMC,EAASP,OAAOC,KAAKF,EAAQ,UACnC,OAAOO,EACHC,EAAOL,SAAS,QAChB,IAAIM,WAAWD,EAAOA,OAAQA,EAAOE,WAAYF,EAAOG,OAC7D,CACH"}