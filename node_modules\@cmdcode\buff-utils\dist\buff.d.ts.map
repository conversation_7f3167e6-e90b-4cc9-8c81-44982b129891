{"version": 3, "file": "buff.d.ts", "sourceRoot": "", "sources": ["../src/buff.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,GAAG,MAAS,mBAAmB,CAAA;AAC3C,OAAO,KAAK,IAAI,MAAQ,YAAY,CAAA;AAEpC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,YAAY,CAAA;AAE7D,qBAAa,IAAK,SAAQ,UAAU;IAClC,MAAM,CAAC,GAAG,mBAAiB;IAC3B,MAAM,CAAC,GAAG,mBAAiB;IAC3B,MAAM,CAAC,GAAG,mBAAiB;IAC3B,MAAM,CAAC,GAAG,mBAAiB;IAC3B,MAAM,CAAC,GAAG,mBAAiB;IAC3B,MAAM,CAAC,GAAG,mBAAiB;IAC3B,MAAM,CAAC,KAAK,gBAAY;IACxB,MAAM,CAAC,IAAI,oBAAiB;IAC5B,MAAM,CAAC,MAAM,sBAAiB;IAC9B,MAAM,CAAC,MAAM,sBAAiB;IAC9B,MAAM,CAAC,MAAM,sBAAiB;IAC9B,MAAM,CAAC,OAAO,uBAAiB;IAC/B,MAAM,CAAC,MAAM,sBAAiB;IAC9B,MAAM,CAAC,MAAM,wBAAmB;IAChC,MAAM,CAAC,MAAM,wBAAmB;IAChC,MAAM,CAAC,KAAK,oBAAgB;IAC5B,MAAM,CAAC,QAAQ,uBAAgB;IAC/B,MAAM,CAAC,MAAM,qBAAgB;IAE7B,MAAM,CAAC,MAAM,CAAE,IAAI,SAAK,GAAI,IAAI;IAKhC,MAAM,CAAC,GAAG,CAAE,IAAI,SAAI,GAAI,IAAI;gBAM1B,IAAI,EAAM,KAAK,GAAG,KAAK,EAAE,GAAG,WAAW,EACvC,IAAO,CAAC,EAAE,MAAM,EAChB,MAAO,CAAC,EAAE,MAAM;IAalB,IAAI,GAAG,IAAM,MAAM,EAAE,CAEpB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,GAAG,IAAM,UAAU,CAEtB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,MAAM,IAAM,MAAM,CAErB;IAED,IAAI,MAAM,IAAM,MAAM,CAErB;IAED,IAAI,MAAM,IAAM,MAAM,CAErB;IAED,IAAI,MAAM,IAAM,IAAI,CAEnB;IAED,IAAI,EAAE,IAAM,MAAM,CAEjB;IAED,IAAI,MAAM,IAAM,MAAM,CAErB;IAED,MAAM,CAAE,MAAM,GAAG,MAAa,GAAI,MAAM;IAOxC,MAAM,CAAE,MAAM,GAAG,MAAa,GAAI,MAAM;IAOxC,MAAM,IAAM,MAAM;IAIlB,OAAO,IAAM,IAAI;IAKjB,OAAO,CAAE,CAAC,GAAG,GAAG,EAAG,OAAQ,CAAC,EAAE,OAAO,GAAI,CAAC;IAQ1C,SAAS,CACP,MAAM,EAAG,MAAM,EACf,KAAM,CAAC,EAAE,MAAM,GACb,MAAM;IAMV,UAAU,CACR,MAAM,EAAG,MAAM,EACf,KAAM,CAAC,EAAE,MAAM,GACb,MAAM;IAMV,MAAM,IAAS,MAAM;IACrB,MAAM,IAAS,MAAM;IACrB,QAAQ,IAAO,UAAU;IACzB,SAAS,IAAM,MAAM;IACrB,SAAS,IAAM,MAAM;IACrB,SAAS,IAAM,MAAM;IAErB,MAAM,CAAE,IAAI,EAAG,KAAK,GAAI,IAAI;IAI5B,OAAO,CAAE,IAAI,EAAG,KAAK,GAAI,IAAI;IAI7B,OAAO,IAAM,IAAI;IAKjB,KAAK,CAAE,KAAM,CAAC,EAAE,MAAM,EAAE,GAAI,CAAC,EAAE,MAAM,GAAI,IAAI;IAK7C,GAAG,CAAE,KAAK,EAAG,SAAS,CAAC,MAAM,CAAC,EAAE,MAAO,CAAC,EAAE,MAAM,GAAI,IAAI;IAIxD,QAAQ,CAAE,KAAM,CAAC,EAAE,MAAM,EAAE,GAAI,CAAC,EAAE,MAAM,GAAI,IAAI;IAKhD,KAAK,CAAE,KAAK,EAAG,KAAK,EAAE,MAAO,CAAC,EAAE,MAAM,GAAI,IAAI;IAK9C,UAAU,CAAE,MAAO,CAAC,EAAE,MAAM,GAAI,IAAI;IAKpC,MAAM,CAAC,IAAI,CAAE,IAAI,EAAG,UAAU,GAAG,MAAM,EAAE,GAAI,IAAI;IAIjD,MAAM,CAAC,EAAE,CAAE,GAAG,IAAI,EAAG,MAAM,EAAE,GAAI,IAAI;IAIrC,MAAM,CAAC,IAAI,CAAE,GAAG,EAAG,KAAK,EAAE,GAAI,IAAI;IAMlC,MAAM,CAAC,IAAI,CAAE,GAAG,EAAG,KAAK,EAAE,EAAE,IAAK,CAAC,EAAE,MAAM,GAAI,IAAI,EAAE;IAMpD,MAAM,CAAC,WAAW,CAAE,GAAG,EAAG,MAAM,EAAE,MAAO,CAAC,EAAE,MAAM,GAAI,IAAI;CAa3D;AAED,iBAAS,SAAS,CAChB,MAAM,EAAI,MAAM,EAChB,IAAO,CAAC,EAAE,MAAM,EAChB,MAAO,CAAC,EAAE,MAAM,GACd,IAAI,CAEP;AAED,iBAAS,SAAS,CAChB,IAAI,EAAM,MAAM,EAChB,IAAO,CAAC,EAAE,MAAM,EAChB,MAAO,CAAC,EAAE,MAAM,GACd,IAAI,CAEP;AAED,iBAAS,SAAS,CAChB,MAAM,EAAI,MAAM,EAChB,IAAO,CAAC,EAAE,MAAM,EAChB,MAAO,CAAC,EAAE,MAAM,GACd,IAAI,CAEP;AAED,iBAAS,SAAS,CAChB,IAAI,EAAM,UAAU,EACpB,IAAO,CAAC,EAAE,MAAM,EAChB,MAAO,CAAC,EAAE,MAAM,GACd,IAAI,CAEP;AAED,iBAAS,SAAS,CAChB,IAAI,EAAM,MAAM,EAChB,IAAO,CAAC,EAAE,MAAM,EAChB,MAAO,CAAC,EAAE,MAAM,GACd,IAAI,CAEP;AAED,iBAAS,SAAS,CAChB,IAAI,EAAM,MAAM,EAChB,IAAO,CAAC,EAAE,MAAM,EAChB,MAAO,CAAC,EAAE,MAAM,GACd,IAAI,CAEP;AAED,iBAAS,UAAU,CAAE,CAAC,EACpB,IAAI,EAAQ,CAAC,EACb,QAAS,CAAC,EAAE,QAAQ,GAClB,IAAI,CAMP;AAED,iBAAS,YAAY,CACnB,IAAI,EAAG,MAAM,GACX,IAAI,CAEP;AAED,iBAAS,YAAY,CACnB,IAAI,EAAG,MAAM,GACX,IAAI,CAEP;AAED,iBAAS,YAAY,CACnB,IAAI,EAAU,MAAM,EACpB,KAAW,CAAC,EAAE,MAAM,GAAG,KAAK,EAC5B,UAAW,CAAC,EAAE,MAAM,GAClB,IAAI,CAQP;AAED,iBAAS,aAAa,CACpB,IAAI,EAAU,MAAM,EACpB,KAAW,CAAC,EAAE,MAAM,GAAG,KAAK,EAC5B,UAAW,CAAC,EAAE,MAAM,GAClB,IAAI,CAQP;AAED,iBAAS,YAAY,CACnB,IAAI,EAAG,MAAM,GACX,IAAI,CAEP;AAED,iBAAS,UAAU,CACjB,SAAS,EAAI,KAAK,EAClB,UAAU,EAAG,MAAM,EACnB,UAAU,EAAG,MAAM,GACjB,IAAI,EAAE,CAIT;AAED,qBAAa,MAAM;IACV,IAAI,EAAG,MAAM,CAAA;IACb,IAAI,EAAG,UAAU,CAAA;gBAEX,IAAI,EAAG,KAAK;IAKzB,IAAI,CAAE,IAAI,EAAG,MAAM,GAAI,IAAI;IAO3B,IAAI,CAAE,IAAI,EAAG,MAAM,GAAI,IAAI;IAO3B,WAAW,CAAE,MAAO,CAAC,EAAE,MAAM,GAAI,MAAM;CAexC;AAED,wBAAgB,MAAM,CACpB,KAAK,EAAG,KAAK,GAAG,KAAK,EAAE,GAAG,WAAW,EACrC,IAAK,CAAC,EAAE,MAAM,EACd,GAAK,CAAC,EAAE,MAAM,GACZ,IAAI,CAEP"}