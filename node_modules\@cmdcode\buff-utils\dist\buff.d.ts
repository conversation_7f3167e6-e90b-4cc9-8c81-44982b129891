import * as fmt from './format/index.js';
import * as util from './utils.js';
import { Bytes, Endian, Replacer, Reviver } from './types.js';
export declare class Buff extends Uint8Array {
    static num: typeof numToBuff;
    static big: typeof bigToBuff;
    static bin: typeof binToBuff;
    static raw: typeof rawToBuff;
    static str: typeof strToBuff;
    static hex: typeof hexToBuff;
    static bytes: typeof buffer;
    static json: typeof jsonToBuff;
    static base64: typeof base64ToBuff;
    static b64url: typeof b64urlToBuff;
    static bech32: typeof bech32ToBuff;
    static bech32m: typeof bech32mToBuff;
    static b58chk: typeof b58chkToBuff;
    static encode: typeof fmt.strToBytes;
    static decode: typeof fmt.bytesToStr;
    static parse: typeof parse_data;
    static is_bytes: typeof util.is_bytes;
    static is_hex: typeof util.is_hex;
    static random(size?: number): Buff;
    static now(size?: number): Buff;
    constructor(data: Bytes | Bytes[] | ArrayBuffer, size?: number, endian?: Endian);
    get arr(): number[];
    get num(): number;
    get big(): bigint;
    get str(): string;
    get hex(): string;
    get raw(): Uint8Array;
    get bin(): string;
    get b58chk(): string;
    get base64(): string;
    get b64url(): string;
    get digest(): Buff;
    get id(): string;
    get stream(): Stream;
    to_num(endian?: Endian): number;
    to_big(endian?: Endian): bigint;
    to_bin(): string;
    to_hash(): Buff;
    to_json<T = any>(reviver?: Reviver): T;
    to_bech32(prefix: string, limit?: number): string;
    to_bech32m(prefix: string, limit?: number): string;
    to_str(): string;
    to_hex(): string;
    to_bytes(): Uint8Array;
    to_b58chk(): string;
    to_base64(): string;
    to_b64url(): string;
    append(data: Bytes): Buff;
    prepend(data: Bytes): Buff;
    reverse(): Buff;
    slice(start?: number, end?: number): Buff;
    set(array: ArrayLike<number>, offset?: number): void;
    subarray(begin?: number, end?: number): Buff;
    write(bytes: Bytes, offset?: number): void;
    add_varint(endian?: Endian): Buff;
    static from(data: Uint8Array | number[]): Buff;
    static of(...args: number[]): Buff;
    static join(arr: Bytes[]): Buff;
    static sort(arr: Bytes[], size?: number): Buff[];
    static calc_varint(num: number, endian?: Endian): Buff;
}
declare function numToBuff(number: number, size?: number, endian?: Endian): Buff;
declare function binToBuff(data: string, size?: number, endian?: Endian): Buff;
declare function bigToBuff(bigint: bigint, size?: number, endian?: Endian): Buff;
declare function rawToBuff(data: Uint8Array, size?: number, endian?: Endian): Buff;
declare function strToBuff(data: string, size?: number, endian?: Endian): Buff;
declare function hexToBuff(data: string, size?: number, endian?: Endian): Buff;
declare function jsonToBuff<T>(data: T, replacer?: Replacer): Buff;
declare function base64ToBuff(data: string): Buff;
declare function b64urlToBuff(data: string): Buff;
declare function bech32ToBuff(data: string, limit?: number | false, chk_prefix?: string): Buff;
declare function bech32mToBuff(data: string, limit?: number | false, chk_prefix?: string): Buff;
declare function b58chkToBuff(data: string): Buff;
declare function parse_data(data_blob: Bytes, chunk_size: number, total_size: number): Buff[];
export declare class Stream {
    size: number;
    data: Uint8Array;
    constructor(data: Bytes);
    peek(size: number): Buff;
    read(size: number): Buff;
    read_varint(endian?: Endian): number;
}
export declare function buffer(bytes: Bytes | Bytes[] | ArrayBuffer, size?: number, end?: Endian): Buff;
export {};
//# sourceMappingURL=buff.d.ts.map