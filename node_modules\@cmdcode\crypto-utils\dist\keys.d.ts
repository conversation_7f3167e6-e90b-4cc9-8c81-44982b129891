import { Buff, Bytes } from '@cmdcode/buff-utils';
export declare function is_even_pub(pubkey: Bytes): boolean;
export declare function gen_seckey(even_y?: boolean): Buff;
export declare function get_seckey(secret: Bytes, even_y?: boolean): Buff;
export declare function get_pubkey(seckey: Bytes, x_only?: boolean): Buff;
export declare function tweak_seckey(seckey: Bytes, tweaks?: Bytes[], even_y?: boolean): Buff;
export declare function tweak_pubkey(pubkey: Bytes, tweaks?: Bytes[], x_only?: boolean): Buff;
export declare function negate_seckey(seckey: Bytes, negate: boolean): Buff;
export declare function get_keypair(secret: Bytes, x_only?: boolean, even_y?: boolean): [Buff, Buff];
export declare function gen_keypair(x_only?: boolean, even_y?: boolean): [Buff, Buff];
export declare function convert_32(pubkey: Bytes): Buff;
export declare function convert_33(pubkey: Bytes, even_y?: boolean): Buff;
//# sourceMappingURL=keys.d.ts.map