import { Buff, Bytes } from '@cmdcode/buff-utils';
import { SignOptions } from './config.js';
export declare function sign(message: Bytes, secret: Bytes, options?: SignOptions): Buff;
export declare function verify(signature: Bytes, message: Bytes, pubkey: Bytes, options?: SignOptions): boolean;
export declare function recover(signature: Bytes, message: Bytes, pub_key: Bytes, rec_key: Bytes): Buff;
export declare function gen_nonce(message: Bytes, secret: Bytes, options?: SignOptions): Buff;
//# sourceMappingURL=sig.d.ts.map