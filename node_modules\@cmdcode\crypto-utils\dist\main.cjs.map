{"version": 3, "file": "main.cjs", "sources": ["../node_modules/@noble/hashes/esm/_assert.js", "../node_modules/@noble/hashes/esm/crypto.js", "../node_modules/@noble/hashes/esm/utils.js", "../node_modules/@noble/hashes/esm/_sha2.js", "../node_modules/@noble/hashes/esm/sha256.js", "../node_modules/@noble/curves/esm/abstract/utils.js", "../node_modules/@noble/curves/esm/abstract/modular.js", "../node_modules/@noble/curves/esm/abstract/curve.js", "../node_modules/@noble/curves/esm/abstract/weierstrass.js", "../node_modules/@noble/hashes/esm/hmac.js", "../node_modules/@noble/curves/esm/_shortw_utils.js", "../node_modules/@noble/curves/esm/secp256k1.js", "../src/config.ts", "../node_modules/@cmdcode/buff-utils/dist/module.mjs", "../src/const.ts", "../src/math.ts", "../src/assert.ts", "../src/ecc.ts", "../node_modules/@noble/hashes/esm/_u64.js", "../node_modules/@noble/hashes/esm/sha512.js", "../node_modules/@noble/hashes/esm/ripemd160.js", "../src/hash.ts", "../src/util.ts", "../src/keys.ts", "../src/hd.ts", "../src/proof.ts", "../src/point.ts"], "sourcesContent": ["function number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`Wrong positive integer: ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`Expected boolean, not ${b}`);\n}\nfunction bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new Error('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n    if (typeof hash !== 'function' || typeof hash.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\nexport { number, bool, bytes, hash, exists, output };\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n//# sourceMappingURL=_assert.js.map", "export const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n//# sourceMappingURL=crypto.js.map", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nconst u8a = (a) => a instanceof Uint8Array;\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!u8a(data))\n        throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\n// For runtime check if class implements interface\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "import { exists, output } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nexport class SHA2 extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        exists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        exists(this);\n        output(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_sha2.js.map", "import { SHA2 } from './_sha2.js';\nimport { rotr, wrapConstructor } from './utils.js';\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n//# sourceMappingURL=sha256.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst u8a = (a) => a instanceof Uint8Array;\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nexport function numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? `0${hex}` : hex;\n}\nexport function hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // Big Endian\n    return BigInt(hex === '' ? '0' : `0x${hex}`);\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nexport function numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(`${title} must be valid hex string, got \"${hex}\". Cause: ${e}`);\n        }\n    }\n    else if (u8a(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(`${title} must be hex string or Uint8Array`);\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(`${title} expected ${expectedLength} bytes, got ${len}`);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\nexport function equalBytes(b1, b2) {\n    // We don't care about timing attacks here\n    if (b1.length !== b2.length)\n        return false;\n    for (let i = 0; i < b1.length; i++)\n        if (b1[i] !== b2[i])\n            return false;\n    return true;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nexport const bitSet = (n, pos, value) => {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n};\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || val instanceof Uint8Array,\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nexport function validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error(`Invalid validator \"${type}\", expected function`);\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error(`Invalid param ${String(fieldName)}=${val} (${typeof val}), expected ${type}`);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n//# sourceMappingURL=utils.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Utilities for modular arithmetics and finite fields\nimport { bitMask, numberToBytesBE, numberToBytesLE, bytesToNumberBE, bytesToNumberLE, ensureBytes, validateObject, } from './utils.js';\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _4n = BigInt(4), _5n = BigInt(5), _8n = BigInt(8);\n// prettier-ignore\nconst _9n = BigInt(9), _16n = BigInt(16);\n// Calculates a modulo b\nexport function mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\n// TODO: use field version && remove\nexport function pow(num, power, modulo) {\n    if (modulo <= _0n || power < _0n)\n        throw new Error('Expected power/modulo > 0');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n// Does x ^ (2 ^ power) mod p. pow2(30, 4) == 30 ^ (2 ^ 4)\nexport function pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n// Inverses number over modulo\nexport function invert(number, modulo) {\n    if (number === _0n || modulo <= _0n) {\n        throw new Error(`invert: expected positive integers, got n=${number} mod=${modulo}`);\n    }\n    // Euclidean GCD https://brilliant.org/wiki/extended-euclidean-algorithm/\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++)\n        ;\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\nexport function FpSqrt(P) {\n    // NOTE: different algorithms can give different roots, it is up to user to decide which one they want.\n    // For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nexport function validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return validateObject(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('Expected power > 0');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nexport function FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare(f) {\n    const legendreConst = (f.ORDER - _1n) / _2n; // Integer arithmetic\n    return (x) => {\n        const p = f.pow(x, legendreConst);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nexport function nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime. **Non-primes are not supported.**\n * Do not init in loop: slow. Very fragile: always run a benchmark on a change.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error(`Expected Field ORDER > 0, got ${ORDER}`);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('Field lengths over 2048 bytes are not supported');\n    const sqrtP = FpSqrt(ORDER);\n    const f = Object.freeze({\n        ORDER,\n        BITS,\n        BYTES,\n        MASK: bitMask(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error(`Invalid field element: expected bigint, got ${typeof num}`);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt || ((n) => sqrtP(f, n)),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error(`Fp.fromBytes: expected ${BYTES}, got ${bytes.length}`);\n            return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nexport function FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nexport function FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use mapKeyToField instead\n */\nexport function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = ensureBytes('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error(`hashToPrivateScalar: expected ${minLen}-1024 bytes of input, got ${hashLen}`);\n    const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error(`expected ${minLen}-1024 bytes of input, got ${len}`);\n    const num = isLE ? bytesToNumberBE(key) : bytesToNumberLE(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Abelian group utilities\nimport { validateField, nLength } from './modular.js';\nimport { validateObject } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n// Elliptic curve multiplication of Point by scalar. Fragile.\n// Scalars should always be less than curve order: this should be checked inside of a curve itself.\n// Creates precomputation tables for fast multiplication:\n// - private scalar is split by fixed size windows of W bits\n// - every window point is collected from window's table & added to accumulator\n// - since windows are different, same point inside tables won't be accessed more than once per calc\n// - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n// - +1 window is neccessary for wNAF\n// - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n// TODO: Research returning 2d JS array of windows, instead of a single window. This would allow\n// windows to be in different memory locations\nexport function wNAF(c, bits) {\n    const constTimeNegate = (condition, item) => {\n        const neg = item.negate();\n        return condition ? neg : item;\n    };\n    const opts = (W) => {\n        const windows = Math.ceil(bits / W) + 1; // +1, because\n        const windowSize = 2 ** (W - 1); // -1 because we skip zero\n        return { windows, windowSize };\n    };\n    return {\n        constTimeNegate,\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n) {\n            let p = c.ZERO;\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = opts(W);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = opts(W);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        wNAFCached(P, precomputesMap, n, transform) {\n            // @ts-ignore\n            const W = P._WINDOW_SIZE || 1;\n            // Calculate precomputes on a first run, reuse them after\n            let comp = precomputesMap.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1) {\n                    precomputesMap.set(P, transform(comp));\n                }\n            }\n            return this.wNAF(W, comp, n);\n        },\n    };\n}\nexport function validateBasic(curve) {\n    validateField(curve.Fp);\n    validateObject(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...nLength(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Short <PERSON> curve. The formula is: y² = x³ + ax + b\nimport * as mod from './modular.js';\nimport * as ut from './utils.js';\nimport { ensureBytes } from './utils.js';\nimport { wNAF, validateBasic } from './curve.js';\nfunction validatePointOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('Endomorphism can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('Expected endomorphism with beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\n// ASN.1 DER encoding utilities\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = ut;\nexport const DER = {\n    // asn.1 DER encoding utils\n    Err: class DERErr extends Error {\n        constructor(m = '') {\n            super(m);\n        }\n    },\n    _parseInt(data) {\n        const { Err: E } = DER;\n        if (data.length < 2 || data[0] !== 0x02)\n            throw new E('Invalid signature integer tag');\n        const len = data[1];\n        const res = data.subarray(2, len + 2);\n        if (!len || res.length !== len)\n            throw new E('Invalid signature integer: wrong length');\n        // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n        // since we always use positive integers here. It must always be empty:\n        // - add zero byte if exists\n        // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n        if (res[0] & 0b10000000)\n            throw new E('Invalid signature integer: negative');\n        if (res[0] === 0x00 && !(res[1] & 0b10000000))\n            throw new E('Invalid signature integer: unnecessary leading zero');\n        return { d: b2n(res), l: data.subarray(len + 2) }; // d is data, l is left\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        if (!(data instanceof Uint8Array))\n            throw new Error('ui8a expected');\n        let l = data.length;\n        if (l < 2 || data[0] != 0x30)\n            throw new E('Invalid signature tag');\n        if (data[1] !== l - 2)\n            throw new E('Invalid signature: incorrect length');\n        const { d: r, l: sBytes } = DER._parseInt(data.subarray(2));\n        const { d: s, l: rBytesLeft } = DER._parseInt(sBytes);\n        if (rBytesLeft.length)\n            throw new E('Invalid signature: left bytes after parsing');\n        return { r, s };\n    },\n    hexFromSig(sig) {\n        // Add leading zero if first byte has negative bit enabled. More details in '_parseInt'\n        const slice = (s) => (Number.parseInt(s[0], 16) & 0b1000 ? '00' + s : s);\n        const h = (num) => {\n            const hex = num.toString(16);\n            return hex.length & 1 ? `0${hex}` : hex;\n        };\n        const s = slice(h(sig.s));\n        const r = slice(h(sig.r));\n        const shl = s.length / 2;\n        const rhl = r.length / 2;\n        const sl = h(shl);\n        const rl = h(rhl);\n        return `30${h(rhl + shl + 4)}02${rl}${r}02${sl}${s}`;\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nexport function weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return ut.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return typeof num === 'bigint' && _0n < num && num < CURVE.n;\n    }\n    function assertGE(num) {\n        if (!isWithinCurveOrder(num))\n            throw new Error('Expected valid bigint: 0 < bigint < curve.n');\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (key instanceof Uint8Array)\n                key = ut.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('Invalid key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : ut.bytesToNumberBE(ensureBytes('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error(`private key must be ${nByteLength} bytes, hex or bigint, not ${typeof key}`);\n        }\n        if (wrapPrivateKey)\n            num = mod.mod(num, n); // disabled by default, enabled for BLS\n        assertGE(num); // num in range [1..N-1]\n        return num;\n    }\n    const pointPrecomputes = new Map();\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            this._WINDOW_SIZE = windowSize;\n            pointPrecomputes.delete(this);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            if (this.is0()) {\n                // (0, 1, 0) aka ZERO is invalid in most contexts.\n                // In BLS, ZERO can be serialized, so we allow it.\n                // (0, 0, 0) is wrong representation of ZERO and is always invalid.\n                if (CURVE.allowInfinityPoint && !Fp.is0(this.py))\n                    return;\n                throw new Error('bad point: ZERO');\n            }\n            // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n            const { x, y } = this.toAffine();\n            // Check if x, y are valid field elements\n            if (!Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('bad point: x or y not FE');\n            const left = Fp.sqr(y); // y²\n            const right = weierstrassEquation(x); // x³ + ax + b\n            if (!Fp.eql(left, right))\n                throw new Error('bad point: equation left != right');\n            if (!this.isTorsionFree())\n                throw new Error('bad point: not in prime-order subgroup');\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, pointPrecomputes, n, (comp) => {\n                const toInv = Fp.invertBatch(comp.map((p) => p.pz));\n                return comp.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n            });\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(n) {\n            const I = Point.ZERO;\n            if (n === _0n)\n                return I;\n            assertGE(n); // Will throw on 0\n            if (n === _1n)\n                return this;\n            const { endo } = CURVE;\n            if (!endo)\n                return wnaf.unsafeLadder(this, n);\n            // Apply endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            assertGE(scalar);\n            let n = scalar;\n            let point, fake; // Fake point is used to const-time mult\n            const { endo } = CURVE;\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(n);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            const { px: x, py: y, pz: z } = this;\n            const is0 = this.is0();\n            // If invZ was 0, we return zero point. However we still want to execute\n            // all operations, so we replace invZ with a random number, 1.\n            if (iz == null)\n                iz = is0 ? Fp.ONE : Fp.inv(z);\n            const ax = Fp.mul(x, iz);\n            const ay = Fp.mul(y, iz);\n            const zz = Fp.mul(z, iz);\n            if (is0)\n                return { x: Fp.ZERO, y: Fp.ZERO };\n            if (!Fp.eql(zz, Fp.ONE))\n                throw new Error('invZ was invalid');\n            return { x: ax, y: ay };\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            return ut.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = wNAF(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\nexport function weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function isValidFieldElement(num) {\n        return _0n < num && num < Fp.ORDER; // 0 is banned since it's not invertible FE\n    }\n    function modN(a) {\n        return mod.mod(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return mod.invert(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = ut.concatBytes;\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = ut.bytesToNumberBE(tail);\n                if (!isValidFieldElement(x))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                throw new Error(`Point of length ${len} was invalid. Expected ${compressedLen} compressed bytes or ${uncompressedLen} uncompressed bytes`);\n            }\n        },\n    });\n    const numToNByteStr = (num) => ut.bytesToHex(ut.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => ut.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = ensureBytes('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig(ensureBytes('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            // can use assertGE here\n            if (!isWithinCurveOrder(this.r))\n                throw new Error('r must be 0 < r < CURVE.n');\n            if (!isWithinCurveOrder(this.s))\n                throw new Error('s must be 0 < s < CURVE.n');\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return ut.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return ut.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = mod.getMinHashLength(CURVE.n);\n            return mod.mapHashToField(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = item instanceof Uint8Array;\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = ut.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = ut.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        if (typeof num !== 'bigint')\n            throw new Error('bigint expected');\n        if (!(_0n <= num && num < ORDER_MASK))\n            throw new Error(`bigint expected < 2^${CURVE.nBitLength}`);\n        // works with order, can have different size than numToField!\n        return ut.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order, this will be wrong at least for P521.\n    // Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = ensureBytes('msgHash', msgHash);\n        if (prehash)\n            msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n        }\n        const seed = ut.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = ut.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = ensureBytes('msgHash', msgHash);\n        publicKey = ensureBytes('publicKey', publicKey);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        const { lowS, prehash } = opts;\n        let _sig = undefined;\n        let P;\n        try {\n            if (typeof sg === 'string' || sg instanceof Uint8Array) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                    _sig = Signature.fromCompact(sg);\n                }\n            }\n            else if (typeof sg === 'object' && typeof sg.r === 'bigint' && typeof sg.s === 'bigint') {\n                const { r, s } = sg;\n                _sig = new Signature(r, s);\n            }\n            else {\n                throw new Error('PARSE');\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            if (error.message === 'PARSE')\n                throw new Error(`signature must be Signature instance, Uint8Array or hex string`);\n            return false;\n        }\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU(Fp, opts) {\n    mod.validateField(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map", "import { hash as assertHash, bytes as assertBytes, exists as assertExists } from './_assert.js';\nimport { Hash, toBytes } from './utils.js';\n// HMAC (RFC 2104)\nexport class HMAC extends Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        assertHash(hash);\n        const key = toBytes(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        assertExists(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        assertExists(this);\n        assertBytes(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n */\nexport const hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac';\nimport { concatBytes, randomBytes } from '@noble/hashes/utils';\nimport { weierstrass } from './abstract/weierstrass.js';\n// connects noble-curves to noble-hashes\nexport function getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => hmac(hash, key, concatBytes(...msgs)),\n        randomBytes,\n    };\n}\nexport function createCurve(curveDef, defHash) {\n    const create = (hash) => weierstrass({ ...curveDef, ...getHash(hash) });\n    return Object.freeze({ ...create(defHash), create });\n}\n//# sourceMappingURL=_shortw_utils.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha256';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { Field, mod, pow2 } from './abstract/modular.js';\nimport { mapToCurveSimpleSWU } from './abstract/weierstrass.js';\nimport { bytesToNumberBE, concatBytes, ensureBytes, numberToBytesBE } from './abstract/utils.js';\nimport { createHasher, isogenyMap } from './abstract/hash-to-curve.js';\nimport { createCurve } from './_shortw_utils.js';\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = (pow2(b3, _3n, P) * b3) % P;\n    const b9 = (pow2(b6, _3n, P) * b3) % P;\n    const b11 = (pow2(b9, _2n, P) * b2) % P;\n    const b22 = (pow2(b11, _11n, P) * b11) % P;\n    const b44 = (pow2(b22, _22n, P) * b22) % P;\n    const b88 = (pow2(b44, _44n, P) * b44) % P;\n    const b176 = (pow2(b88, _88n, P) * b88) % P;\n    const b220 = (pow2(b176, _44n, P) * b44) % P;\n    const b223 = (pow2(b220, _3n, P) * b3) % P;\n    const t1 = (pow2(b223, _23n, P) * b22) % P;\n    const t2 = (pow2(t1, _6n, P) * b2) % P;\n    const root = pow2(t2, _2n, P);\n    if (!Fp.eql(Fp.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fp = Field(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\nexport const secp256k1 = createCurve({\n    a: BigInt(0),\n    b: BigInt(7),\n    Fp,\n    n: secp256k1N,\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1),\n    lowS: true,\n    /**\n     * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n     * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n     * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n     * Explanation: https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066\n     */\n    endo: {\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = mod(k - c1 * a1 - c2 * a2, n);\n            let k2 = mod(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\nconst fe = (x) => typeof x === 'bigint' && _0n < x && x < secp256k1P;\nconst ge = (x) => typeof x === 'bigint' && _0n < x && x < secp256k1N;\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = sha256(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = concatBytes(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return sha256(concatBytes(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => numberToBytesBE(n, 32);\nconst modP = (x) => mod(x, secp256k1P);\nconst modN = (x) => mod(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    if (!fe(x))\n        throw new Error('bad x: need 0 < x < p'); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(bytesToNumberBE(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = randomBytes(32)) {\n    const m = ensureBytes('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ bytesToNumberBE(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(bytesToNumberBE(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = ensureBytes('signature', signature, 64);\n    const m = ensureBytes('message', message);\n    const pub = ensureBytes('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(bytesToNumberBE(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = bytesToNumberBE(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!fe(r))\n            return false;\n        const s = bytesToNumberBE(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!ge(s))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\nexport const schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE,\n        bytesToNumberBE,\n        taggedHash,\n        mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => isogenyMap(Fp, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => mapToCurveSimpleSWU(Fp, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fp.create(BigInt('-11')),\n}))();\nconst htf = /* @__PURE__ */ (() => createHasher(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fp.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fp.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: sha256,\n}))();\nexport const hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\nexport const encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map", null, "function bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new Error('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\n\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nconst u8a = (a) => a instanceof Uint8Array;\n// Cast array to view\nconst createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nconst rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nconst isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!u8a(data))\n        throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n// For runtime check if class implements interface\nclass Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nfunction wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\n\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nclass SHA2 extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        exists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        exists(this);\n        output(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nconst sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\n\n/*! scure-base - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// Utilities\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n    // Wrap call in closure so JIT can inline calls\n    const wrap = (a, b) => (c) => a(b(c));\n    // Construct chain of args[-1].encode(args[-2].encode([...]))\n    const encode = Array.from(args)\n        .reverse()\n        .reduce((acc, i) => (acc ? wrap(acc, i.encode) : i.encode), undefined);\n    // Construct chain of args[0].decode(args[1].decode(...))\n    const decode = args.reduce((acc, i) => (acc ? wrap(acc, i.decode) : i.decode), undefined);\n    return { encode, decode };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(alphabet) {\n    return {\n        encode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('alphabet.encode input should be an array of numbers');\n            return digits.map((i) => {\n                if (i < 0 || i >= alphabet.length)\n                    throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n                return alphabet[i];\n            });\n        },\n        decode: (input) => {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('alphabet.decode input should be array of strings');\n            return input.map((letter) => {\n                if (typeof letter !== 'string')\n                    throw new Error(`alphabet.decode: not string element=${letter}`);\n                const index = alphabet.indexOf(letter);\n                if (index === -1)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n                return index;\n            });\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n    if (typeof separator !== 'string')\n        throw new Error('join separator should be string');\n    return {\n        encode: (from) => {\n            if (!Array.isArray(from) || (from.length && typeof from[0] !== 'string'))\n                throw new Error('join.encode input should be array of strings');\n            for (let i of from)\n                if (typeof i !== 'string')\n                    throw new Error(`join.encode: non-string input=${i}`);\n            return from.join(separator);\n        },\n        decode: (to) => {\n            if (typeof to !== 'string')\n                throw new Error('join.decode input should be string');\n            return to.split(separator);\n        },\n    };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n    if (typeof chr !== 'string')\n        throw new Error('padding chr should be string');\n    return {\n        encode(data) {\n            if (!Array.isArray(data) || (data.length && typeof data[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of data)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.encode: non-string input=${i}`);\n            while ((data.length * bits) % 8)\n                data.push(chr);\n            return data;\n        },\n        decode(input) {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of input)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.decode: non-string input=${i}`);\n            let end = input.length;\n            if ((end * bits) % 8)\n                throw new Error('Invalid padding: string should have whole number of bytes');\n            for (; end > 0 && input[end - 1] === chr; end--) {\n                if (!(((end - 1) * bits) % 8))\n                    throw new Error('Invalid padding: string has too much padding');\n            }\n            return input.slice(0, end);\n        },\n    };\n}\n/**\n * Slow: O(n^2) time complexity\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix(data, from, to) {\n    // base 1 is impossible\n    if (from < 2)\n        throw new Error(`convertRadix: wrong from=${from}, base cannot be less than 2`);\n    if (to < 2)\n        throw new Error(`convertRadix: wrong to=${to}, base cannot be less than 2`);\n    if (!Array.isArray(data))\n        throw new Error('convertRadix: data should be array');\n    if (!data.length)\n        return [];\n    let pos = 0;\n    const res = [];\n    const digits = Array.from(data);\n    digits.forEach((d) => {\n        if (d < 0 || d >= from)\n            throw new Error(`Wrong integer: ${d}`);\n    });\n    while (true) {\n        let carry = 0;\n        let done = true;\n        for (let i = pos; i < digits.length; i++) {\n            const digit = digits[i];\n            const digitBase = from * carry + digit;\n            if (!Number.isSafeInteger(digitBase) ||\n                (from * carry) / from !== carry ||\n                digitBase - digit !== from * carry) {\n                throw new Error('convertRadix: carry overflow');\n            }\n            carry = digitBase % to;\n            const rounded = Math.floor(digitBase / to);\n            digits[i] = rounded;\n            if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n                throw new Error('convertRadix: carry overflow');\n            if (!done)\n                continue;\n            else if (!rounded)\n                pos = i;\n            else\n                done = false;\n        }\n        res.push(carry);\n        if (done)\n            break;\n    }\n    for (let i = 0; i < data.length - 1 && data[i] === 0; i++)\n        res.push(0);\n    return res.reverse();\n}\nconst gcd = /* @__NO_SIDE_EFFECTS__ */ (a, b) => (!b ? a : gcd(b, a % b));\nconst radix2carry = /*@__NO_SIDE_EFFECTS__ */ (from, to) => from + (to - gcd(from, to));\n/**\n * Implemented with numbers, because BigInt is 5x slower\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix2(data, from, to, padding) {\n    if (!Array.isArray(data))\n        throw new Error('convertRadix2: data should be array');\n    if (from <= 0 || from > 32)\n        throw new Error(`convertRadix2: wrong from=${from}`);\n    if (to <= 0 || to > 32)\n        throw new Error(`convertRadix2: wrong to=${to}`);\n    if (radix2carry(from, to) > 32) {\n        throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n    }\n    let carry = 0;\n    let pos = 0; // bitwise position in current element\n    const mask = 2 ** to - 1;\n    const res = [];\n    for (const n of data) {\n        if (n >= 2 ** from)\n            throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n        carry = (carry << from) | n;\n        if (pos + from > 32)\n            throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n        pos += from;\n        for (; pos >= to; pos -= to)\n            res.push(((carry >> (pos - to)) & mask) >>> 0);\n        carry &= 2 ** pos - 1; // clean carry, otherwise it will cause overflow\n    }\n    carry = (carry << (to - pos)) & mask;\n    if (!padding && pos >= from)\n        throw new Error('Excess padding');\n    if (!padding && carry)\n        throw new Error(`Non-zero padding: ${carry}`);\n    if (padding && pos > 0)\n        res.push(carry >>> 0);\n    return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix.encode input should be Uint8Array');\n            return convertRadix(Array.from(bytes), 2 ** 8, num);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix.decode input should be array of strings');\n            return Uint8Array.from(convertRadix(digits, num, 2 ** 8));\n        },\n    };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n    if (bits <= 0 || bits > 32)\n        throw new Error('radix2: bits should be in (0..32]');\n    if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n        throw new Error('radix2: carry overflow');\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix2.encode input should be Uint8Array');\n            return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix2.decode input should be array of strings');\n            return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction unsafeWrapper(fn) {\n    if (typeof fn !== 'function')\n        throw new Error('unsafeWrapper fn should be function');\n    return function (...args) {\n        try {\n            return fn.apply(null, args);\n        }\n        catch (e) { }\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction checksum(len, fn) {\n    if (typeof fn !== 'function')\n        throw new Error('checksum fn should be function');\n    return {\n        encode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.encode: input should be Uint8Array');\n            const checksum = fn(data).slice(0, len);\n            const res = new Uint8Array(data.length + len);\n            res.set(data);\n            res.set(checksum, data.length);\n            return res;\n        },\n        decode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.decode: input should be Uint8Array');\n            const payload = data.slice(0, -len);\n            const newChecksum = fn(payload).slice(0, len);\n            const oldChecksum = data.slice(-len);\n            for (let i = 0; i < len; i++)\n                if (newChecksum[i] !== oldChecksum[i])\n                    throw new Error('Invalid checksum');\n            return payload;\n        },\n    };\n}\nconst base64 = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\nconst base64url = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\n// base58 code\n// -----------\nconst genBase58 = (abc) => chain(radix(58), alphabet(abc), join(''));\nconst base58 = /* @__PURE__ */ genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\nconst base58check = /* @__PURE__ */ (sha256) => chain(checksum(4, (data) => sha256(sha256(data))), base58);\nconst BECH_ALPHABET = /* @__PURE__ */ chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bech32Polymod(pre) {\n    const b = pre >> 25;\n    let chk = (pre & 0x1ffffff) << 5;\n    for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n        if (((b >> i) & 1) === 1)\n            chk ^= POLYMOD_GENERATORS[i];\n    }\n    return chk;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n    const len = prefix.length;\n    let chk = 1;\n    for (let i = 0; i < len; i++) {\n        const c = prefix.charCodeAt(i);\n        if (c < 33 || c > 126)\n            throw new Error(`Invalid prefix (${prefix})`);\n        chk = bech32Polymod(chk) ^ (c >> 5);\n    }\n    chk = bech32Polymod(chk);\n    for (let i = 0; i < len; i++)\n        chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n    for (let v of words)\n        chk = bech32Polymod(chk) ^ v;\n    for (let i = 0; i < 6; i++)\n        chk = bech32Polymod(chk);\n    chk ^= encodingConst;\n    return BECH_ALPHABET.encode(convertRadix2([chk % 2 ** 30], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n    const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n    const _words = radix2(5);\n    const fromWords = _words.decode;\n    const toWords = _words.encode;\n    const fromWordsUnsafe = unsafeWrapper(fromWords);\n    function encode(prefix, words, limit = 90) {\n        if (typeof prefix !== 'string')\n            throw new Error(`bech32.encode prefix should be string, not ${typeof prefix}`);\n        if (!Array.isArray(words) || (words.length && typeof words[0] !== 'number'))\n            throw new Error(`bech32.encode words should be array of numbers, not ${typeof words}`);\n        const actualLength = prefix.length + 7 + words.length;\n        if (limit !== false && actualLength > limit)\n            throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n        const lowered = prefix.toLowerCase();\n        const sum = bechChecksum(lowered, words, ENCODING_CONST);\n        return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n    }\n    function decode(str, limit = 90) {\n        if (typeof str !== 'string')\n            throw new Error(`bech32.decode input should be string, not ${typeof str}`);\n        if (str.length < 8 || (limit !== false && str.length > limit))\n            throw new TypeError(`Wrong string length: ${str.length} (${str}). Expected (8..${limit})`);\n        // don't allow mixed case\n        const lowered = str.toLowerCase();\n        if (str !== lowered && str !== str.toUpperCase())\n            throw new Error(`String must be lowercase or uppercase`);\n        str = lowered;\n        const sepIndex = str.lastIndexOf('1');\n        if (sepIndex === 0 || sepIndex === -1)\n            throw new Error(`Letter \"1\" must be present between prefix and data only`);\n        const prefix = str.slice(0, sepIndex);\n        const _words = str.slice(sepIndex + 1);\n        if (_words.length < 6)\n            throw new Error('Data must be at least 6 characters long');\n        const words = BECH_ALPHABET.decode(_words).slice(0, -6);\n        const sum = bechChecksum(prefix, words, ENCODING_CONST);\n        if (!_words.endsWith(sum))\n            throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n        return { prefix, words };\n    }\n    const decodeUnsafe = unsafeWrapper(decode);\n    function decodeToBytes(str) {\n        const { prefix, words } = decode(str, false);\n        return { prefix, words, bytes: fromWords(words) };\n    }\n    return { encode, decode, decodeToBytes, decodeUnsafe, fromWords, fromWordsUnsafe, toWords };\n}\nconst bech32 = /* @__PURE__ */ genBech32('bech32');\nconst bech32m = /* @__PURE__ */ genBech32('bech32m');\n\nconst Encoder = {\n    b58chk: {\n        encode: (data) => base58check(sha256).encode(data),\n        decode: (data) => base58check(sha256).decode(data)\n    },\n    base64: {\n        encode: (data) => base64.encode(data),\n        decode: (data) => base64.decode(data)\n    },\n    b64url: {\n        encode: (data) => base64url.encode(data),\n        decode: (data) => base64url.decode(data)\n    },\n    bech32: {\n        to_words: bech32.toWords,\n        to_bytes: bech32.fromWords,\n        encode: (prefix, words, limit = false) => {\n            return bech32.encode(prefix, words, limit);\n        },\n        decode: (data, limit = false) => {\n            const { prefix, words } = bech32.decode(data, limit);\n            return { prefix, words };\n        }\n    },\n    bech32m: {\n        to_words: bech32m.toWords,\n        to_bytes: bech32m.fromWords,\n        encode: (prefix, words, limit = false) => {\n            return bech32m.encode(prefix, words, limit);\n        },\n        decode: (data, limit = false) => {\n            const { prefix, words } = bech32m.decode(data, limit);\n            return { prefix, words };\n        }\n    }\n};\n\nfunction within_size(data, size) {\n    if (data.length > size) {\n        throw new TypeError(`Data is larger than array size: ${data.length} > ${size}`);\n    }\n}\nfunction is_hex$1(hex) {\n    if (hex.match(/[^a-fA-f0-9]/) !== null) {\n        throw new TypeError('Invalid characters in hex string: ' + hex);\n    }\n    if (hex.length % 2 !== 0) {\n        throw new Error(`Length of hex string is invalid: ${hex.length}`);\n    }\n}\nfunction is_json(str) {\n    try {\n        JSON.parse(str);\n    }\n    catch {\n        throw new TypeError('JSON string is invalid!');\n    }\n}\nfunction is_safe_num(num) {\n    if (num > Number.MAX_SAFE_INTEGER) {\n        throw new TypeError('Number exceeds safe bounds!');\n    }\n}\nfunction is_prefix(actual, target) {\n    if (actual !== target) {\n        throw new TypeError(`Bech32 prefix does not match: ${actual} !== ${target}`);\n    }\n}\n\nvar assert = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    is_hex: is_hex$1,\n    is_json: is_json,\n    is_prefix: is_prefix,\n    is_safe_num: is_safe_num,\n    within_size: within_size\n});\n\nconst _0n = BigInt(0);\nconst _255n = BigInt(255);\nconst _256n = BigInt(256);\nfunction big_size(big) {\n    if (big <= 0xffn)\n        return 1;\n    if (big <= 0xffffn)\n        return 2;\n    if (big <= 0xffffffffn)\n        return 4;\n    if (big <= 0xffffffffffffffffn)\n        return 8;\n    if (big <= 0xffffffffffffffffffffffffffffffffn)\n        return 16;\n    if (big <= 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn) {\n        return 32;\n    }\n    throw new TypeError('Must specify a fixed buffer size for bigints greater than 32 bytes.');\n}\nfunction bigToBytes(big, size, endian = 'be') {\n    if (size === undefined)\n        size = big_size(big);\n    const use_le = (endian === 'le');\n    const buffer = new ArrayBuffer(size);\n    const dataView = new DataView(buffer);\n    let offset = (use_le) ? 0 : size - 1;\n    while (big > _0n) {\n        const byte = big & _255n;\n        const num = Number(byte);\n        if (use_le) {\n            dataView.setUint8(offset++, num);\n        }\n        else {\n            dataView.setUint8(offset--, num);\n        }\n        big = (big - byte) / _256n;\n    }\n    return new Uint8Array(buffer);\n}\nfunction bytesToBig(bytes) {\n    let num = BigInt(0);\n    for (let i = bytes.length - 1; i >= 0; i--) {\n        num = (num * _256n) + BigInt(bytes[i]);\n    }\n    return BigInt(num);\n}\n\nfunction binToBytes(binary) {\n    const bins = binary.split('').map(Number);\n    if (bins.length % 8 !== 0) {\n        throw new Error(`Binary array is invalid length: ${binary.length}`);\n    }\n    const bytes = new Uint8Array(bins.length / 8);\n    for (let i = 0, ct = 0; i < bins.length; i += 8, ct++) {\n        let byte = 0;\n        for (let j = 0; j < 8; j++) {\n            byte |= (bins[i + j] << (7 - j));\n        }\n        bytes[ct] = byte;\n    }\n    return bytes;\n}\nfunction bytesToBin(bytes) {\n    const bin = new Array(bytes.length * 8);\n    let count = 0;\n    for (const num of bytes) {\n        if (num > 255) {\n            throw new Error(`Invalid byte value: ${num}. Byte values must be between 0 and 255.`);\n        }\n        for (let i = 7; i >= 0; i--, count++) {\n            bin[count] = (num >> i) & 1;\n        }\n    }\n    return bin.join('');\n}\n\nfunction num_size(num) {\n    if (num <= 0xFF)\n        return 1;\n    if (num <= 0xFFFF)\n        return 2;\n    if (num <= 0xFFFFFFFF)\n        return 4;\n    throw new TypeError('Numbers larger than 4 bytes must specify a fixed size!');\n}\nfunction numToBytes(num, size, endian = 'be') {\n    if (size === undefined)\n        size = num_size(num);\n    const use_le = (endian === 'le');\n    const buffer = new ArrayBuffer(size);\n    const dataView = new DataView(buffer);\n    let offset = (use_le) ? 0 : size - 1;\n    while (num > 0) {\n        const byte = num & 255;\n        if (use_le) {\n            dataView.setUint8(offset++, num);\n        }\n        else {\n            dataView.setUint8(offset--, num);\n        }\n        num = (num - byte) / 256;\n    }\n    return new Uint8Array(buffer);\n}\nfunction bytesToNum(bytes) {\n    let num = 0;\n    for (let i = bytes.length - 1; i >= 0; i--) {\n        num = (num * 256) + bytes[i];\n        is_safe_num(num);\n    }\n    return num;\n}\n\nconst ec = new TextEncoder();\nconst dc = new TextDecoder();\nfunction strToBytes(str) {\n    return ec.encode(str);\n}\nfunction bytesToStr(bytes) {\n    return dc.decode(bytes);\n}\nfunction hex_size(hexstr, size) {\n    is_hex$1(hexstr);\n    const len = hexstr.length / 2;\n    if (size === undefined)\n        size = len;\n    if (len > size) {\n        throw new TypeError(`Hex string is larger than array size: ${len} > ${size}`);\n    }\n    return size;\n}\nfunction hexToBytes(hexstr, size, endian = 'le') {\n    size = hex_size(hexstr, size);\n    const use_le = (endian === 'le');\n    const buffer = new ArrayBuffer(size);\n    const dataView = new DataView(buffer);\n    let offset = (use_le) ? 0 : size - 1;\n    for (let i = 0; i < hexstr.length; i += 2) {\n        const char = hexstr.substring(i, i + 2);\n        const num = parseInt(char, 16);\n        if (use_le) {\n            dataView.setUint8(offset++, num);\n        }\n        else {\n            dataView.setUint8(offset--, num);\n        }\n    }\n    return new Uint8Array(buffer);\n}\nfunction bytesToHex(bytes) {\n    let chars = '';\n    for (let i = 0; i < bytes.length; i++) {\n        chars += bytes[i].toString(16).padStart(2, '0');\n    }\n    return chars;\n}\nconst Hex = {\n    encode: bytesToHex,\n    decode: hexToBytes\n};\nconst Txt = {\n    encode: strToBytes,\n    decode: bytesToStr\n};\n\nconst { getRandomValues } = crypto ?? globalThis.crypto ?? window.crypto;\nfunction random(size = 32) {\n    if (typeof getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(size));\n    }\n    throw new Error('Crypto module missing getRandomValues!');\n}\nfunction is_hex(input) {\n    if (input.match(/[^a-fA-f0-9]/) === null &&\n        input.length % 2 === 0) {\n        return true;\n    }\n    return false;\n}\nfunction is_bytes(input) {\n    if (typeof input === 'string' &&\n        is_hex(input)) {\n        return true;\n    }\n    else if (typeof input === 'number' ||\n        typeof input === 'bigint' ||\n        input instanceof Uint8Array) {\n        return true;\n    }\n    else if (Array.isArray(input) &&\n        input.every(e => typeof e === 'number')) {\n        return true;\n    }\n    else {\n        return false;\n    }\n}\nfunction set_buffer(data, size, endian = 'be') {\n    if (size === undefined)\n        size = data.length;\n    within_size(data, size);\n    const buffer = new Uint8Array(size).fill(0);\n    const offset = (endian === 'be') ? 0 : size - data.length;\n    buffer.set(data, offset);\n    return buffer;\n}\nfunction join_array(arr) {\n    let i, offset = 0;\n    const size = arr.reduce((len, arr) => len + arr.length, 0);\n    const buff = new Uint8Array(size);\n    for (i = 0; i < arr.length; i++) {\n        const a = arr[i];\n        buff.set(a, offset);\n        offset += a.length;\n    }\n    return buff;\n}\nfunction bigint_replacer(_, v) {\n    return typeof v === 'bigint'\n        ? `${v}n`\n        : v;\n}\nfunction bigint_reviver(_, v) {\n    return typeof v === 'string' && /n$/.test(v)\n        ? BigInt(v.slice(0, -1))\n        : v;\n}\nfunction parse_data$1(data_blob, chunk_size, total_size) {\n    const len = data_blob.length, count = total_size / chunk_size;\n    if (total_size % chunk_size !== 0) {\n        throw new TypeError(`Invalid parameters: ${total_size} % ${chunk_size} !== 0`);\n    }\n    if (len !== total_size) {\n        throw new TypeError(`Invalid data stream: ${len} !== ${total_size}`);\n    }\n    if (len % chunk_size !== 0) {\n        throw new TypeError(`Invalid data stream: ${len} % ${chunk_size} !== 0`);\n    }\n    const chunks = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const idx = i * chunk_size;\n        chunks[i] = data_blob.subarray(idx, idx + chunk_size);\n    }\n    return chunks;\n}\n\nvar utils = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    bigint_replacer: bigint_replacer,\n    bigint_reviver: bigint_reviver,\n    is_bytes: is_bytes,\n    is_hex: is_hex,\n    join_array: join_array,\n    parse_data: parse_data$1,\n    random: random,\n    set_buffer: set_buffer\n});\n\nfunction buffer_data(data, size, endian) {\n    if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else if (data instanceof Uint8Array) {\n        return set_buffer(data, size, endian);\n    }\n    else if (Array.isArray(data)) {\n        const bytes = data.map(e => buffer_data(e, size, endian));\n        return join_array(bytes);\n    }\n    else if (typeof data === 'string') {\n        return hexToBytes(data, size, endian);\n    }\n    else if (typeof data === 'bigint') {\n        return bigToBytes(data, size, endian);\n    }\n    else if (typeof data === 'number') {\n        return numToBytes(data, size, endian);\n    }\n    else if (typeof data === 'boolean') {\n        return Uint8Array.of(data ? 1 : 0);\n    }\n    throw new TypeError('Unsupported format:' + String(typeof data));\n}\n\nclass Buff extends Uint8Array {\n    static { this.num = numToBuff; }\n    static { this.big = bigToBuff; }\n    static { this.bin = binToBuff; }\n    static { this.raw = rawToBuff; }\n    static { this.str = strToBuff; }\n    static { this.hex = hexToBuff; }\n    static { this.bytes = buffer; }\n    static { this.json = jsonToBuff; }\n    static { this.base64 = base64ToBuff; }\n    static { this.b64url = b64urlToBuff; }\n    static { this.bech32 = bech32ToBuff; }\n    static { this.bech32m = bech32mToBuff; }\n    static { this.b58chk = b58chkToBuff; }\n    static { this.encode = strToBytes; }\n    static { this.decode = bytesToStr; }\n    static { this.parse = parse_data; }\n    static { this.is_bytes = is_bytes; }\n    static { this.is_hex = is_hex; }\n    static random(size = 32) {\n        const rand = random(size);\n        return new Buff(rand, size);\n    }\n    static now(size = 4) {\n        const stamp = Math.floor(Date.now() / 1000);\n        return new Buff(stamp, size);\n    }\n    constructor(data, size, endian) {\n        if (data instanceof Buff &&\n            size === undefined) {\n            return data;\n        }\n        const buffer = buffer_data(data, size, endian);\n        super(buffer);\n    }\n    get arr() {\n        return [...this];\n    }\n    get num() {\n        return this.to_num();\n    }\n    get big() {\n        return this.to_big();\n    }\n    get str() {\n        return this.to_str();\n    }\n    get hex() {\n        return this.to_hex();\n    }\n    get raw() {\n        return new Uint8Array(this);\n    }\n    get bin() {\n        return this.to_bin();\n    }\n    get b58chk() {\n        return this.to_b58chk();\n    }\n    get base64() {\n        return this.to_base64();\n    }\n    get b64url() {\n        return this.to_b64url();\n    }\n    get digest() {\n        return this.to_hash();\n    }\n    get id() {\n        return this.to_hash().hex;\n    }\n    get stream() {\n        return new Stream(this);\n    }\n    to_num(endian = 'be') {\n        const bytes = (endian === 'be')\n            ? this.reverse()\n            : this;\n        return bytesToNum(bytes);\n    }\n    to_big(endian = 'be') {\n        const bytes = (endian === 'be')\n            ? this.reverse()\n            : this;\n        return bytesToBig(bytes);\n    }\n    to_bin() {\n        return bytesToBin(this);\n    }\n    to_hash() {\n        const digest = sha256(this);\n        return new Buff(digest);\n    }\n    to_json(reviver) {\n        if (reviver === undefined) {\n            reviver = bigint_reviver;\n        }\n        const str = bytesToStr(this);\n        return JSON.parse(str, reviver);\n    }\n    to_bech32(prefix, limit) {\n        const { encode, to_words } = Encoder.bech32;\n        const words = to_words(this);\n        return encode(prefix, words, limit);\n    }\n    to_bech32m(prefix, limit) {\n        const { encode, to_words } = Encoder.bech32m;\n        const words = to_words(this);\n        return encode(prefix, words, limit);\n    }\n    to_str() { return bytesToStr(this); }\n    to_hex() { return bytesToHex(this); }\n    to_bytes() { return new Uint8Array(this); }\n    to_b58chk() { return Encoder.b58chk.encode(this); }\n    to_base64() { return Encoder.base64.encode(this); }\n    to_b64url() { return Encoder.b64url.encode(this); }\n    append(data) {\n        return Buff.join([this, Buff.bytes(data)]);\n    }\n    prepend(data) {\n        return Buff.join([Buff.bytes(data), this]);\n    }\n    reverse() {\n        const arr = new Uint8Array(this).reverse();\n        return new Buff(arr);\n    }\n    slice(start, end) {\n        const arr = new Uint8Array(this).slice(start, end);\n        return new Buff(arr);\n    }\n    set(array, offset) {\n        this.set(array, offset);\n    }\n    subarray(begin, end) {\n        const arr = new Uint8Array(this).subarray(begin, end);\n        return new Buff(arr);\n    }\n    write(bytes, offset) {\n        const b = Buff.bytes(bytes);\n        this.set(b, offset);\n    }\n    add_varint(endian) {\n        const size = Buff.calc_varint(this.length, endian);\n        return Buff.join([size, this]);\n    }\n    static from(data) {\n        return new Buff(Uint8Array.from(data));\n    }\n    static of(...args) {\n        return new Buff(Uint8Array.of(...args));\n    }\n    static join(arr) {\n        const bytes = arr.map(e => Buff.bytes(e));\n        const joined = join_array(bytes);\n        return new Buff(joined);\n    }\n    static sort(arr, size) {\n        const hex = arr.map(e => buffer(e, size).hex);\n        hex.sort();\n        return hex.map(e => Buff.hex(e, size));\n    }\n    static calc_varint(num, endian) {\n        if (num < 0xFD) {\n            return Buff.num(num, 1);\n        }\n        else if (num < 0x10000) {\n            return Buff.of(0xFD, ...Buff.num(num, 2, endian));\n        }\n        else if (num < 0x100000000) {\n            return Buff.of(0xFE, ...Buff.num(num, 4, endian));\n        }\n        else if (BigInt(num) < 0x10000000000000000n) {\n            return Buff.of(0xFF, ...Buff.num(num, 8, endian));\n        }\n        else {\n            throw new Error(`Value is too large: ${num}`);\n        }\n    }\n}\nfunction numToBuff(number, size, endian) {\n    return new Buff(number, size, endian);\n}\nfunction binToBuff(data, size, endian) {\n    return new Buff(binToBytes(data), size, endian);\n}\nfunction bigToBuff(bigint, size, endian) {\n    return new Buff(bigint, size, endian);\n}\nfunction rawToBuff(data, size, endian) {\n    return new Buff(data, size, endian);\n}\nfunction strToBuff(data, size, endian) {\n    return new Buff(strToBytes(data), size, endian);\n}\nfunction hexToBuff(data, size, endian) {\n    return new Buff(data, size, endian);\n}\nfunction jsonToBuff(data, replacer) {\n    if (replacer === undefined) {\n        replacer = bigint_replacer;\n    }\n    const str = JSON.stringify(data, replacer);\n    return new Buff(strToBytes(str));\n}\nfunction base64ToBuff(data) {\n    return new Buff(Encoder.base64.decode(data));\n}\nfunction b64urlToBuff(data) {\n    return new Buff(Encoder.b64url.decode(data));\n}\nfunction bech32ToBuff(data, limit, chk_prefix) {\n    const { decode, to_bytes } = Encoder.bech32;\n    const { prefix, words } = decode(data, limit);\n    const bytes = to_bytes(words);\n    if (typeof chk_prefix === 'string') {\n        is_prefix(prefix, chk_prefix);\n    }\n    return new Buff(bytes);\n}\nfunction bech32mToBuff(data, limit, chk_prefix) {\n    const { decode, to_bytes } = Encoder.bech32m;\n    const { prefix, words } = decode(data, limit);\n    const bytes = to_bytes(words);\n    if (typeof chk_prefix === 'string') {\n        is_prefix(prefix, chk_prefix);\n    }\n    return new Buff(bytes);\n}\nfunction b58chkToBuff(data) {\n    return new Buff(Encoder.b58chk.decode(data));\n}\nfunction parse_data(data_blob, chunk_size, total_size) {\n    const bytes = buffer_data(data_blob);\n    const chunks = parse_data$1(bytes, chunk_size, total_size);\n    return chunks.map(e => Buff.bytes(e));\n}\nclass Stream {\n    constructor(data) {\n        this.data = Buff.bytes(data);\n        this.size = this.data.length;\n    }\n    peek(size) {\n        if (size > this.size) {\n            throw new Error(`Size greater than stream: ${size} > ${this.size}`);\n        }\n        return new Buff(this.data.slice(0, size));\n    }\n    read(size) {\n        const chunk = this.peek(size);\n        this.data = this.data.slice(size);\n        this.size = this.data.length;\n        return chunk;\n    }\n    read_varint(endian) {\n        const num = this.read(1).num;\n        switch (true) {\n            case (num >= 0 && num < 0xFD):\n                return num;\n            case (num === 0xFD):\n                return this.read(2).to_num(endian);\n            case (num === 0xFE):\n                return this.read(4).to_num(endian);\n            case (num === 0xFF):\n                return this.read(8).to_num(endian);\n            default:\n                throw new Error(`Varint is out of range: ${num}`);\n        }\n    }\n}\nfunction buffer(bytes, size, end) {\n    return new Buff(bytes, size, end);\n}\n\nexport { Buff, Encoder, Hex, Stream, Txt, assert, buffer, utils as util };\n//# sourceMappingURL=module.mjs.map\n", null, null, null, null, "const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for (let i = 0; i < lst.length; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\nexport { fromBig, split, toBig, shrSH, shrSL, rotrSH, rotrSL, rotrBH, rotrBL, rotr32H, rotr32L, rotlSH, rotlSL, rotlBH, rotlBL, add, add3L, add3H, add4L, add4H, add5H, add5L, };\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n//# sourceMappingURL=_u64.js.map", "import { SHA2 } from './_sha2.js';\nimport u64 from './_u64.js';\nimport { wrapConstructor } from './utils.js';\n// Round contants (first 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409):\n// prettier-ignore\nconst [SHA512_Kh, SHA512_Kl] = /* @__PURE__ */ (() => u64.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\n// Temporary buffer, not used to store anything between runs\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nexport class SHA512 extends SHA2 {\n    constructor() {\n        super(128, 64, 16, false);\n        // We cannot use array here since array allows indexing by variable which means optimizer/compiler cannot use registers.\n        // Also looks cleaner and easier to verify with spec.\n        // Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x6a09e667 | 0;\n        this.Al = 0xf3bcc908 | 0;\n        this.Bh = 0xbb67ae85 | 0;\n        this.Bl = 0x84caa73b | 0;\n        this.Ch = 0x3c6ef372 | 0;\n        this.Cl = 0xfe94f82b | 0;\n        this.Dh = 0xa54ff53a | 0;\n        this.Dl = 0x5f1d36f1 | 0;\n        this.Eh = 0x510e527f | 0;\n        this.El = 0xade682d1 | 0;\n        this.Fh = 0x9b05688c | 0;\n        this.Fl = 0x2b3e6c1f | 0;\n        this.Gh = 0x1f83d9ab | 0;\n        this.Gl = 0xfb41bd6b | 0;\n        this.Hh = 0x5be0cd19 | 0;\n        this.Hl = 0x137e2179 | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n            const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n            const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n            const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n            const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = u64.add3L(T1l, sigma0l, MAJl);\n            Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        SHA512_W_H.fill(0);\n        SHA512_W_L.fill(0);\n    }\n    destroy() {\n        this.buffer.fill(0);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nclass SHA512_224 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x8c3d37c8 | 0;\n        this.Al = 0x19544da2 | 0;\n        this.Bh = 0x73e19966 | 0;\n        this.Bl = 0x89dcd4d6 | 0;\n        this.Ch = 0x1dfab7ae | 0;\n        this.Cl = 0x32ff9c82 | 0;\n        this.Dh = 0x679dd514 | 0;\n        this.Dl = 0x582f9fcf | 0;\n        this.Eh = 0x0f6d2b69 | 0;\n        this.El = 0x7bd44da8 | 0;\n        this.Fh = 0x77e36f73 | 0;\n        this.Fl = 0x04c48942 | 0;\n        this.Gh = 0x3f9d85a8 | 0;\n        this.Gl = 0x6a1d36c8 | 0;\n        this.Hh = 0x1112e6ad | 0;\n        this.Hl = 0x91d692a1 | 0;\n        this.outputLen = 28;\n    }\n}\nclass SHA512_256 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x22312194 | 0;\n        this.Al = 0xfc2bf72c | 0;\n        this.Bh = 0x9f555fa3 | 0;\n        this.Bl = 0xc84c64c2 | 0;\n        this.Ch = 0x2393b86b | 0;\n        this.Cl = 0x6f53b151 | 0;\n        this.Dh = 0x96387719 | 0;\n        this.Dl = 0x5940eabd | 0;\n        this.Eh = 0x96283ee2 | 0;\n        this.El = 0xa88effe3 | 0;\n        this.Fh = 0xbe5e1e25 | 0;\n        this.Fl = 0x53863992 | 0;\n        this.Gh = 0x2b0199fc | 0;\n        this.Gl = 0x2c85b8aa | 0;\n        this.Hh = 0x0eb72ddc | 0;\n        this.Hl = 0x81c52ca2 | 0;\n        this.outputLen = 32;\n    }\n}\nclass SHA384 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0xcbbb9d5d | 0;\n        this.Al = 0xc1059ed8 | 0;\n        this.Bh = 0x629a292a | 0;\n        this.Bl = 0x367cd507 | 0;\n        this.Ch = 0x9159015a | 0;\n        this.Cl = 0x3070dd17 | 0;\n        this.Dh = 0x152fecd8 | 0;\n        this.Dl = 0xf70e5939 | 0;\n        this.Eh = 0x67332667 | 0;\n        this.El = 0xffc00b31 | 0;\n        this.Fh = 0x8eb44a87 | 0;\n        this.Fl = 0x68581511 | 0;\n        this.Gh = 0xdb0c2e0d | 0;\n        this.Gl = 0x64f98fa7 | 0;\n        this.Hh = 0x47b5481d | 0;\n        this.Hl = 0xbefa4fa4 | 0;\n        this.outputLen = 48;\n    }\n}\nexport const sha512 = /* @__PURE__ */ wrapConstructor(() => new SHA512());\nexport const sha512_224 = /* @__PURE__ */ wrapConstructor(() => new SHA512_224());\nexport const sha512_256 = /* @__PURE__ */ wrapConstructor(() => new SHA512_256());\nexport const sha384 = /* @__PURE__ */ wrapConstructor(() => new SHA384());\n//# sourceMappingURL=sha512.js.map", "import { SHA2 } from './_sha2.js';\nimport { wrapConstructor } from './utils.js';\n// https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n// https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\nconst Rho = /* @__PURE__ */ new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);\nconst Id = /* @__PURE__ */ Uint8Array.from({ length: 16 }, (_, i) => i);\nconst Pi = /* @__PURE__ */ Id.map((i) => (9 * i + 5) % 16);\nlet idxL = [Id];\nlet idxR = [Pi];\nfor (let i = 0; i < 4; i++)\n    for (let j of [idxL, idxR])\n        j.push(j[i].map((k) => Rho[k]));\nconst shifts = /* @__PURE__ */ [\n    [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n    [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n    [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n    [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n    [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => new Uint8Array(i));\nconst shiftsL = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst shiftsR = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst Kl = /* @__PURE__ */ new Uint32Array([\n    0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr = /* @__PURE__ */ new Uint32Array([\n    0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// The rotate left (circular left shift) operation for uint32\nconst rotl = (word, shift) => (word << shift) | (word >>> (32 - shift));\n// It's called f() in spec.\nfunction f(group, x, y, z) {\n    if (group === 0)\n        return x ^ y ^ z;\n    else if (group === 1)\n        return (x & y) | (~x & z);\n    else if (group === 2)\n        return (x | ~y) ^ z;\n    else if (group === 3)\n        return (x & z) | (y & ~z);\n    else\n        return x ^ (y | ~z);\n}\n// Temporary buffer, not used to store anything between runs\nconst BUF = /* @__PURE__ */ new Uint32Array(16);\nexport class RIPEMD160 extends SHA2 {\n    constructor() {\n        super(64, 20, 8, true);\n        this.h0 = 0x67452301 | 0;\n        this.h1 = 0xefcdab89 | 0;\n        this.h2 = 0x98badcfe | 0;\n        this.h3 = 0x10325476 | 0;\n        this.h4 = 0xc3d2e1f0 | 0;\n    }\n    get() {\n        const { h0, h1, h2, h3, h4 } = this;\n        return [h0, h1, h2, h3, h4];\n    }\n    set(h0, h1, h2, h3, h4) {\n        this.h0 = h0 | 0;\n        this.h1 = h1 | 0;\n        this.h2 = h2 | 0;\n        this.h3 = h3 | 0;\n        this.h4 = h4 | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            BUF[i] = view.getUint32(offset, true);\n        // prettier-ignore\n        let al = this.h0 | 0, ar = al, bl = this.h1 | 0, br = bl, cl = this.h2 | 0, cr = cl, dl = this.h3 | 0, dr = dl, el = this.h4 | 0, er = el;\n        // Instead of iterating 0 to 80, we split it into 5 groups\n        // And use the groups in constants, functions, etc. Much simpler\n        for (let group = 0; group < 5; group++) {\n            const rGroup = 4 - group;\n            const hbl = Kl[group], hbr = Kr[group]; // prettier-ignore\n            const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n            const sl = shiftsL[group], sr = shiftsR[group]; // prettier-ignore\n            for (let i = 0; i < 16; i++) {\n                const tl = (rotl(al + f(group, bl, cl, dl) + BUF[rl[i]] + hbl, sl[i]) + el) | 0;\n                al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n            }\n            // 2 loops are 10% faster\n            for (let i = 0; i < 16; i++) {\n                const tr = (rotl(ar + f(rGroup, br, cr, dr) + BUF[rr[i]] + hbr, sr[i]) + er) | 0;\n                ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n            }\n        }\n        // Add the compressed chunk to the current hash value\n        this.set((this.h1 + cl + dr) | 0, (this.h2 + dl + er) | 0, (this.h3 + el + ar) | 0, (this.h4 + al + br) | 0, (this.h0 + bl + cr) | 0);\n    }\n    roundClean() {\n        BUF.fill(0);\n    }\n    destroy() {\n        this.destroyed = true;\n        this.buffer.fill(0);\n        this.set(0, 0, 0, 0, 0);\n    }\n}\n/**\n * RIPEMD-160 - a hash function from 1990s.\n * @param message - msg that would be hashed\n */\nexport const ripemd160 = /* @__PURE__ */ wrapConstructor(() => new RIPEMD160());\n//# sourceMappingURL=ripemd160.js.map", null, null, null, null, null, null], "names": ["bytes", "hash", "exists", "output", "crypto", "u8a", "createView", "rotr", "isLE", "utf8ToBytes", "toBytes", "concatBytes", "wrapConstructor", "setBigUint64", "Hash", "<PERSON>", "Maj", "SHA256_K", "IV", "SHA256_W", "SHA2", "sha256", "SHA256", "_0n", "_1n", "_2n", "bytesToHex", "hexToBytes", "_3n", "_4n", "Field", "ut.validateObject", "ut.concatBytes", "ut.bytesToHex", "ut.bytesToNumberBE", "mod.mod", "mod.invert", "ut.numberToBytesBE", "ut.hexToBytes", "mod.getMin<PERSON>ash<PERSON><PERSON><PERSON>", "mod.mapHashToField", "ut.bitMask", "ut.createHmacDrbg", "assertHash", "assertExists", "assertBytes", "modP", "modN", "Point", "lift_x", "random", "on_curve", "NFD", "assert.in_field", "math.modN", "add", "u64", "sha512", "s256", "assert.valid_chain", "util.stringify"], "mappings": ";;AAAA,SAAS,MAAM,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AACzC,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC;AAKD,SAASA,OAAK,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE;AAC9B,IAAI,IAAI,EAAE,CAAC,YAAY,UAAU,CAAC;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC/C,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;AACzD,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/F,CAAC;AACD,SAASC,MAAI,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;AACvE,QAAQ,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AAC3E,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1B,CAAC;AACD,SAASC,QAAM,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAI,EAAE;AAChD,IAAI,IAAI,QAAQ,CAAC,SAAS;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,IAAI,IAAI,aAAa,IAAI,QAAQ,CAAC,QAAQ;AAC1C,QAAQ,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACjE,CAAC;AACD,SAASC,QAAM,CAAC,GAAG,EAAE,QAAQ,EAAE;AAC/B,IAAIH,OAAK,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;AACnC,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,sDAAsD,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxF,KAAK;AACL;;AChCO,MAAMI,QAAM,GAAG,OAAO,UAAU,KAAK,QAAQ,IAAI,QAAQ,IAAI,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,SAAS;;ACA9G;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMC,KAAG,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU,CAAC;AAI3C;AACO,MAAMC,YAAU,GAAG,CAAC,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAC5F;AACO,MAAMC,MAAI,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAC/E;AACA;AACO,MAAMC,MAAI,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACrF,IAAI,CAACA,MAAI;AACT,IAAI,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAoDnE;AACA;AACA;AACO,SAASC,aAAW,CAAC,GAAG,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAASC,SAAO,CAAC,IAAI,EAAE;AAC9B,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;AAChC,QAAQ,IAAI,GAAGD,aAAW,CAAC,IAAI,CAAC,CAAC;AACjC,IAAI,IAAI,CAACJ,KAAG,CAAC,IAAI,CAAC;AAClB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD;AACA;AACA;AACO,SAASM,aAAW,CAAC,GAAG,MAAM,EAAE;AACvC,IAAI,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AAC1B,QAAQ,IAAI,CAACN,KAAG,CAAC,CAAC,CAAC;AACnB,YAAY,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AACnD,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,QAAQ,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD;aACO,MAAM,IAAI,CAAC;AAClB;AACA,IAAI,KAAK,GAAG;AACZ,QAAQ,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;AACjC,KAAK;AACL,EAAC;AAQM,SAASO,iBAAe,CAAC,QAAQ,EAAE;AAC1C,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC,MAAM,CAACF,SAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACpE,IAAI,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC;AAC3B,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AACpC,IAAI,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAClC,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,QAAQ,EAAE,CAAC;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AAiBD;AACA;AACA;AACO,SAAS,WAAW,CAAC,WAAW,GAAG,EAAE,EAAE;AAC9C,IAAI,IAAIN,QAAM,IAAI,OAAOA,QAAM,CAAC,eAAe,KAAK,UAAU,EAAE;AAChE,QAAQ,OAAOA,QAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;AACnE,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC9D;;ACtJA;AACA,SAASS,cAAY,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;AACrD,IAAI,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU;AAC/C,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1D,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAClD,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,CAAC;AACD;aACO,MAAM,IAAI,SAASC,MAAI,CAAC;AAC/B,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;AACtD,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACrB,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,IAAI,GAAGR,YAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,EAAE;AACjB,QAAQJ,QAAM,CAAC,IAAI,CAAC,CAAC;AACrB,QAAQ,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAChD,QAAQ,IAAI,GAAGQ,SAAO,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC,QAAQ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AACtC,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,YAAY,IAAI,IAAI,KAAK,QAAQ,EAAE;AACnC,gBAAgB,MAAM,QAAQ,GAAGJ,YAAU,CAAC,IAAI,CAAC,CAAC;AAClD,gBAAgB,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,QAAQ;AAC7D,oBAAoB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AAChD,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACjE,YAAY,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;AAC7B,YAAY,GAAG,IAAI,IAAI,CAAC;AACxB,YAAY,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE;AACvC,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,gBAAgB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB,QAAQJ,QAAM,CAAC,IAAI,CAAC,CAAC;AACrB,QAAQC,QAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AACtD,QAAQ,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3B;AACA,QAAQ,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA,QAAQ,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,GAAG,EAAE;AAC7C,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAClC,YAAY,GAAG,GAAG,CAAC,CAAC;AACpB,SAAS;AACT;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE;AAC3C,YAAY,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B;AACA;AACA;AACA,QAAQU,cAAY,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACxE,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC9B,QAAQ,MAAM,KAAK,GAAGP,YAAU,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC;AACnB,YAAY,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC3E,QAAQ,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;AAC/B,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAClE,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;AACvC,YAAY,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,GAAG;AACb,QAAQ,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;AAC3C,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,UAAU,CAAC,EAAE,EAAE;AACnB,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAC5C,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B,QAAQ,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC5E,QAAQ,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,QAAQ,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,QAAQ;AAC7B,YAAY,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL;;AC9GA;AACA;AACA;AACA,MAAMS,KAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C;AACA,MAAMC,KAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA,MAAMC,UAAQ,mBAAmB,IAAI,WAAW,CAAC;AACjD,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,CAAC,CAAC,CAAC;AACH;AACA;AACA,MAAMC,IAAE,mBAAmB,IAAI,WAAW,CAAC;AAC3C,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,CAAC,CAAC,CAAC;AACH;AACA;AACA,MAAMC,UAAQ,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;eACrD,MAAM,MAAM,SAASC,MAAI,CAAC;AAC1B,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAChC;AACA;AACA,QAAQ,IAAI,CAAC,CAAC,GAAGF,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAGA,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAGA,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAGA,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAGA,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAGA,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAGA,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAGA,IAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,GAAG,GAAG;AACV,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AAChD,QAAQ,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,KAAK;AACL;AACA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1B;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;AAChD,YAAYC,UAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxD,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtC,YAAY,MAAM,GAAG,GAAGA,UAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,YAAY,MAAM,EAAE,GAAGA,UAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,YAAY,MAAM,EAAE,GAAGZ,MAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAGA,MAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAGA,MAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAGA,MAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACjE,YAAYY,UAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAGA,UAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGA,UAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AAC7E,SAAS;AACT;AACA,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AAC9C,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACrC,YAAY,MAAM,MAAM,GAAGZ,MAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,MAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGA,MAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,GAAGQ,KAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAGE,UAAQ,CAAC,CAAC,CAAC,GAAGE,UAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACnF,YAAY,MAAM,MAAM,GAAGZ,MAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,MAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGA,MAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,CAAC,MAAM,GAAGS,KAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACnD,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC7B,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC9B,SAAS;AACT;AACA,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,UAAU,GAAG;AACjB,QAAQG,UAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,KAAK;AACL,EAAC;AAgBD;AACA;AACA;AACA;AACO,MAAME,QAAM,mBAAmBT,iBAAe,CAAC,MAAM,IAAIU,QAAM,EAAE,CAAC;;AC3HzE;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAMC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAMC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAMpB,KAAG,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU,CAAC;AAC3C,MAAM,KAAK,mBAAmB,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACrG;AACA;AACA;AACO,SAASqB,YAAU,CAAC,KAAK,EAAE;AAClC,IAAI,IAAI,CAACrB,KAAG,CAAC,KAAK,CAAC;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC/C;AACA,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC;AACjB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACM,SAAS,mBAAmB,CAAC,GAAG,EAAE;AACzC,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AACjC,IAAI,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AAC5C,CAAC;AACM,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,OAAO,GAAG,CAAC,CAAC;AAClE;AACA,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACO,SAASsB,YAAU,CAAC,GAAG,EAAE;AAChC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,OAAO,GAAG,CAAC,CAAC;AAClE,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AAC3B,IAAI,IAAI,GAAG,GAAG,CAAC;AACf,QAAQ,MAAM,IAAI,KAAK,CAAC,yDAAyD,GAAG,GAAG,CAAC,CAAC;AACzF,IAAI,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,QAAQ,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,QAAQ,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAClD,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC;AAC1C,YAAY,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AACrD,QAAQ,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACO,SAAS,eAAe,CAAC,KAAK,EAAE;AACvC,IAAI,OAAO,WAAW,CAACD,YAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,CAAC;AACM,SAAS,eAAe,CAAC,KAAK,EAAE;AACvC,IAAI,IAAI,CAACrB,KAAG,CAAC,KAAK,CAAC;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC/C,IAAI,OAAO,WAAW,CAACqB,YAAU,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACrE,CAAC;AACM,SAAS,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE;AACxC,IAAI,OAAOC,YAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7D,CAAC;AACM,SAAS,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE;AACxC,IAAI,OAAO,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;AAC7C,CAAC;AACD;AACO,SAAS,kBAAkB,CAAC,CAAC,EAAE;AACtC,IAAI,OAAOA,YAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE;AACxD,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACjC,QAAQ,IAAI;AACZ,YAAY,GAAG,GAAGA,YAAU,CAAC,GAAG,CAAC,CAAC;AAClC,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAClB,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,gCAAgC,EAAE,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,SAAS;AACT,KAAK;AACL,SAAS,IAAItB,KAAG,CAAC,GAAG,CAAC,EAAE;AACvB;AACA;AACA,QAAQ,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,KAAK;AACL,SAAS;AACT,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AAC3B,IAAI,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,GAAG,KAAK,cAAc;AACpE,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,cAAc,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACjF,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACO,SAAS,WAAW,CAAC,GAAG,MAAM,EAAE;AACvC,IAAI,MAAM,CAAC,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AAC1B,QAAQ,IAAI,CAACA,KAAG,CAAC,CAAC,CAAC;AACnB,YAAY,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AACnD,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACtB,QAAQ,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACM,SAAS,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE;AACnC;AACA,IAAI,IAAI,EAAE,CAAC,MAAM,KAAK,EAAE,CAAC,MAAM;AAC/B,QAAQ,OAAO,KAAK,CAAC;AACrB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE;AACtC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC3B,YAAY,OAAO,KAAK,CAAC;AACzB,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD;AACA;AACA;AACO,SAASI,aAAW,CAAC,GAAG,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,CAAC,EAAE;AAC1B,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,GAAGc,KAAG,EAAE,CAAC,KAAKC,KAAG,EAAE,GAAG,IAAI,CAAC;AAC9C,QAAQ,CAAC;AACT,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE;AAC/B,IAAI,OAAO,CAAC,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,IAAIA,KAAG,CAAC;AACpC,CAAC;AACD;AACA;AACA;AACO,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK;AACzC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,GAAGA,KAAG,GAAGD,KAAG,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACpD,CAAC,CAAC;AACF;AACA;AACA;AACA;AACO,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAACE,KAAG,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAID,KAAG,CAAC;AAC3D;AACA,MAAM,GAAG,GAAG,CAAC,IAAI,KAAK,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AAC3C,MAAM,IAAI,GAAG,CAAC,GAAG,KAAK,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE;AAC1D,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC;AAClD,QAAQ,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AACpD,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,GAAG,CAAC;AACpD,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AACrD,IAAI,IAAI,OAAO,MAAM,KAAK,UAAU;AACpC,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AACrD;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,MAAM,KAAK,GAAG,MAAM;AACxB,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,QAAQ,CAAC,GAAG,CAAC,CAAC;AACd,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3C,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,KAAK;AACrC;AACA,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;AAChB,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAC7B,YAAY,OAAO;AACnB,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;AAChB,KAAK,CAAC;AACN,IAAI,MAAM,GAAG,GAAG,MAAM;AACtB;AACA,QAAQ,IAAI,CAAC,EAAE,IAAI,IAAI;AACvB,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AACvD,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC;AACpB,QAAQ,MAAM,GAAG,GAAG,EAAE,CAAC;AACvB,QAAQ,OAAO,GAAG,GAAG,QAAQ,EAAE;AAC/B,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;AACpB,YAAY,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;AACjC,YAAY,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,YAAY,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC;AAC5B,SAAS;AACT,QAAQ,OAAO,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC;AACnC,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK;AACrC,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,QAAQ,IAAI,GAAG,GAAG,SAAS,CAAC;AAC5B,QAAQ,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,YAAY,MAAM,EAAE,CAAC;AACrB,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;AACD;AACA,MAAM,YAAY,GAAG;AACrB,IAAI,MAAM,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ;AAC5C,IAAI,QAAQ,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU;AAChD,IAAI,OAAO,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,SAAS;AAC9C,IAAI,MAAM,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ;AAC5C,IAAI,kBAAkB,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,UAAU;AACrF,IAAI,aAAa,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC;AACrD,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;AACtC,IAAI,KAAK,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;AAClD,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU,IAAI,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC;AACnF,CAAC,CAAC;AACF;AACO,SAAS,cAAc,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,GAAG,EAAE,EAAE;AACvE,IAAI,MAAM,UAAU,GAAG,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,KAAK;AACxD,QAAQ,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAQ,IAAI,OAAO,QAAQ,KAAK,UAAU;AAC1C,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;AAC9E,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACtC,QAAQ,IAAI,UAAU,IAAI,GAAG,KAAK,SAAS;AAC3C,YAAY,OAAO;AACnB,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;AACpC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,OAAO,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3G,SAAS;AACT,KAAK,CAAC;AACN,IAAI,KAAK,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,QAAQ,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC3C,IAAI,KAAK,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC;AACjE,QAAQ,UAAU,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1C,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;ACxQA;AACA;AAEA;AACA,MAAMD,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAEG,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACzE;AACA,MAAMC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxD;AACY,MAAM,CAAC,CAAC,CAAC,CAAC,CAAQ,MAAM,CAAC,EAAE,EAAE;AACzC;AACO,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;AAC1B,IAAI,MAAM,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,IAAI,OAAO,MAAM,IAAIN,KAAG,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE;AACxC,IAAI,IAAI,MAAM,IAAIA,KAAG,IAAI,KAAK,GAAGA,KAAG;AACpC,QAAQ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AACrD,IAAI,IAAI,MAAM,KAAKC,KAAG;AACtB,QAAQ,OAAOD,KAAG,CAAC;AACnB,IAAI,IAAI,GAAG,GAAGC,KAAG,CAAC;AAClB,IAAI,OAAO,KAAK,GAAGD,KAAG,EAAE;AACxB,QAAQ,IAAI,KAAK,GAAGC,KAAG;AACvB,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,MAAM,CAAC;AACvC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,MAAM,CAAC;AACnC,QAAQ,KAAK,KAAKA,KAAG,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACO,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,OAAO,KAAK,EAAE,GAAGD,KAAG,EAAE;AAC1B,QAAQ,GAAG,IAAI,GAAG,CAAC;AACnB,QAAQ,GAAG,IAAI,MAAM,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACO,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE;AACvC,IAAI,IAAI,MAAM,KAAKA,KAAG,IAAI,MAAM,IAAIA,KAAG,EAAE;AACzC,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,0CAA0C,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7F,KAAK;AACL;AACA;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC;AACnB;AACA,IAAO,IAAC,CAAC,GAAGA,KAAG,CAAC,CAAU,CAAC,GAAGC,KAAG,CAAU;AAC3C,IAAI,OAAO,CAAC,KAAKD,KAAG,EAAE;AACtB;AACA,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAE5B;AACA,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAS,CAAC,GAAG,CAAQ,CAAC;AACjD,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,GAAG,KAAKC,KAAG;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAClD,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,CAAC,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,SAAS,GAAG,CAAC,CAAC,GAAGA,KAAG,IAAIC,KAAG,CAAC;AACtC,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAChB;AACA;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,GAAGD,KAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGC,KAAG,KAAKF,KAAG,EAAE,CAAC,IAAIE,KAAG,EAAE,CAAC,EAAE;AAC3D,QAAQ,CAAC;AACT;AACA,IAAI,KAAK,CAAC,GAAGA,KAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,GAAGD,KAAG,EAAE,CAAC,EAAE;AAChE,QAAQ,CAAC;AACT;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;AACjB,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGA,KAAG,IAAIK,KAAG,CAAC;AACvC,QAAQ,OAAO,SAAS,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE;AAC3C,YAAY,MAAM,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC3D,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL;AACA,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGL,KAAG,IAAIC,KAAG,CAAC;AACnC,IAAI,OAAO,SAAS,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE;AACvC;AACA,QAAQ,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;AACnD,YAAY,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB;AACA,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7C,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE;AACnC,YAAY,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;AAClC,gBAAgB,OAAO,EAAE,CAAC,IAAI,CAAC;AAC/B;AACA,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC;AACtB,YAAY,KAAK,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACjD,gBAAgB,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC;AACtC,oBAAoB,MAAM;AAC1B,gBAAgB,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAChC,aAAa;AACb;AACA,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAED,KAAG,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC3B,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9B,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,SAAS;AACT,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,CAAC;AACM,SAAS,MAAM,CAAC,CAAC,EAAE;AAC1B;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAGK,KAAG,KAAKD,KAAG,EAAE;AACzB;AACA;AACA;AACA;AACA,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGJ,KAAG,IAAIK,KAAG,CAAC;AACvC,QAAQ,OAAO,SAAS,SAAS,CAAC,EAAE,EAAE,CAAC,EAAE;AACzC,YAAY,MAAM,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3C;AACA,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC3D,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,EAAE;AACzB,QAAQ,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;AACnC,QAAQ,OAAO,SAAS,SAAS,CAAC,EAAE,EAAE,CAAC,EAAE;AACzC,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEJ,KAAG,CAAC,CAAC;AACtC,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACrC,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAEA,KAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACjD,YAAY,MAAM,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACvD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxC,gBAAgB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AAC3D,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AAuBL;AACA,IAAI,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AAGD;AACA,MAAM,YAAY,GAAG;AACrB,IAAI,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;AAC3D,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AAC5C,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,CAAC,CAAC;AACK,SAAS,aAAa,CAAC,KAAK,EAAE;AACrC,IAAI,MAAM,OAAO,GAAG;AACpB,QAAQ,KAAK,EAAE,QAAQ;AACvB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,IAAI,EAAE,eAAe;AAC7B,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK;AACnD,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AAC9B,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,EAAE,OAAO,CAAC,CAAC;AAChB,IAAI,OAAO,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;AACrC;AACA;AACA,IAAI,IAAI,KAAK,GAAGF,KAAG;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAC9C,IAAI,IAAI,KAAK,KAAKA,KAAG;AACrB,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC;AACrB,IAAI,IAAI,KAAK,KAAKC,KAAG;AACrB,QAAQ,OAAO,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAClB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC;AAChB,IAAI,OAAO,KAAK,GAAGD,KAAG,EAAE;AACxB,QAAQ,IAAI,KAAK,GAAGC,KAAG;AACvB,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrB,QAAQ,KAAK,KAAKA,KAAG,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD;AACA;AACA;AACA;AACO,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE;AACvC,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC;AACA,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK;AACxD,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACtB,YAAY,OAAO,GAAG,CAAC;AACvB,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACrB,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/B,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACd;AACA,IAAI,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC3C;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,KAAK;AACtC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AACtB,YAAY,OAAO,GAAG,CAAC;AACvB,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/B,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjB,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AAYD;AACO,SAAS,OAAO,CAAC,CAAC,EAAE,UAAU,EAAE;AACvC;AACA,IAAI,MAAM,WAAW,GAAG,UAAU,KAAK,SAAS,GAAG,UAAU,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACrF,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AACnD,IAAI,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASM,OAAK,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE;AAC/D,IAAI,IAAI,KAAK,IAAIP,KAAG;AACpB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAClE,IAAI,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC5E,IAAI,IAAI,KAAK,GAAG,IAAI;AACpB,QAAQ,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AAC3E,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;AAC5B,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;AAC3B,QAAQ,IAAI,EAAEA,KAAG;AACjB,QAAQ,GAAG,EAAEC,KAAG;AAChB,QAAQ,MAAM,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;AACxC,QAAQ,OAAO,EAAE,CAAC,GAAG,KAAK;AAC1B,YAAY,IAAI,OAAO,GAAG,KAAK,QAAQ;AACvC,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,4CAA4C,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7F,YAAY,OAAOD,KAAG,IAAI,GAAG,IAAI,GAAG,GAAG,KAAK,CAAC;AAC7C,SAAS;AACT,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,KAAKA,KAAG;AACjC,QAAQ,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,GAAGC,KAAG,MAAMA,KAAG;AAC3C,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC;AACtC,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,KAAK,GAAG;AACtC,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAC3C,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAChD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAChD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,KAAK,CAAC;AAChD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;AACjD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;AAC/D;AACA,QAAQ,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,GAAG,GAAG;AAChC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,GAAG;AACrC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,GAAG;AACrC,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,GAAG;AACrC,QAAQ,GAAG,EAAE,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC;AACxC,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChD,QAAQ,WAAW,EAAE,CAAC,GAAG,KAAK,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC;AACnD;AACA;AACA,QAAQ,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,OAAO,EAAE,CAAC,GAAG,MAAM,IAAI,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC5F,QAAQ,SAAS,EAAE,CAAC,KAAK,KAAK;AAC9B,YAAY,IAAI,KAAK,CAAC,MAAM,KAAK,KAAK;AACtC,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxF,YAAY,OAAO,IAAI,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC1E,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AA4BD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,UAAU,EAAE;AAChD,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ;AACtC,QAAQ,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AACtD,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AACpC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,UAAU,EAAE;AAC7C,IAAI,MAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;AACnD,IAAI,OAAO,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC1C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,GAAG,KAAK,EAAE;AAC9D,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;AAC3B,IAAI,MAAM,QAAQ,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;AACrD,IAAI,MAAM,MAAM,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAChD;AACA,IAAI,IAAI,GAAG,GAAG,EAAE,IAAI,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,IAAI;AAC9C,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9E,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG,eAAe,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;AACnE;AACA,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,EAAE,UAAU,GAAGA,KAAG,CAAC,GAAGA,KAAG,CAAC;AACrD,IAAI,OAAO,IAAI,GAAG,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAC1F;;AC9ZA;AACA;AAGA,MAAMD,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAMC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;AAC9B,IAAI,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,IAAI,KAAK;AACjD,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAClC,QAAQ,OAAO,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC;AACtC,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK;AACxB,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAChD,QAAQ,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,QAAQ,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;AACvC,KAAK,CAAC;AACN,IAAI,OAAO;AACX,QAAQ,eAAe;AACvB;AACA,QAAQ,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE;AAC7B,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC3B,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC;AACxB,YAAY,OAAO,CAAC,GAAGD,KAAG,EAAE;AAC5B,gBAAgB,IAAI,CAAC,GAAGC,KAAG;AAC3B,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AAC/B,gBAAgB,CAAC,KAAKA,KAAG,CAAC;AAC1B,aAAa;AACb,YAAY,OAAO,CAAC,CAAC;AACrB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,gBAAgB,CAAC,GAAG,EAAE,CAAC,EAAE;AACjC,YAAY,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACpD,YAAY,MAAM,MAAM,GAAG,EAAE,CAAC;AAC9B,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC;AACxB,YAAY,IAAI,IAAI,GAAG,CAAC,CAAC;AACzB,YAAY,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE;AAC7D,gBAAgB,IAAI,GAAG,CAAC,CAAC;AACzB,gBAAgB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC;AACA,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;AACrD,oBAAoB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC,oBAAoB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtC,iBAAiB;AACjB,gBAAgB,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAClC,aAAa;AACb,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;AAChC;AACA;AACA,YAAY,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACpD,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC3B,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC3B,YAAY,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,YAAY,MAAM,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;AACrC,YAAY,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,EAAE;AAC7D,gBAAgB,MAAM,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;AACnD;AACA,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC7C;AACA,gBAAgB,CAAC,KAAK,OAAO,CAAC;AAC9B;AACA;AACA,gBAAgB,IAAI,KAAK,GAAG,UAAU,EAAE;AACxC,oBAAoB,KAAK,IAAI,SAAS,CAAC;AACvC,oBAAoB,CAAC,IAAIA,KAAG,CAAC;AAC7B,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,MAAM,OAAO,GAAG,MAAM,CAAC;AACvC,gBAAgB,MAAM,OAAO,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7D,gBAAgB,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/C,gBAAgB,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AACxC,gBAAgB,IAAI,KAAK,KAAK,CAAC,EAAE;AACjC;AACA,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5E,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5E,iBAAiB;AACjB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,SAAS;AACT,QAAQ,UAAU,CAAC,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,SAAS,EAAE;AACpD;AACA,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC;AAC1C;AACA,YAAY,IAAI,IAAI,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,YAAY,IAAI,CAAC,IAAI,EAAE;AACvB,gBAAgB,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD,gBAAgB,IAAI,CAAC,KAAK,CAAC,EAAE;AAC7B,oBAAoB,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3D,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACzC,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACM,SAAS,aAAa,CAAC,KAAK,EAAE;AACrC,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,cAAc,CAAC,KAAK,EAAE;AAC1B,QAAQ,CAAC,EAAE,QAAQ;AACnB,QAAQ,CAAC,EAAE,QAAQ;AACnB,QAAQ,EAAE,EAAE,OAAO;AACnB,QAAQ,EAAE,EAAE,OAAO;AACnB,KAAK,EAAE;AACP,QAAQ,UAAU,EAAE,eAAe;AACnC,QAAQ,WAAW,EAAE,eAAe;AACpC,KAAK,CAAC,CAAC;AACP;AACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC;AACzB,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC;AAC7C,QAAQ,GAAG,KAAK;AAChB,QAAQ,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;AAChC,KAAK,CAAC,CAAC;AACP;;AC1JA;AACA;AAKA,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAClC,IAAI,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AACtC,IAAIO,cAAiB,CAAC,IAAI,EAAE;AAC5B,QAAQ,CAAC,EAAE,OAAO;AAClB,QAAQ,CAAC,EAAE,OAAO;AAClB,KAAK,EAAE;AACP,QAAQ,wBAAwB,EAAE,OAAO;AACzC,QAAQ,cAAc,EAAE,SAAS;AACjC,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,kBAAkB,EAAE,SAAS;AACrC,QAAQ,SAAS,EAAE,UAAU;AAC7B,QAAQ,OAAO,EAAE,UAAU;AAC3B,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACjC,IAAI,IAAI,IAAI,EAAE;AACd,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;AACjG,SAAS;AACT,QAAQ,IAAI,OAAO,IAAI,KAAK,QAAQ;AACpC,YAAY,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;AACzC,YAAY,OAAO,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;AACpD,YAAY,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;AACjG,SAAS;AACT,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;AACtC,CAAC;AACD;AACA,MAAM,EAAE,eAAe,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC9C,MAAM,GAAG,GAAG;AACnB;AACA,IAAI,GAAG,EAAE,MAAM,MAAM,SAAS,KAAK,CAAC;AACpC,QAAQ,WAAW,CAAC,CAAC,GAAG,EAAE,EAAE;AAC5B,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC;AACrB,SAAS;AACT,KAAK;AACL,IAAI,SAAS,CAAC,IAAI,EAAE;AACpB,QAAQ,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC;AAC/B,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;AAC/C,YAAY,MAAM,IAAI,CAAC,CAAC,+BAA+B,CAAC,CAAC;AACzD,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9C,QAAQ,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG;AACtC,YAAY,MAAM,IAAI,CAAC,CAAC,yCAAyC,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU;AAC/B,YAAY,MAAM,IAAI,CAAC,CAAC,qCAAqC,CAAC,CAAC;AAC/D,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;AACrD,YAAY,MAAM,IAAI,CAAC,CAAC,qDAAqD,CAAC,CAAC;AAC/E,QAAQ,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;AAC1D,KAAK;AACL,IAAI,KAAK,CAAC,GAAG,EAAE;AACf;AACA,QAAQ,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC;AAC/B,QAAQ,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC9D,QAAQ,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;AACzC,YAAY,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AAC7C,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;AACpC,YAAY,MAAM,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC;AACjD,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;AAC7B,YAAY,MAAM,IAAI,CAAC,CAAC,qCAAqC,CAAC,CAAC;AAC/D,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC9D,QAAQ,IAAI,UAAU,CAAC,MAAM;AAC7B,YAAY,MAAM,IAAI,CAAC,CAAC,6CAA6C,CAAC,CAAC;AACvE,QAAQ,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACxB,KAAK;AACL,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB;AACA,QAAQ,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjF,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK;AAC3B,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AACzC,YAAY,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AACpD,SAAS,CAAC;AACV,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,QAAQ,MAAM,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACjC,QAAQ,MAAM,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACjC,QAAQ,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B,QAAQ,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B,QAAQ,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7D,KAAK;AACL,CAAC,CAAC;AACF;AACA;AACK,MAACR,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAO,MAAM,CAAC,CAAC,CAAC,CAAC,OAACI,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAO,MAAM,CAAC,CAAC,EAAE;AACnF,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACxC,IAAI,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;AACzB,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AACjC,SAAS,CAAC,EAAE,EAAE,KAAK,EAAE,aAAa,KAAK;AACvC,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AACvC,YAAY,OAAOI,WAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7F,SAAS,CAAC,CAAC;AACX,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS;AACrC,SAAS,CAAC,KAAK,KAAK;AACpB;AACA,YAAY,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/D,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E,YAAY,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,SAAS,CAAC,CAAC;AACX;AACA;AACA;AACA;AACA,IAAI,SAAS,mBAAmB,CAAC,CAAC,EAAE;AACpC,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC/B,QAAQ,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7B,QAAQ,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACjC,QAAQ,OAAO,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnD,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAChE,QAAQ,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AACvE;AACA,IAAI,SAAS,kBAAkB,CAAC,GAAG,EAAE;AACrC,QAAQ,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAIT,KAAG,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC3B,QAAQ,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;AACpC,YAAY,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC3E,KAAK;AACL;AACA;AACA,IAAI,SAAS,sBAAsB,CAAC,GAAG,EAAE;AACzC,QAAQ,MAAM,EAAE,wBAAwB,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AAC5F,QAAQ,IAAI,OAAO,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAChD,YAAY,IAAI,GAAG,YAAY,UAAU;AACzC,gBAAgB,GAAG,GAAGU,YAAa,CAAC,GAAG,CAAC,CAAC;AACzC;AACA,YAAY,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AACxE,gBAAgB,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;AAC/C,YAAY,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC;AAChB,QAAQ,IAAI;AACZ,YAAY,GAAG;AACf,gBAAgB,OAAO,GAAG,KAAK,QAAQ;AACvC,sBAAsB,GAAG;AACzB,sBAAsBC,eAAkB,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC;AACvF,SAAS;AACT,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,WAAW,CAAC,2BAA2B,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1G,SAAS;AACT,QAAQ,IAAI,cAAc;AAC1B,YAAY,GAAG,GAAGC,GAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAClC,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC;AACtB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;AACvC,IAAI,SAAS,cAAc,CAAC,KAAK,EAAE;AACnC,QAAQ,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC;AACrC,YAAY,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AACxD,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,KAAK,CAAC;AAChB,QAAQ,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAChC,YAAY,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,YAAY,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AAC7C,gBAAgB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAC9C,YAAY,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AAC7C,gBAAgB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAC9C,YAAY,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;AAC7C,gBAAgB,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAC9C,SAAS;AACT;AACA;AACA,QAAQ,OAAO,UAAU,CAAC,CAAC,EAAE;AAC7B,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AACrC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AACtD,gBAAgB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;AACxD,YAAY,IAAI,CAAC,YAAY,KAAK;AAClC,gBAAgB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAChE,YAAY,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AAClD;AACA,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAChC,gBAAgB,OAAO,KAAK,CAAC,IAAI,CAAC;AAClC,YAAY,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAC3C,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG;AAChB,YAAY,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG;AAChB,YAAY,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACrC,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,OAAO,UAAU,CAAC,MAAM,EAAE;AAClC,YAAY,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClE,YAAY,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACpF,SAAS;AACT;AACA;AACA;AACA;AACA,QAAQ,OAAO,OAAO,CAAC,GAAG,EAAE;AAC5B,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAChF,YAAY,CAAC,CAAC,cAAc,EAAE,CAAC;AAC/B,YAAY,OAAO,CAAC,CAAC;AACrB,SAAS;AACT;AACA,QAAQ,OAAO,cAAc,CAAC,UAAU,EAAE;AAC1C,YAAY,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC;AAC3E,SAAS;AACT;AACA,QAAQ,cAAc,CAAC,UAAU,EAAE;AACnC,YAAY,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC;AAC3C,YAAY,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1C,SAAS;AACT;AACA,QAAQ,cAAc,GAAG;AACzB,YAAY,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;AAC5B;AACA;AACA;AACA,gBAAgB,IAAI,KAAK,CAAC,kBAAkB,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;AAChE,oBAAoB,OAAO;AAC3B,gBAAgB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACnD,aAAa;AACb;AACA,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC7C;AACA,YAAY,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AAChD,gBAAgB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAC5D,YAAY,MAAM,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,YAAY,MAAM,KAAK,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;AACjD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC;AACpC,gBAAgB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;AACrE,YAAY,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACrC,gBAAgB,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC1E,SAAS;AACT,QAAQ,QAAQ,GAAG;AACnB,YAAY,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC1C,YAAY,IAAI,EAAE,CAAC,KAAK;AACxB,gBAAgB,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpC,YAAY,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;AAC3D,SAAS;AACT;AACA;AACA;AACA,QAAQ,MAAM,CAAC,KAAK,EAAE;AACtB,YAAY,cAAc,CAAC,KAAK,CAAC,CAAC;AAClC,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACpD,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;AACrD,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9D,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9D,YAAY,OAAO,EAAE,IAAI,EAAE,CAAC;AAC5B,SAAS;AACT;AACA;AACA;AACA,QAAQ,MAAM,GAAG;AACjB,YAAY,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAChE,SAAS;AACT;AACA;AACA;AACA;AACA,QAAQ,MAAM,GAAG;AACjB,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;AACnC,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAEP,KAAG,CAAC,CAAC;AACtC,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACpD,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC;AACzD,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/B,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/B,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/B,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACzC,SAAS;AACT;AACA;AACA;AACA;AACA,QAAQ,GAAG,CAAC,KAAK,EAAE;AACnB,YAAY,cAAc,CAAC,KAAK,CAAC,CAAC;AAClC,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACpD,YAAY,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;AACrD,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC;AACzD,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9B,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAEA,KAAG,CAAC,CAAC;AAC5C,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/B,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/B,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/B,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,YAAY,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACzC,SAAS;AACT,QAAQ,QAAQ,CAAC,KAAK,EAAE;AACxB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,GAAG,GAAG;AACd,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3C,SAAS;AACT,QAAQ,IAAI,CAAC,CAAC,EAAE;AAChB,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK;AACxE,gBAAgB,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE,gBAAgB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACtF,aAAa,CAAC,CAAC;AACf,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,QAAQ,cAAc,CAAC,CAAC,EAAE;AAC1B,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;AACjC,YAAY,IAAI,CAAC,KAAKL,KAAG;AACzB,gBAAgB,OAAO,CAAC,CAAC;AACzB,YAAY,QAAQ,CAAC,CAAC,CAAC,CAAC;AACxB,YAAY,IAAI,CAAC,KAAKC,KAAG;AACzB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACnC,YAAY,IAAI,CAAC,IAAI;AACrB,gBAAgB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAClD;AACA,YAAY,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC/D,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC;AACxB,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC;AACxB,YAAY,IAAI,CAAC,GAAG,IAAI,CAAC;AACzB,YAAY,OAAO,EAAE,GAAGD,KAAG,IAAI,EAAE,GAAGA,KAAG,EAAE;AACzC,gBAAgB,IAAI,EAAE,GAAGC,KAAG;AAC5B,oBAAoB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAgB,IAAI,EAAE,GAAGA,KAAG;AAC5B,oBAAoB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC;AAC/B,gBAAgB,EAAE,KAAKA,KAAG,CAAC;AAC3B,gBAAgB,EAAE,KAAKA,KAAG,CAAC;AAC3B,aAAa;AACb,YAAY,IAAI,KAAK;AACrB,gBAAgB,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;AACnC,YAAY,IAAI,KAAK;AACrB,gBAAgB,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;AACnC,YAAY,GAAG,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AACvE,YAAY,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAChC,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,QAAQ,CAAC,MAAM,EAAE;AACzB,YAAY,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC7B,YAAY,IAAI,CAAC,GAAG,MAAM,CAAC;AAC3B,YAAY,IAAI,KAAK,EAAE,IAAI,CAAC;AAC5B,YAAY,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;AACnC,YAAY,IAAI,IAAI,EAAE;AACtB,gBAAgB,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACrE,gBAAgB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvD,gBAAgB,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvD,gBAAgB,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACvD,gBAAgB,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACvD,gBAAgB,GAAG,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AAC3E,gBAAgB,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,gBAAgB,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9C,gBAAgB,KAAK,GAAG,CAAC,CAAC;AAC1B,gBAAgB,IAAI,GAAG,CAAC,CAAC;AACzB,aAAa;AACb;AACA,YAAY,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;AACjC,YAAY,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;AAC7B,kBAAkB,CAAC,KAAKD,KAAG,IAAI,CAAC,KAAKC,KAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAChG,YAAY,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpD,YAAY,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG,GAAG,CAAC;AAC/C,SAAS;AACT;AACA;AACA;AACA,QAAQ,QAAQ,CAAC,EAAE,EAAE;AACrB,YAAY,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AACjD,YAAY,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACnC;AACA;AACA,YAAY,IAAI,EAAE,IAAI,IAAI;AAC1B,gBAAgB,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9C,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACrC,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACrC,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACrC,YAAY,IAAI,GAAG;AACnB,gBAAgB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;AAClD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC;AACnC,gBAAgB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACpD,YAAY,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;AACpC,SAAS;AACT,QAAQ,aAAa,GAAG;AACxB,YAAY,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACzD,YAAY,IAAI,QAAQ,KAAKA,KAAG;AAChC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,IAAI,aAAa;AAC7B,gBAAgB,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAClD,YAAY,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;AAC5F,SAAS;AACT,QAAQ,aAAa,GAAG;AACxB,YAAY,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACzD,YAAY,IAAI,QAAQ,KAAKA,KAAG;AAChC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,IAAI,aAAa;AAC7B,gBAAgB,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAClD,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChD,SAAS;AACT,QAAQ,UAAU,CAAC,YAAY,GAAG,IAAI,EAAE;AACxC,YAAY,IAAI,CAAC,cAAc,EAAE,CAAC;AAClC,YAAY,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AACtD,SAAS;AACT,QAAQ,KAAK,CAAC,YAAY,GAAG,IAAI,EAAE;AACnC,YAAY,OAAOS,YAAa,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;AAChE,SAAS;AACT,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AACvD,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AACrD,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;AACnC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACxE;AACA,IAAI,OAAO;AACX,QAAQ,KAAK;AACb,QAAQ,eAAe,EAAE,KAAK;AAC9B,QAAQ,sBAAsB;AAC9B,QAAQ,mBAAmB;AAC3B,QAAQ,kBAAkB;AAC1B,KAAK,CAAC;AACN,CAAC;AACD,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,IAAI,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AACtC,IAAIF,cAAiB,CAAC,IAAI,EAAE;AAC5B,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,WAAW,EAAE,UAAU;AAC/B,KAAK,EAAE;AACP,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,IAAI,EAAE,SAAS;AACvB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;AAClD,CAAC;AACM,SAAS,WAAW,CAAC,QAAQ,EAAE;AACtC,IAAI,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AACzC,IAAI,MAAM,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;AACzC,IAAI,MAAM,aAAa,GAAG,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;AACvC,IAAI,MAAM,eAAe,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;AAC7C,IAAI,SAAS,mBAAmB,CAAC,GAAG,EAAE;AACtC,QAAQ,OAAOR,KAAG,GAAG,GAAG,IAAI,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;AAC3C,KAAK;AACL,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE;AACrB,QAAQ,OAAOY,GAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE;AACrB,QAAQ,OAAOC,MAAU,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AAC1C,KAAK;AACL,IAAI,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,kBAAkB,GAAG,GAAG,iBAAiB,CAAC;AAC3H,QAAQ,GAAG,KAAK;AAChB,QAAQ,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;AACzC,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;AACvC,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,MAAM,GAAG,GAAGJ,WAAc,CAAC;AACvC,YAAY,IAAI,YAAY,EAAE;AAC9B,gBAAgB,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjF,aAAa;AACb,iBAAiB;AACjB,gBAAgB,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,aAAa;AACb,SAAS;AACT,QAAQ,SAAS,CAAC,KAAK,EAAE;AACzB,YAAY,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AACrC,YAAY,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,YAAY,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA,YAAY,IAAI,GAAG,KAAK,aAAa,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE;AAC3E,gBAAgB,MAAM,CAAC,GAAGE,eAAkB,CAAC,IAAI,CAAC,CAAC;AACnD,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAC3C,oBAAoB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAC7D,gBAAgB,MAAM,EAAE,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAClD,gBAAgB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACpC,gBAAgB,MAAM,MAAM,GAAG,CAAC,CAAC,GAAGV,KAAG,MAAMA,KAAG,CAAC;AACjD;AACA,gBAAgB,MAAM,SAAS,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;AACnD,gBAAgB,IAAI,SAAS,KAAK,MAAM;AACxC,oBAAoB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,gBAAgB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChC,aAAa;AACb,iBAAiB,IAAI,GAAG,KAAK,eAAe,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/D,gBAAgB,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACnE,gBAAgB,MAAM,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9E,gBAAgB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAChC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,uBAAuB,EAAE,aAAa,CAAC,qBAAqB,EAAE,eAAe,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAC3J,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,aAAa,GAAG,CAAC,GAAG,KAAKS,YAAa,CAACI,eAAkB,CAAC,GAAG,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;AAC7F,IAAI,SAAS,qBAAqB,CAAC,MAAM,EAAE;AAC3C,QAAQ,MAAM,IAAI,GAAG,WAAW,IAAIb,KAAG,CAAC;AACxC,QAAQ,OAAO,MAAM,GAAG,IAAI,CAAC;AAC7B,KAAK;AACL,IAAI,SAAS,UAAU,CAAC,CAAC,EAAE;AAC3B,QAAQ,OAAO,qBAAqB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACvD,KAAK;AACL;AACA,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,KAAKU,eAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAC1E;AACA;AACA;AACA,IAAI,MAAM,SAAS,CAAC;AACpB,QAAQ,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE;AACpC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACvB,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACvB,YAAY,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACrC,YAAY,IAAI,CAAC,cAAc,EAAE,CAAC;AAClC,SAAS;AACT;AACA,QAAQ,OAAO,WAAW,CAAC,GAAG,EAAE;AAChC,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;AACxC,YAAY,GAAG,GAAG,WAAW,CAAC,kBAAkB,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9D,YAAY,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3E,SAAS;AACT;AACA;AACA,QAAQ,OAAO,OAAO,CAAC,GAAG,EAAE;AAC5B,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;AAChE,YAAY,OAAO,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,SAAS;AACT,QAAQ,cAAc,GAAG;AACzB;AACA,YAAY,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3C,gBAAgB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC7D,YAAY,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3C,gBAAgB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC7D,SAAS;AACT,QAAQ,cAAc,CAAC,QAAQ,EAAE;AACjC,YAAY,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,gBAAgB,CAAC,OAAO,EAAE;AAClC,YAAY,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACjD,YAAY,MAAM,CAAC,GAAG,aAAa,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;AACrE,YAAY,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC1D,gBAAgB,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AACvD,YAAY,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAClE,YAAY,IAAI,IAAI,IAAI,EAAE,CAAC,KAAK;AAChC,gBAAgB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAC9D,YAAY,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AACzD,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAClC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACrC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjE,YAAY,IAAI,CAAC,CAAC;AAClB,gBAAgB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;AACrD,YAAY,CAAC,CAAC,cAAc,EAAE,CAAC;AAC/B,YAAY,OAAO,CAAC,CAAC;AACrB,SAAS;AACT;AACA,QAAQ,QAAQ,GAAG;AACnB,YAAY,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjD,SAAS;AACT,QAAQ,UAAU,GAAG;AACrB,YAAY,OAAO,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AAChG,SAAS;AACT;AACA,QAAQ,aAAa,GAAG;AACxB,YAAY,OAAOI,YAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClD,SAAS;AACT,QAAQ,QAAQ,GAAG;AACnB,YAAY,OAAO,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5D,SAAS;AACT;AACA,QAAQ,iBAAiB,GAAG;AAC5B,YAAY,OAAOA,YAAa,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;AACtD,SAAS;AACT,QAAQ,YAAY,GAAG;AACvB,YAAY,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACjE,SAAS;AACT,KAAK;AACL,IAAI,MAAM,KAAK,GAAG;AAClB,QAAQ,iBAAiB,CAAC,UAAU,EAAE;AACtC,YAAY,IAAI;AAChB,gBAAgB,sBAAsB,CAAC,UAAU,CAAC,CAAC;AACnD,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,YAAY,OAAO,KAAK,EAAE;AAC1B,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,QAAQ,sBAAsB,EAAE,sBAAsB;AACtD;AACA;AACA;AACA;AACA,QAAQ,gBAAgB,EAAE,MAAM;AAChC,YAAY,MAAM,MAAM,GAAGC,gBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzD,YAAY,OAAOC,cAAkB,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1E,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;AACvD,YAAY,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAC7C,YAAY,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,YAAY,CAAC,UAAU,EAAE,YAAY,GAAG,IAAI,EAAE;AAC3D,QAAQ,OAAO,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AACzE,KAAK;AACL;AACA;AACA;AACA,IAAI,SAAS,SAAS,CAAC,IAAI,EAAE;AAC7B,QAAQ,MAAM,GAAG,GAAG,IAAI,YAAY,UAAU,CAAC;AAC/C,QAAQ,MAAM,GAAG,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC;AAC7C,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,MAAM,CAAC;AAChD,QAAQ,IAAI,GAAG;AACf,YAAY,OAAO,GAAG,KAAK,aAAa,IAAI,GAAG,KAAK,eAAe,CAAC;AACpE,QAAQ,IAAI,GAAG;AACf,YAAY,OAAO,GAAG,KAAK,CAAC,GAAG,aAAa,IAAI,GAAG,KAAK,CAAC,GAAG,eAAe,CAAC;AAC5E,QAAQ,IAAI,IAAI,YAAY,KAAK;AACjC,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,eAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,GAAG,IAAI,EAAE;AACrE,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAC7D,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAC7D,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACzC,QAAQ,OAAO,CAAC,CAAC,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AACrF,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ;AACnC,QAAQ,UAAU,KAAK,EAAE;AACzB;AACA;AACA,YAAY,MAAM,GAAG,GAAGN,eAAkB,CAAC,KAAK,CAAC,CAAC;AAClD,YAAY,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;AAC9D,YAAY,OAAO,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AAC1D,SAAS,CAAC;AACV,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa;AAC7C,QAAQ,UAAU,KAAK,EAAE;AACzB,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,SAAS,CAAC;AACV;AACA,IAAI,MAAM,UAAU,GAAGO,OAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACpD;AACA;AACA;AACA,IAAI,SAAS,UAAU,CAAC,GAAG,EAAE;AAC7B,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ;AACnC,YAAY,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC/C,QAAQ,IAAI,EAAElB,KAAG,IAAI,GAAG,IAAI,GAAG,GAAG,UAAU,CAAC;AAC7C,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvE;AACA,QAAQ,OAAOc,eAAkB,CAAC,GAAG,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;AAC1D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,GAAG,cAAc,EAAE;AACjE,QAAQ,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;AAC7D,YAAY,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AACnE,QAAQ,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;AAC5C,QAAQ,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACxD,QAAQ,IAAI,IAAI,IAAI,IAAI;AACxB,YAAY,IAAI,GAAG,IAAI,CAAC;AACxB,QAAQ,OAAO,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAClD,QAAQ,IAAI,OAAO;AACnB,YAAY,OAAO,GAAG,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AACtE;AACA;AACA;AACA,QAAQ,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAQ,MAAM,CAAC,GAAG,sBAAsB,CAAC,UAAU,CAAC,CAAC;AACrD,QAAQ,MAAM,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5D;AACA,QAAQ,IAAI,GAAG,IAAI,IAAI,EAAE;AACzB;AACA,YAAY,MAAM,CAAC,GAAG,GAAG,KAAK,IAAI,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACjE,YAAY,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1D,SAAS;AACT,QAAQ,MAAM,IAAI,GAAGL,WAAc,CAAC,GAAG,QAAQ,CAAC,CAAC;AACjD,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC;AACxB;AACA,QAAQ,SAAS,KAAK,CAAC,MAAM,EAAE;AAC/B;AACA,YAAY,MAAM,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AACvC,YAAY,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;AACtC,gBAAgB,OAAO;AACvB,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,YAAY,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;AACxD,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,YAAY,IAAI,CAAC,KAAKT,KAAG;AACzB,gBAAgB,OAAO;AACvB;AACA;AACA;AACA,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD,YAAY,IAAI,CAAC,KAAKA,KAAG;AACzB,gBAAgB,OAAO;AACvB,YAAY,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAG,CAAC,CAAC;AACnE,YAAY,IAAI,KAAK,GAAG,CAAC,CAAC;AAC1B,YAAY,IAAI,IAAI,IAAI,qBAAqB,CAAC,CAAC,CAAC,EAAE;AAClD,gBAAgB,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACtC,gBAAgB,QAAQ,IAAI,CAAC,CAAC;AAC9B,aAAa;AACb,YAAY,OAAO,IAAI,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC/B,KAAK;AACL,IAAI,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;AAChE,IAAI,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,GAAG,cAAc,EAAE;AAC3D,QAAQ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAChE,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC;AACxB,QAAQ,MAAM,IAAI,GAAGkB,cAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AAChF,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACjC,KAAK;AACL;AACA,IAAI,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,GAAG,cAAc,EAAE;AAC1E,QAAQ,MAAM,EAAE,GAAG,SAAS,CAAC;AAC7B,QAAQ,OAAO,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAClD,QAAQ,SAAS,GAAG,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AACxD,QAAQ,IAAI,QAAQ,IAAI,IAAI;AAC5B,YAAY,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAClE,QAAQ,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;AACvC,QAAQ,IAAI,IAAI,GAAG,SAAS,CAAC;AAC7B,QAAQ,IAAI,CAAC,CAAC;AACd,QAAQ,IAAI;AACZ,YAAY,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,EAAE,YAAY,UAAU,EAAE;AACpE;AACA;AACA,gBAAgB,IAAI;AACpB,oBAAoB,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACjD,iBAAiB;AACjB,gBAAgB,OAAO,QAAQ,EAAE;AACjC,oBAAoB,IAAI,EAAE,QAAQ,YAAY,GAAG,CAAC,GAAG,CAAC;AACtD,wBAAwB,MAAM,QAAQ,CAAC;AACvC,oBAAoB,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACrD,iBAAiB;AACjB,aAAa;AACb,iBAAiB,IAAI,OAAO,EAAE,KAAK,QAAQ,IAAI,OAAO,EAAE,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,EAAE,CAAC,CAAC,KAAK,QAAQ,EAAE;AACrG,gBAAgB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACpC,gBAAgB,IAAI,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACzC,aAAa;AACb,YAAY,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACzC,SAAS;AACT,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO;AACzC,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,8DAA8D,CAAC,CAAC,CAAC;AAClG,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,IAAI,OAAO;AACnB,YAAY,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1C,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AAC9B,QAAQ,MAAM,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;AACzC,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAChC,QAAQ,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAChC,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC;AACzE,QAAQ,IAAI,CAAC,CAAC;AACd,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC;AACvB,KAAK;AACL,IAAI,OAAO;AACX,QAAQ,KAAK;AACb,QAAQ,YAAY;AACpB,QAAQ,eAAe;AACvB,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,eAAe,EAAE,KAAK;AAC9B,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,KAAK,CAAC;AACN;;ACl6BA;AACO,MAAM,IAAI,SAAS5B,MAAI,CAAC;AAC/B,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC5B,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAQ6B,MAAU,CAAC,IAAI,CAAC,CAAC;AACzB,QAAQ,MAAM,GAAG,GAAGjC,SAAO,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACnC,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,UAAU;AACnD,YAAY,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;AACnF,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC5C,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;AAC9C,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC,QAAQ,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC7C;AACA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;AAClF,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;AAC3C,YAAY,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AAC3B,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B;AACA,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACnC;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;AAC3C,YAAY,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;AAClC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,CAAC,GAAG,EAAE;AAChB,QAAQkC,QAAY,CAAC,IAAI,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB,QAAQA,QAAY,CAAC,IAAI,CAAC,CAAC;AAC3B,QAAQC,OAAW,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AACzC,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,KAAK;AACL,IAAI,MAAM,GAAG;AACb,QAAQ,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACzD,QAAQ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7B,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,UAAU,CAAC,EAAE,EAAE;AACnB;AACA,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACpE,QAAQ,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;AAChF,QAAQ,EAAE,GAAG,EAAE,CAAC;AAChB,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,QAAQ,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,QAAQ,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,QAAQ,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9C,QAAQ,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9C,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AAC7B,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AAC7B,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;AACzF,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;;AC3EhD;AAIA;AACO,SAAS,OAAO,CAAC,IAAI,EAAE;AAC9B,IAAI,OAAO;AACX,QAAQ,IAAI;AACZ,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,EAAElC,aAAW,CAAC,GAAG,IAAI,CAAC,CAAC;AACrE,QAAQ,WAAW;AACnB,KAAK,CAAC;AACN,CAAC;AACM,SAAS,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AAC/C,IAAI,MAAM,MAAM,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC5E,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;AACzD;;ACfA;AAQA,MAAM,UAAU,GAAG,MAAM,CAAC,oEAAoE,CAAC,CAAC;AAChG,MAAM,UAAU,GAAG,MAAM,CAAC,oEAAoE,CAAC,CAAC;AAChG,MAAMa,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAMC,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGA,KAAG,IAAI,CAAC,CAAC;AAC/C;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,CAAC,EAAE;AACpB,IAAI,MAAM,CAAC,GAAG,UAAU,CAAC;AACzB;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AACjF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAClE,IAAI,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACjC,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,EAAEA,KAAG,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC5C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAChD,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAC/C,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,EAAEA,KAAG,EAAE,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChC,QAAQ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,MAAM,EAAE,GAAGK,OAAK,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAC/D,MAAM,SAAS,GAAG,WAAW,CAAC;AACrC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AAChB,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AAChB,IAAI,EAAE;AACN,IAAI,CAAC,EAAE,UAAU;AACjB;AACA,IAAI,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;AAC/F,IAAI,EAAE,EAAE,MAAM,CAAC,+EAA+E,CAAC;AAC/F,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,EAAE,IAAI;AACd;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,EAAE;AACV,QAAQ,IAAI,EAAE,MAAM,CAAC,oEAAoE,CAAC;AAC1F,QAAQ,WAAW,EAAE,CAAC,CAAC,KAAK;AAC5B,YAAY,MAAM,CAAC,GAAG,UAAU,CAAC;AACjC,YAAY,MAAM,EAAE,GAAG,MAAM,CAAC,oCAAoC,CAAC,CAAC;AACpE,YAAY,MAAM,EAAE,GAAG,CAACN,KAAG,GAAG,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAC3E,YAAY,MAAM,EAAE,GAAG,MAAM,CAAC,qCAAqC,CAAC,CAAC;AACrE,YAAY,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1B,YAAY,MAAM,SAAS,GAAG,MAAM,CAAC,qCAAqC,CAAC,CAAC;AAC5E,YAAY,MAAM,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7C,YAAY,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C,YAAY,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AACnD,YAAY,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;AAChD,YAAY,MAAM,KAAK,GAAG,EAAE,GAAG,SAAS,CAAC;AACzC,YAAY,MAAM,KAAK,GAAG,EAAE,GAAG,SAAS,CAAC;AACzC,YAAY,IAAI,KAAK;AACrB,gBAAgB,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAC5B,YAAY,IAAI,KAAK;AACrB,gBAAgB,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;AAC5B,YAAY,IAAI,EAAE,GAAG,SAAS,IAAI,EAAE,GAAG,SAAS,EAAE;AAClD,gBAAgB,MAAM,IAAI,KAAK,CAAC,sCAAsC,GAAG,CAAC,CAAC,CAAC;AAC5E,aAAa;AACb,YAAY,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AAC5C,SAAS;AACT,KAAK;AACL,CAAC,EAAEH,QAAM,CAAC,CAAC;AACX;AACA;AACA,MAAME,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ,IAAIA,KAAG,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;AACrE,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ,IAAIA,KAAG,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;AACrE;AACA,MAAM,oBAAoB,GAAG,EAAE,CAAC;AAChC,SAAS,UAAU,CAAC,GAAG,EAAE,GAAG,QAAQ,EAAE;AACtC,IAAI,IAAI,IAAI,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;AACzC,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;AAC5B,QAAQ,MAAM,IAAI,GAAGF,QAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,QAAQ,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACvC,QAAQ,oBAAoB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACzC,KAAK;AACL,IAAI,OAAOA,QAAM,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAClD,CAAC;AACD;AACA,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChE,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/C,MAAMyB,MAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AACvC,MAAMC,MAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AACvC,MAAMC,OAAK,GAAG,SAAS,CAAC,eAAe,CAAC;AACxC,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAKA,OAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE;AACA,SAAS,mBAAmB,CAAC,IAAI,EAAE;AACnC,IAAI,IAAI,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,GAAGA,OAAK,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;AACrC,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAGD,MAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA,SAASE,QAAM,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACd,QAAQ,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;AACjD,IAAI,MAAM,EAAE,GAAGH,MAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,GAAGA,MAAI,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,GAAGrB,KAAG,KAAKF,KAAG;AACvB,QAAQ,CAAC,GAAGuB,MAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACrB,IAAI,MAAM,CAAC,GAAG,IAAIE,OAAK,CAAC,CAAC,EAAE,CAAC,EAAExB,KAAG,CAAC,CAAC;AACnC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;AACvB,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD;AACA;AACA;AACA,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE;AAC5B,IAAI,OAAOuB,MAAI,CAAC,eAAe,CAAC,UAAU,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,CAAC;AACD;AACA;AACA;AACA,SAAS,mBAAmB,CAAC,UAAU,EAAE;AACzC,IAAI,OAAO,mBAAmB,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,GAAG,WAAW,CAAC,EAAE,CAAC,EAAE;AACrE,IAAI,MAAM,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC9C,IAAI,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;AACrE,IAAI,MAAM,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;AAClD,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,eAAe,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,MAAM,IAAI,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD,IAAI,MAAM,EAAE,GAAGA,MAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3C,IAAI,IAAI,EAAE,KAAKxB,KAAG;AAClB,QAAQ,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAClD,IAAI,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,CAAC,CAAC;AAC7D,IAAI,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACnC,IAAI,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACnC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACnB,IAAI,GAAG,CAAC,GAAG,CAAC,QAAQ,CAACwB,MAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC3C;AACA,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE;AACtD,IAAI,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;AACxD,IAAI,MAAM,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC9C,IAAI,MAAM,GAAG,GAAG,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;AACxD,IAAI,IAAI;AACR,QAAQ,MAAM,CAAC,GAAGE,QAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C,QAAQ,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACxD,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,MAAM,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7D,QAAQ,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEF,MAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;AACvD,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,KAAK,EAAE;AAClB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,CAAC;AACM,MAAM,OAAO,mBAAmB,CAAC,OAAO;AAC/C,IAAI,YAAY,EAAE,mBAAmB;AACrC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,MAAM,EAAE,aAAa;AACzB,IAAI,KAAK,EAAE;AACX,QAAQ,gBAAgB,EAAE,SAAS,CAAC,KAAK,CAAC,gBAAgB;AAC1D,gBAAQE,QAAM;AACd,QAAQ,YAAY;AACpB,QAAQ,eAAe;AACvB,QAAQ,eAAe;AACvB,QAAQ,UAAU;AAClB,QAAQ,GAAG;AACX,KAAK;AACL,CAAC,CAAC,GAAG;;AC5ML,MAAA,aAAgB,GAAM;AAEtB,IAAA,QAAY,KAAW;AAEvB,IAAA;AACE,CAAA,CAAA;AACa,SAAG,WAAK,CAAA,MAAA,GAAA,EAAA,EAAA;IACrB,OAAgB,EAAA,GAAA,aAAK,EAAA,GAAA,MAAA,EAAA,CAAA;AACrB;;ACRF,SAAS,KAAK,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE;AAC9B,IAAI,IAAI,EAAE,CAAC,YAAY,UAAU,CAAC;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC/C,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;AACzD,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/F,CAAC;AACD,SAAS/C,QAAM,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAI,EAAE;AAChD,IAAI,IAAI,QAAQ,CAAC,SAAS;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,IAAI,IAAI,aAAa,IAAI,QAAQ,CAAC,QAAQ;AAC1C,QAAQ,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACjE,CAAC;AACD,SAAS,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE;AAC/B,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;AACnC,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,sDAAsD,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxF,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU,CAAC;AAC3C;AACA,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AACrF;AACA,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AACxE;AACA;AACA,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AAC9E,IAAI,CAAC,IAAI;AACT,IAAI,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AACnE;AACA;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,IAAI,EAAE;AACvB,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;AAChC,QAAQ,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;AAClB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD;AACA,MAAM,IAAI,CAAC;AACX;AACA,IAAI,KAAK,GAAG;AACZ,QAAQ,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;AACjC,KAAK;AACL,CAAC;AACD,SAAS,eAAe,CAAC,QAAQ,EAAE;AACnC,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACpE,IAAI,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC;AAC3B,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AACpC,IAAI,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAClC,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,QAAQ,EAAE,CAAC;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACA;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;AACrD,IAAI,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU;AAC/C,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1D,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAClD,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,CAAC;AACD;AACA,MAAM,IAAI,SAAS,IAAI,CAAC;AACxB,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;AACtD,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACrB,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,EAAE;AACjB,QAAQA,QAAM,CAAC,IAAI,CAAC,CAAC;AACrB,QAAQ,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAChD,QAAQ,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC,QAAQ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AACtC,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,YAAY,IAAI,IAAI,KAAK,QAAQ,EAAE;AACnC,gBAAgB,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AAClD,gBAAgB,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,QAAQ;AAC7D,oBAAoB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AAChD,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACjE,YAAY,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;AAC7B,YAAY,GAAG,IAAI,IAAI,CAAC;AACxB,YAAY,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE;AACvC,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,gBAAgB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB,QAAQA,QAAM,CAAC,IAAI,CAAC,CAAC;AACrB,QAAQ,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AACtD,QAAQ,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3B;AACA,QAAQ,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA,QAAQ,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,GAAG,EAAE;AAC7C,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAClC,YAAY,GAAG,GAAG,CAAC,CAAC;AACpB,SAAS;AACT;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE;AAC3C,YAAY,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B;AACA;AACA;AACA,QAAQ,YAAY,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACxE,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC9B,QAAQ,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC;AACnB,YAAY,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC3E,QAAQ,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;AAC/B,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAClE,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;AACvC,YAAY,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,GAAG;AACb,QAAQ,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;AAC3C,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,UAAU,CAAC,EAAE,EAAE;AACnB,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAC5C,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B,QAAQ,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC5E,QAAQ,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,QAAQ,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,QAAQ;AAC7B,YAAY,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C;AACA,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA,MAAM,QAAQ,mBAAmB,IAAI,WAAW,CAAC;AACjD,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,CAAC,CAAC,CAAC;AACH;AACA;AACA,MAAM,EAAE,mBAAmB,IAAI,WAAW,CAAC;AAC3C,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,CAAC,CAAC,CAAC;AACH;AACA;AACA,MAAM,QAAQ,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AACrD,MAAM,MAAM,SAAS,IAAI,CAAC;AAC1B,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAChC;AACA;AACA,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,GAAG,GAAG;AACV,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AAChD,QAAQ,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,KAAK;AACL;AACA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1B;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;AAChD,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxD,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtC,YAAY,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,YAAY,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACjE,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AAC7E,SAAS;AACT;AACA,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AAC9C,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACrC,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACnF,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACnD,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC7B,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC9B,SAAS;AACT;AACA,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,UAAU,GAAG;AACjB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMmB,QAAM,mBAAmB,eAAe,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,KAAK,CAAC,GAAG,IAAI,EAAE;AACxB;AACA,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACnC,SAAS,OAAO,EAAE;AAClB,SAAS,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAC/E;AACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAC9F,IAAI,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,QAAQ,EAAE;AAC5B,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,MAAM,KAAK;AAC5B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1F,gBAAgB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;AACvF,YAAY,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AACrC,gBAAgB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,MAAM;AACjD,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACzG,gBAAgB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACnC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,KAAK,KAAK;AAC3B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACvF,gBAAgB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACpF,YAAY,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AACzC,gBAAgB,IAAI,OAAO,MAAM,KAAK,QAAQ;AAC9C,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACrF,gBAAgB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACvD,gBAAgB,IAAI,KAAK,KAAK,CAAC,CAAC;AAChC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzF,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,SAAS,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE;AAC9B,IAAI,IAAI,OAAO,SAAS,KAAK,QAAQ;AACrC,QAAQ,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AAC3D,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK;AAC1B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACpF,gBAAgB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AAChF,YAAY,KAAK,IAAI,CAAC,IAAI,IAAI;AAC9B,gBAAgB,IAAI,OAAO,CAAC,KAAK,QAAQ;AACzC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,EAAE,KAAK;AACxB,YAAY,IAAI,OAAO,EAAE,KAAK,QAAQ;AACtC,gBAAgB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACtE,YAAY,OAAO,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACvC,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE;AAClC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI,OAAO;AACX,QAAQ,MAAM,CAAC,IAAI,EAAE;AACrB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACpF,gBAAgB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACnF,YAAY,KAAK,IAAI,CAAC,IAAI,IAAI;AAC9B,gBAAgB,IAAI,OAAO,CAAC,KAAK,QAAQ;AACzC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC;AAC3C,gBAAgB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,MAAM,CAAC,KAAK,EAAE;AACtB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACvF,gBAAgB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACnF,YAAY,KAAK,IAAI,CAAC,IAAI,KAAK;AAC/B,gBAAgB,IAAI,OAAO,CAAC,KAAK,QAAQ;AACzC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,YAAY,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AACnC,YAAY,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC;AAChC,gBAAgB,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;AAC7F,YAAY,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,EAAE;AAC7D,gBAAgB,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AAC7C,oBAAoB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACpF,aAAa;AACb,YAAY,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACvC,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC;AACA,IAAI,IAAI,IAAI,GAAG,CAAC;AAChB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;AACxF,IAAI,IAAI,EAAE,GAAG,CAAC;AACd,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,EAAE,CAAC,4BAA4B,CAAC,CAAC,CAAC;AACpF,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5B,QAAQ,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,QAAQ,OAAO,EAAE,CAAC;AAClB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AAC1B,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;AAC9B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,EAAE;AACjB,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;AACtB,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC;AACxB,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,YAAY,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACpC,YAAY,MAAM,SAAS,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AACnD,YAAY,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AAChD,gBAAgB,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;AAC/C,gBAAgB,SAAS,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,EAAE;AACpD,gBAAgB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAChE,aAAa;AACb,YAAY,KAAK,GAAG,SAAS,GAAG,EAAE,CAAC;AACnC,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC;AACvD,YAAY,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAChC,YAAY,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,EAAE,GAAG,KAAK,KAAK,SAAS;AACpF,gBAAgB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAChE,YAAY,IAAI,CAAC,IAAI;AACrB,gBAAgB,SAAS;AACzB,iBAAiB,IAAI,CAAC,OAAO;AAC7B,gBAAgB,GAAG,GAAG,CAAC,CAAC;AACxB;AACA,gBAAgB,IAAI,GAAG,KAAK,CAAC;AAC7B,SAAS;AACT,QAAQ,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,QAAQ,IAAI,IAAI;AAChB,YAAY,MAAM;AAClB,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAC7D,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC;AACzB,CAAC;AACD,MAAM,GAAG,8BAA8B,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,MAAM,WAAW,6BAA6B,CAAC,IAAI,EAAE,EAAE,KAAK,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AACxF;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE;AAChD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5B,QAAQ,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AAC/D,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;AAC9B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;AACpC,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClH,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC7B,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;AAC1B,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAClF,QAAQ,KAAK,GAAG,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;AACpC,QAAQ,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE;AAC3B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACrF,QAAQ,GAAG,IAAI,IAAI,CAAC;AACpB,QAAQ,OAAO,GAAG,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE;AACnC,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC;AAC3D,QAAQ,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC;AACzC,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,IAAI,IAAI;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK;AACzB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACtD,IAAI,IAAI,OAAO,IAAI,GAAG,GAAG,CAAC;AAC1B,QAAQ,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAC9B,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA,SAAS,KAAK,CAAC,GAAG,EAAE;AACpB,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,KAAK,KAAK;AAC3B,YAAY,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC;AAC9C,gBAAgB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC3E,YAAY,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AAChE,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,MAAM,KAAK;AAC5B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1F,gBAAgB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AACjF,YAAY,OAAO,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtE,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE;AAC1C,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;AAC9B,QAAQ,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;AAC7D,IAAI,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE;AAC9D,QAAQ,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAClD,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,KAAK,KAAK;AAC3B,YAAY,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC;AAC9C,gBAAgB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC5E,YAAY,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,CAAC;AAC1E,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,MAAM,KAAK;AAC5B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1F,gBAAgB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AAClF,YAAY,OAAO,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;AAC/E,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,SAAS,aAAa,CAAC,EAAE,EAAE;AAC3B,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU;AAChC,QAAQ,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AAC/D,IAAI,OAAO,UAAU,GAAG,IAAI,EAAE;AAC9B,QAAQ,IAAI;AACZ,YAAY,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE,GAAG;AACrB,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,SAAS,QAAQ,CAAC,GAAG,EAAE,EAAE,EAAE;AAC3B,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU;AAChC,QAAQ,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AAC1D,IAAI,OAAO;AACX,QAAQ,MAAM,CAAC,IAAI,EAAE;AACrB,YAAY,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;AAC7C,gBAAgB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC/E,YAAY,MAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACpD,YAAY,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;AAC1D,YAAY,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,YAAY,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC3C,YAAY,OAAO,GAAG,CAAC;AACvB,SAAS;AACT,QAAQ,MAAM,CAAC,IAAI,EAAE;AACrB,YAAY,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;AAC7C,gBAAgB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC/E,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,MAAM,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1D,YAAY,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AACjD,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AACxC,gBAAgB,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC;AACrD,oBAAoB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACxD,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD,MAAM,MAAM,mBAAmB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,kEAAkE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACpJ,MAAM,SAAS,mBAAmB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,kEAAkE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACvJ;AACA;AACA,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE,MAAM,MAAM,mBAAmB,SAAS,CAAC,4DAA4D,CAAC,CAAC;AACvG,MAAM,WAAW,mBAAmB,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3G,MAAM,aAAa,mBAAmB,KAAK,CAAC,QAAQ,CAAC,kCAAkC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACpG,MAAM,kBAAkB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AACxF;AACA;AACA;AACA,SAAS,aAAa,CAAC,GAAG,EAAE;AAC5B,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;AACxB,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxD,QAAQ,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAChC,YAAY,GAAG,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA,SAAS,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,GAAG,CAAC,EAAE;AACxD,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AAC9B,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClC,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAChC,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACjE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK;AACvB,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC9B,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,IAAI,GAAG,IAAI,aAAa,CAAC;AACzB,IAAI,OAAO,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9E,CAAC;AACD;AACA;AACA;AACA,SAAS,SAAS,CAAC,QAAQ,EAAE;AAC7B,IAAI,MAAM,cAAc,GAAG,QAAQ,KAAK,QAAQ,GAAG,CAAC,GAAG,UAAU,CAAC;AAClE,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAClC,IAAI,MAAM,eAAe,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;AACrD,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE;AAC/C,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ;AACtC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,2CAA2C,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3F,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACnF,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oDAAoD,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AACnG,QAAQ,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AAC9D,QAAQ,IAAI,KAAK,KAAK,KAAK,IAAI,YAAY,GAAG,KAAK;AACnD,YAAY,MAAM,IAAI,SAAS,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACjF,QAAQ,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;AAC7C,QAAQ,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;AACjE,QAAQ,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,EAAE;AACrC,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ;AACnC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,0CAA0C,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,QAAQ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;AACrE,YAAY,MAAM,IAAI,SAAS,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvG;AACA,QAAQ,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAQ,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,WAAW,EAAE;AACxD,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC;AACrE,QAAQ,GAAG,GAAG,OAAO,CAAC;AACtB,QAAQ,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAQ,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC7C,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,uDAAuD,CAAC,CAAC,CAAC;AACvF,QAAQ,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC9C,QAAQ,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC/C,QAAQ,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AACvE,QAAQ,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChE,QAAQ,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;AAChE,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,QAAQ,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,SAAS,aAAa,CAAC,GAAG,EAAE;AAChC,QAAQ,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACrD,QAAQ,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC;AAChG,CAAC;AACD,MAAM,MAAM,mBAAmB,SAAS,CAAC,QAAQ,CAAC,CAAC;AACnD,MAAM,OAAO,mBAAmB,SAAS,CAAC,SAAS,CAAC,CAAC;AACrD;AACA,MAAM,OAAO,GAAG;AAChB,IAAI,MAAM,EAAE;AACZ,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,WAAW,CAACA,QAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AAC1D,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,WAAW,CAACA,QAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AAC1D,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AAC7C,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AAC7C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AAChD,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AAChD,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,QAAQ,QAAQ,EAAE,MAAM,CAAC,OAAO;AAChC,QAAQ,QAAQ,EAAE,MAAM,CAAC,SAAS;AAClC,QAAQ,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,KAAK;AAClD,YAAY,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACvD,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,KAAK;AACzC,YAAY,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACjE,YAAY,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACrC,SAAS;AACT,KAAK;AACL,IAAI,OAAO,EAAE;AACb,QAAQ,QAAQ,EAAE,OAAO,CAAC,OAAO;AACjC,QAAQ,QAAQ,EAAE,OAAO,CAAC,SAAS;AACnC,QAAQ,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,KAAK,KAAK;AAClD,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACxD,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,KAAK;AACzC,YAAY,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAClE,YAAY,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACrC,SAAS;AACT,KAAK;AACL,CAAC,CAAC;AACF;AACA,SAAS,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AACjC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE;AAC5B,QAAQ,MAAM,IAAI,SAAS,CAAC,CAAC,gCAAgC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACxF,KAAK;AACL,CAAC;AACD,SAAS,QAAQ,CAAC,GAAG,EAAE;AACvB,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;AAC5C,QAAQ,MAAM,IAAI,SAAS,CAAC,oCAAoC,GAAG,GAAG,CAAC,CAAC;AACxE,KAAK;AACL,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AAC9B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1E,KAAK;AACL,CAAC;AASD,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,gBAAgB,EAAE;AACvC,QAAQ,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAC;AAC3D,KAAK;AACL,CAAC;AACD,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE;AACnC,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,QAAQ,MAAM,IAAI,SAAS,CAAC,CAAC,8BAA8B,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACrF,KAAK;AACL,CAAC;AAUD;AACA,MAAME,KAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1B,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1B,SAAS,QAAQ,CAAC,GAAG,EAAE;AACvB,IAAI,IAAI,GAAG,IAAI,KAAK;AACpB,QAAQ,OAAO,CAAC,CAAC;AACjB,IAAI,IAAI,GAAG,IAAI,OAAO;AACtB,QAAQ,OAAO,CAAC,CAAC;AACjB,IAAI,IAAI,GAAG,IAAI,WAAW;AAC1B,QAAQ,OAAO,CAAC,CAAC;AACjB,IAAI,IAAI,GAAG,IAAI,mBAAmB;AAClC,QAAQ,OAAO,CAAC,CAAC;AACjB,IAAI,IAAI,GAAG,IAAI,mCAAmC;AAClD,QAAQ,OAAO,EAAE,CAAC;AAClB,IAAI,IAAI,GAAG,IAAI,mEAAmE,EAAE;AACpF,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL,IAAI,MAAM,IAAI,SAAS,CAAC,qEAAqE,CAAC,CAAC;AAC/F,CAAC;AACD,SAAS,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE;AAC9C,IAAI,IAAI,IAAI,KAAK,SAAS;AAC1B,QAAQ,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC;AACrC,IAAI,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACzC,IAAI,OAAO,GAAG,GAAGA,KAAG,EAAE;AACtB,QAAQ,MAAM,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;AACjC,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACjC,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,aAAa;AACb,YAAY,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC;AACnC,KAAK;AACL,IAAI,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC;AACD;AACA,SAAS,UAAU,CAAC,MAAM,EAAE;AAC5B,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC9C,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,gCAAgC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAClD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;AAC3D,QAAQ,IAAI,IAAI,GAAG,CAAC,CAAC;AACrB,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACpC,YAAY,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,SAAS;AACT,QAAQ,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AAC7B,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAE;AACvB,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,wCAAwC,CAAC,CAAC,CAAC;AAClG,SAAS;AACT,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;AAC9C,YAAY,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC,SAAS;AACT,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxB,CAAC;AACD;AACA,SAAS,QAAQ,CAAC,GAAG,EAAE;AACvB,IAAI,IAAI,GAAG,IAAI,IAAI;AACnB,QAAQ,OAAO,CAAC,CAAC;AACjB,IAAI,IAAI,GAAG,IAAI,MAAM;AACrB,QAAQ,OAAO,CAAC,CAAC;AACjB,IAAI,IAAI,GAAG,IAAI,UAAU;AACzB,QAAQ,OAAO,CAAC,CAAC;AACjB,IAAI,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;AAClF,CAAC;AACD,SAAS,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE;AAC9C,IAAI,IAAI,IAAI,KAAK,SAAS;AAC1B,QAAQ,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC;AACrC,IAAI,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACzC,IAAI,OAAO,GAAG,GAAG,CAAC,EAAE;AACpB,QAAQ,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,aAAa;AACb,YAAY,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7B,MAAM,EAAE,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7B,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC;AACD,SAAS,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE;AAChC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrB,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,IAAI,KAAK,SAAS;AAC1B,QAAQ,IAAI,GAAG,GAAG,CAAC;AACnB,IAAI,IAAI,GAAG,GAAG,IAAI,EAAE;AACpB,QAAQ,MAAM,IAAI,SAAS,CAAC,CAAC,sCAAsC,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACtF,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,SAAS,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE;AACjD,IAAI,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAClC,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC;AACrC,IAAI,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACzC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC/C,QAAQ,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,QAAQ,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AACvC,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,aAAa;AACb,YAAY,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,KAAK;AACL,IAAI,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AAClC,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;AACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,QAAQ,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AASD;AACA,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;AACzE,SAAS2B,QAAM,CAAC,IAAI,GAAG,EAAE,EAAE;AAC3B,IAAI,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AAC/C,QAAQ,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;AAC9D,CAAC;AACD,SAAS,MAAM,CAAC,KAAK,EAAE;AACvB,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI;AAC5C,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;AAChC,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;AACjC,QAAQ,MAAM,CAAC,KAAK,CAAC,EAAE;AACvB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ;AACtC,QAAQ,OAAO,KAAK,KAAK,QAAQ;AACjC,QAAQ,KAAK,YAAY,UAAU,EAAE;AACrC,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AACjC,QAAQ,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC,EAAE;AACjD,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE;AAC/C,IAAI,IAAI,IAAI,KAAK,SAAS;AAC1B,QAAQ,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5B,IAAI,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChD,IAAI,MAAM,MAAM,GAAG,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC9D,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC7B,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;AACtB,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC/D,IAAI,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,QAAQ,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC5B,QAAQ,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC;AAC3B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/B,IAAI,OAAO,OAAO,CAAC,KAAK,QAAQ;AAChC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjB,UAAU,CAAC,CAAC;AACZ,CAAC;AACD,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE;AAC9B,IAAI,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,UAAU,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChC,UAAU,CAAC,CAAC;AACZ,CAAC;AACD,SAAS,YAAY,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;AACzD,IAAI,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,UAAU,GAAG,UAAU,CAAC;AAClE,IAAI,IAAI,UAAU,GAAG,UAAU,KAAK,CAAC,EAAE;AACvC,QAAQ,MAAM,IAAI,SAAS,CAAC,CAAC,oBAAoB,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACvF,KAAK;AACL,IAAI,IAAI,GAAG,KAAK,UAAU,EAAE;AAC5B,QAAQ,MAAM,IAAI,SAAS,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7E,KAAK;AACL,IAAI,IAAI,GAAG,GAAG,UAAU,KAAK,CAAC,EAAE;AAChC,QAAQ,MAAM,IAAI,SAAS,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACjF,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AACpC,QAAQ,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,CAAC;AACnC,QAAQ,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,UAAU,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AAaD;AACA,SAAS,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACzC,IAAI,IAAI,IAAI,YAAY,WAAW,EAAE;AACrC,QAAQ,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK;AACL,SAAS,IAAI,IAAI,YAAY,UAAU,EAAE;AACzC,QAAQ,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC9C,KAAK;AACL,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAClC,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AAClE,QAAQ,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK;AACL,SAAS,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC9C,KAAK;AACL,SAAS,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC9C,KAAK;AACL,SAAS,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC9C,KAAK;AACL,SAAS,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;AACxC,QAAQ,OAAO,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,MAAM,IAAI,SAAS,CAAC,qBAAqB,GAAG,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;AACrE,CAAC;AACD;AACA,MAAM,IAAI,SAAS,UAAU,CAAC;AAC9B,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;AACpC,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;AACpC,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;AACpC,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;AACpC,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;AACpC,IAAI,SAAS,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;AACpC,IAAI,SAAS,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE;AACnC,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,EAAE;AACtC,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,EAAE;AAC1C,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,EAAE;AAC1C,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,EAAE;AAC1C,IAAI,SAAS,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,EAAE;AAC5C,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,EAAE;AAC1C,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE;AACxC,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE;AACxC,IAAI,SAAS,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,EAAE;AACvC,IAAI,SAAS,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,EAAE;AACxC,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE;AACpC,IAAI,OAAO,MAAM,CAAC,IAAI,GAAG,EAAE,EAAE;AAC7B,QAAQ,MAAM,IAAI,GAAGA,QAAM,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE;AACzB,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AACpD,QAAQ,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACpC,QAAQ,IAAI,IAAI,YAAY,IAAI;AAChC,YAAY,IAAI,KAAK,SAAS,EAAE;AAChC,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACvD,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,MAAM,GAAG;AACjB,QAAQ,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,MAAM,GAAG;AACjB,QAAQ,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,MAAM,GAAG;AACjB,QAAQ,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,MAAM,GAAG;AACjB,QAAQ,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,EAAE,GAAG;AACb,QAAQ,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC;AAClC,KAAK;AACL,IAAI,IAAI,MAAM,GAAG;AACjB,QAAQ,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE;AAC1B,QAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,IAAI;AACtC,cAAc,IAAI,CAAC,OAAO,EAAE;AAC5B,cAAc,IAAI,CAAC;AACnB,QAAQ,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE;AAC1B,QAAQ,MAAM,KAAK,GAAG,CAAC,MAAM,KAAK,IAAI;AACtC,cAAc,IAAI,CAAC,OAAO,EAAE;AAC5B,cAAc,IAAI,CAAC;AACnB,QAAQ,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,MAAM,GAAG;AACb,QAAQ,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,MAAM,MAAM,GAAG7B,QAAM,CAAC,IAAI,CAAC,CAAC;AACpC,QAAQ,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,CAAC,OAAO,EAAE;AACrB,QAAQ,IAAI,OAAO,KAAK,SAAS,EAAE;AACnC,YAAY,OAAO,GAAG,cAAc,CAAC;AACrC,SAAS;AACT,QAAQ,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACrC,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE;AAC7B,QAAQ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;AACpD,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrC,QAAQ,OAAO,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE;AAC9B,QAAQ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;AACrD,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrC,QAAQ,OAAO,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,GAAG,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;AACzC,IAAI,MAAM,GAAG,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;AACzC,IAAI,QAAQ,GAAG,EAAE,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;AAC/C,IAAI,SAAS,GAAG,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AACvD,IAAI,SAAS,GAAG,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AACvD,IAAI,SAAS,GAAG,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AACvD,IAAI,MAAM,CAAC,IAAI,EAAE;AACjB,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE;AAClB,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;AACnD,QAAQ,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE;AACtB,QAAQ,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC3D,QAAQ,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE;AACvB,QAAQ,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;AACzB,QAAQ,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC9D,QAAQ,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE;AACzB,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC5B,KAAK;AACL,IAAI,UAAU,CAAC,MAAM,EAAE;AACvB,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3D,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE;AACtB,QAAQ,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE;AACvB,QAAQ,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE;AACrB,QAAQ,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,QAAQ,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AACzC,QAAQ,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE;AAC3B,QAAQ,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACtD,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;AACnB,QAAQ,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,WAAW,CAAC,GAAG,EAAE,MAAM,EAAE;AACpC,QAAQ,IAAI,GAAG,GAAG,IAAI,EAAE;AACxB,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACpC,SAAS;AACT,aAAa,IAAI,GAAG,GAAG,OAAO,EAAE;AAChC,YAAY,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9D,SAAS;AACT,aAAa,IAAI,GAAG,GAAG,WAAW,EAAE;AACpC,YAAY,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9D,SAAS;AACT,aAAa,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,oBAAoB,EAAE;AACrD,YAAY,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9D,SAAS;AACT,aAAa;AACb,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1D,SAAS;AACT,KAAK;AACL,CAAC;AACD,SAAS,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;AACzC,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC1C,CAAC;AACD,SAAS,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACvC,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACpD,CAAC;AACD,SAAS,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;AACzC,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC1C,CAAC;AACD,SAAS,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACvC,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,CAAC;AACD,SAAS,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACvC,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACpD,CAAC;AACD,SAAS,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACvC,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACxC,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE;AACpC,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE;AAChC,QAAQ,QAAQ,GAAG,eAAe,CAAC;AACnC,KAAK;AACL,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC/C,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE;AAC/C,IAAI,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;AAChD,IAAI,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACxC,QAAQ,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AACD,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE;AAChD,IAAI,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;AACjD,IAAI,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAClC,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACxC,QAAQ,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;AACD,SAAS,UAAU,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;AACvD,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;AACzC,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AAC/D,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC;AACD,MAAM,MAAM,CAAC;AACb,IAAI,WAAW,CAAC,IAAI,EAAE;AACtB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;AAC9B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChF,SAAS;AACT,QAAQ,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1C,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACrC,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,WAAW,CAAC,MAAM,EAAE;AACxB,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACrC,QAAQ,QAAQ,IAAI;AACpB,YAAY,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,IAAI;AACxC,gBAAgB,OAAO,GAAG,CAAC;AAC3B,YAAY,MAAM,GAAG,KAAK,IAAI;AAC9B,gBAAgB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACnD,YAAY,MAAM,GAAG,KAAK,IAAI;AAC9B,gBAAgB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACnD,YAAY,MAAM,GAAG,KAAK,IAAI;AAC9B,gBAAgB,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACnD,YAAY;AACZ,gBAAgB,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAClE,SAAS;AACT,KAAK;AACL,CAAC;AACD,SAAS,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE;AAClC,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AACtC;;AC/wCA,MAAO,KAAA,GAAA,SAAQ,CAAA,KAAA,CAAA;AACR,MAAA,EAAA,GAAA,KAAQ,CAAA,CAAA,CAAA;AAEf,MAAA,EAAO,GAAM,KAAE,CAAG,CAAA,CAAA;AAEX,MAAA,EAAA,GAAA,EAAA,CAAM,EAAG,KAAA,CAAA,EAAA,EAAY,CAAA,EAAA,KAAA,CAAA,EAAA,EAAA,CAAA;AACrB,MAAA,GAAA,GAAA,MAAS,CAAA,CAAA,CAAA,CAAA;AACT,MAAA,GAAA,GAAA,MAAS,CAAA,CAAA,CAAA,CAAA;AACT,MAAA,GAAA,GAAA,MAAS,CAAA,CAAA,CAAA,CAAA;AACT,MAAA,GAAA,GAAA,MAAS,CAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;ACDT,MAAA,IAAA,GAAA,CAAM,MAAY,GAAA,CAAA,CAAA,EAAA,EAAA,CAAM;AAE/B,MAAA,QAAa,CAAQ,KAAA,GAAA,CAAA,CAAA,EAAA,EAAc,CAAA,CAAA;AAGnC,MAAA,QAAa,CAAQ,EAAA,GAAA,KAAA,GAAQ,CAAM,CAAA,EAAA,GAAA,EAAA,EAAI;AAIvC,MAAA8B,UAAA,GAAgB;;;;;;;;;;;;;;;;;;;;;;;;;ACfA,SAAA,EAAA,CAAA,KAAA,EAAA,OACd,EAAS;AAOX,IAAA,IAAA,KAAA,KAAA,KAAA;AAYA,QAAA,MAAA,IAAA,KAAA,CAAgB,OAAQ,IAChB,mBACJ,CAAA,CAAA;AASJ,CAAA;AAUA,SAAA,IAAA,CAAA,KAAwB,EAAA,MACrB,GAAe,KAAA,EAChB;AAQF,IAAA,IAAA,CAAA,MAAA;AAeA,QAAA,OAAA,KAAA,CAAA;AAOA,IAAA,MAAA,IAAA,KAAA,CAAA;AAOA,CAAA;AAMA,SAAA,IAAA,CAAA,mBAA8B,EAAA;AAO9B,IAAgB,MAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClFhB,MAAa,UAAA,GAAAC,OAAA,CAAA,EAAA,UAAc,CAAU,CAAA;MAC7B,UAAO,GAAA,SAAA,CAAA,eAAA,CAAA;QAED,GAAI,WAAU;AAI1B,MAAU,KAAM,SAAA,UAAmB,CAAA;IAInC,cAAgB,CAAU,GAAA,EAAA,CAAA,EAAA;AAI1B,IAAA,OAAO,GAAA,CAAA,CAAA,EAAA;AAKM,QAAA,OAAA,CAAA,CAAC,GAAa,CAAA,CAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAMvB;IAIJ,OAAO,GAAM,CAAA,CAAA,EAAA;QAIT,OAAS,IAAA,KAEZ,CAAA,CAAA,CAAA,CAAA;KAEG;IAIJ,OAAI,GAAW,CAAA,CAAA,EAAA;QAIX,OAAO,CAAM,CAAA,GAAA,CAAA,CAAA,IAAO,KAEvB,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KAEG;AAMJ,IAAA,OAAI,QAAQ,CAAA,OAAc,MAAO,EAAA;AAKjC,QAAI,MAAQ,GAAA,GAAA,IAAU,MAAW,CAAA,KAAA,EAAA,EAAA,CAAA,CAAA,GAAA,CAAA;AAKjC,QAAI,OAAQC,QAAqB,CAAA,GAAA,EAAA,MAAA,CAAA,CAAA;AAKjC,KAAA;AAKA,IAAA,WAAa,CAAA,CAAA,EAAA;AAMb,QAAK,MAAQ,CAAA,GAAAC,KAAc,cAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAMhC,QAAK,KAAK,CAAG,QAAA,CAAA,CAAA,MAAmB,CAAA,CAAA;AAMhC,QAAK,KAAK,CAAG,IAAA,CAAA,GAAA,CAAA,CAAA,IAAc,CAAK,EAAA,EAAA,CAAA,CAAA;AAMhC,KAAA;AAMA,IAAA,IAAA,OAAiB;AAKjB,QAAA,WAAmB,IAAA,CAAA,IAAA,CAAA,CAAA;AAKpB,KAAA;AAED,IAAA,IAAA,GAAA,GAAA;QACQ,OAAW,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA;KACX;AACN,IAAA,IAAA,GAAO,GAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAkB,KAAA;IAC9B,GAAM,CAAA,OAAK;QAEL,MAAO,CAAA,GACX,KAAK,CAAA,SACL,CAAA,CAAA;AAeF,QAAA,MAAe,CAAA,GAAA,EAAE,KAAK,IAAG,CAAA,GAAA,EAAA,CAAA,CAAU,GAAI,CAAK,CAAA;QAMtC,OAAqB,IAAA,KAAA,CAAA,CAAA,CAAA,CAAA;KAErB;AAQN,IAAA,GAAA,CAAA,KAAS,EAAE;AAGT,QAAA,MAAA,CAAA,GAAI,KAAA,CAAA,GACJ,CAAC,KAAS,CAAA,CAAA;QAMR,MAAO,CAAA,GAAA,EAAO,CAEjB,GAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,GAAA,CAAA,CAAA;QAEG,OAAO,IAEV,KAAA,CAAA,CAAA,CAAA,CAAA;KAEG;IAIJ,GAAI,CAAA,KAAU,EAAA;QAIV,MAAS,CAAA,GAAA,KAAA,CAAA,GAEZ,CAAA,KAAA,CAAA,CAAA;QAEG,MAAS,CAAA,GAAA,EAAA,CAAM,GAElB,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,GAAA,CAAA,CAAA;QAEG,OAAA,IAAc,KAAA,CAAA,CAAA,CAAO,CAExB;KAEG;IAIJ,GAAI,CAAA,KAAA,EAAO;AAMX,QAAI,MAAQ,CAAA,GAAA,KAAA,CAAU,GAAI,CAAO,KAAA,CAAA,CAAA;AAKjC,QAAK,MAAI,CAAA,GAAA,EAAA,CAAU,GAAI,CAAK,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAM5B,QAAK,OAAI,IAAA,KAAc,CAAK,CAAA,CAAA,CAAA;AAM5B,KAAA;AAMA,IAAA,GAAA,CAAA,OAAiB;AAGlB,QAAA,MAAA,CAAA,GAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzPD,MAAM,UAAU,mBAAmB,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AACvD,MAAM,IAAI,mBAAmB,MAAM,CAAC,EAAE,CAAC,CAAC;AACxC;AACA,SAAS,OAAO,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,EAAE;AAChC,IAAI,IAAI,EAAE;AACV,QAAQ,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,EAAE,CAAC;AAClF,IAAI,OAAO,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;AACtF,CAAC;AACD,SAAS,KAAK,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,EAAE;AAChC,IAAI,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAI,IAAI,EAAE,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACzC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACpB,CAAC;AACD,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE;AACA,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACvD;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACxD;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/D,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D;AACA,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;AAC7B,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7B;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD;AACA,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D,MAAM,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D;AACA;AACA,SAASC,KAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC7B,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AACtC,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;AAChE,CAAC;AACD;AACA,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACnE,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC9E,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACpF,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACvF,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACrG,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAGhG;AACA,MAAM,GAAG,GAAG;AACZ,IAAI,OAAO,EAAE,KAAK,EAAE,KAAK;AACzB,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AAClC,SAAIA,KAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACjD,CAAC,CAAC;AACF,YAAe,GAAG;;ACzDlB;AACA;AACA,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,mBAAmB,CAAC,MAAMC,KAAG,CAAC,KAAK,CAAC;AAChE,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,IAAI,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB;AAC1F,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC1B;AACA,MAAM,UAAU,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AACvD,MAAM,UAAU,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AAChD,MAAM,MAAM,SAASpC,MAAI,CAAC;AACjC,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,KAAK;AACL;AACA,IAAI,GAAG,GAAG;AACV,QAAQ,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACxF,QAAQ,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAChF,KAAK;AACL;AACA,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACxE,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1B;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE;AAClD,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACnD,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC;AAC1D,SAAS;AACT,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtC;AACA,YAAY,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,MAAM,GAAG,GAAGoC,KAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGA,KAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACzG,YAAY,MAAM,GAAG,GAAGA,KAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGA,KAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACzG;AACA,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAY,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAY,MAAM,GAAG,GAAGA,KAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACrG,YAAY,MAAM,GAAG,GAAGA,KAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AACrG;AACA,YAAY,MAAM,IAAI,GAAGA,KAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACpF,YAAY,MAAM,IAAI,GAAGA,KAAG,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC1F,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACrC,YAAY,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACtF;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACrC;AACA,YAAY,MAAM,OAAO,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG,YAAY,MAAM,OAAO,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG;AACA,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AAChD;AACA;AACA,YAAY,MAAM,IAAI,GAAGA,KAAG,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,YAAY,MAAM,GAAG,GAAGA,KAAG,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,YAAY,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;AACjC;AACA,YAAY,MAAM,OAAO,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG,YAAY,MAAM,OAAO,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAGA,KAAG,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACrG,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;AAC3D,YAAY,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;AAC3D,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;AAC3E,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,YAAY,MAAM,GAAG,GAAGA,KAAG,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACtD,YAAY,EAAE,GAAGA,KAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AACpD,YAAY,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AACzB,SAAS;AACT;AACA,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,GAAGA,KAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;AAC/E,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACjF,KAAK;AACL,IAAI,UAAU,GAAG;AACjB,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,CAAC;AAsEM,MAAMC,QAAM,mBAAmB7C,iBAAe,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;;AChOzE;AACA;AACA,MAAM,GAAG,mBAAmB,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACnG,MAAM,EAAE,mBAAmB,UAAU,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACxE,MAAM,EAAE,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3D,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;AAChB,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;AAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC1B,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAC9B,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,MAAM,MAAM,mBAAmB;AAC/B,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5D,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5D,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5D,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5D,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5D,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,OAAO,mBAAmB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,MAAM,OAAO,mBAAmB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,MAAM,EAAE,mBAAmB,IAAI,WAAW,CAAC;AAC3C,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAC9D,CAAC,CAAC,CAAC;AACH,MAAM,EAAE,mBAAmB,IAAI,WAAW,CAAC;AAC3C,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAC9D,CAAC,CAAC,CAAC;AACH;AACA,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,KAAK,IAAI,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;AACxE;AACA,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3B,IAAI,IAAI,KAAK,KAAK,CAAC;AACnB,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,SAAS,IAAI,KAAK,KAAK,CAAC;AACxB,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAClC,SAAS,IAAI,KAAK,KAAK,CAAC;AACxB,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5B,SAAS,IAAI,KAAK,KAAK,CAAC;AACxB,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AACD;AACA,MAAM,GAAG,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AACzC,MAAM,SAAS,SAASQ,MAAI,CAAC;AACpC,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/B,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,GAAG,GAAG;AACV,QAAQ,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AAC5C,QAAQ,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC5B,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;AAChD,YAAY,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAClD;AACA,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC;AAClJ;AACA;AACA,QAAQ,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE;AAChD,YAAY,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC;AACrC,YAAY,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACnD,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AACrD,YAAY,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3D,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACzC,gBAAgB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAChG,gBAAgB,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;AAC1E,aAAa;AACb;AACA,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACzC,gBAAgB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACjG,gBAAgB,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;AAC1E,aAAa;AACb,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9I,KAAK;AACL,IAAI,UAAU,GAAG;AACjB,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChC,KAAK;AACL,CAAC;AACD;AACA;AACA;AACA;AACO,MAAM,SAAS,mBAAmBR,iBAAe,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;;AC5E/E,SAAA,MAAA,CAAA;AAKA,IAAgB,MAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAMhB,IAAgB,OAAA,IAAA,CAAA,GAAA,CAAA8C,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAMhB,CAAA;AAKA,SAAA,MAAA,CAAA,GAAsB,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9CN,SAAA,MAAA,CAAA,IAAA,EAAA;AAIhB,IAAA,OAAA,IAAA,CAAA,MAAA,CAAA;AAcA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACIgB,SAAA,WAAA,CAAA,MAAU,EACxB;AAOF,IAAgB,MAAA,GAAA,GAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAU,CACxB;AAOF,IAAA,QAAA,IAAA;AAaA,QAAA,MAAA,GAAA,CAAA,MAAgB,KAAY,EAAA;AAa5B,YAAgB,OAAA,IAAA,CAAA;AAQhB,QAAgB,MAAA,GAAA,CAAA,MAAA,KAAA,EAAA,IAAW,GACnB,CAAA,CAAA,CAAA,SACN;AAQF,YAAA,OAAA,IAAA,CAAgB;AAQhB,QAAA,MAAA,GAAA,CAAA,MAAgB,WAAY,GAAA,CAAA,CAAA,CAAM,KAAQ,IAAA;AAO1C,YAAgB,OAAA,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkBhB,MAAA,SAAA,GAAA,eAA6B,EAAA,SACtB,GAAQ,uBAEP,CAAA;AASR,SAAA,MAAA,CAAA,IAA6B,EAAA,SAC3B,EAAK,UAAQ,EACb,UAAW,GAAG,KAAA,EACZ;AAcJ,IAAgBC,WAAA,CAAA,IAAA,EAAA,UACd,CAAA,CAAA;AASF,IAAA,MAAA,GAAA,GAAA,IAAA,CAAA,gBAA6B,CAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC3ID,KAAA,EAAA,UAAA;AAED,IAAgB,IAAA,EAAA,EAAA;AAuBhB,CAAA,CAAA;AAagB,SAAA,YAAA,CAAA,MAAA,EACd,IAAM,EAAA,QAAS,OACJ,EAAA;AAIb,IAAA,MAAA,EAAA,IAAA,EAAA,KAAA,uBAAwC,CAAM,MAAA;AAO9C,IAAA,MAAA,OAAA,GAAAC,eACE,CAAK;AAiCP,IAAA,MAAA,GAAA,GAAA,UAA4B,CAAA,MAAA,EAAA,IAAG,CAC7B,CAAK,GAAA,CAAA;AAaP,IAAgB,MAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,MACR,CAAA;AAUR,IAAgB,MAAA,GAAA,GAAA,CAAA,CAAA,EAAA,GAAA,EAAA,KAAA,EAAA,IAAA,EAAe,IAAK,EAAE,OAAU,CAAA,CAAA;AAOhD,IAAgB,MAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,MACd,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzHF,MAAgB,OAAA,GAAA,SAAA,CAAA,eAAmB;AAQnB,MAAA,KAAA,GAAA,OAAA,CAAA;AASA,SAAA,OAAA,CAAA,CAAA,EAAA;AAMhB,IAAgB,MAAA,EAAA,GAAA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAM,EACnB,CAAA,CAAG;AAUN,IAAA,OAAA,EAAA,CAAA,QAAA,EAAgB;AAehB,CAAA;AAeA,SAAA,QAAgB,CAAE,KACZ;AAYN,IAAA,MAAA,CAAA,GAAA,KAAA,CAAA;AAcA,IAAA,QAAA,CAAA,OAAA,CAAA,aACW,IAAA,CACP,SAAS;AAQb,SAAgB,OAAA,CAAA,CAAA,CAAA,KAAA,QACd,IAAK,OAAQ,EACb,CAAK,KAAA,QAAA,CAAQ,EACX;AAOJ,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 18, 19, 20]}