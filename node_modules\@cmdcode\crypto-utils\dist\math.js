import { Buff } from '@cmdcode/buff-utils';
import { mod, pow } from '@noble/curves/abstract/modular';
import { _N, _P, _0n } from './const.js';
export { mod, pow, pow2, invert } from '@noble/curves/abstract/modular';
export const modN = (x) => mod(x, _N);
export const modP = (x) => mod(x, _P);
export const powN = (x, exp) => pow(x, exp, _N);
export const on_curve = (x) => {
    return typeof x === 'bigint' && _0n < x && x < _P;
};
export const in_field = (x) => {
    return typeof x === 'bigint' && _0n < x && x < _N;
};
export function mod_bytes(bytes) {
    const b = Buff.bytes(bytes).big;
    return Buff.big(modN(b), 32);
}
//# sourceMappingURL=math.js.map