{"version": 3, "file": "sig.js", "sourceRoot": "", "sources": ["../src/sig.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAS,MAAS,qBAAqB,CAAA;AACpD,OAAO,EAAE,GAAG,EAAE,MAAiB,YAAY,CAAA;AAC3C,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAQ,UAAU,CAAA;AACzC,OAAO,EAAE,cAAc,EAAE,MAAM,WAAW,CAAA;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAc,WAAW,CAAA;AAE1C,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAQ,WAAW,CAAA;AACpD,OAAO,EAAE,WAAW,EAAe,MAAM,aAAa,CAAA;AAEtD,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAErC,MAAM,UAAU,IAAI,CAClB,OAAgB,EAChB,MAAgB,EAChB,OAAsB;IAMtB,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,CAAA;IAChC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,GAAG,CAAA;IAGrC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAE7B,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;IAE1B,IAAI,KAAK,KAAK,SAAS,EAAE;QAEvB,IAAI,KAAK;YAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAA;QAE1B,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;KACnB;IAED,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAA;IAElB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAA;IAEnC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA;IAE9B,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAErB,IAAI,OAAO,KAAK,SAAS,EAAE;QAEzB,IAAI,KAAK;YAAE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAA;QAE1B,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;KACrB;IAED,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAA;IAElB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAA;IAE3C,MAAM,EAAE,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;IAC3D,MAAM,CAAC,GAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAExB,MAAM,CAAC,GAAI,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAEzC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;IAEpC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,EAAE,EAAE,CAAC,CAAC,GAAG,CAAE,CAAC,CAAA;AACjC,CAAC;AAED,MAAM,UAAU,MAAM,CACpB,SAAiB,EACjB,OAAiB,EACjB,MAAiB,EACjB,OAAuB;IAMvB,MAAM,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAA;IAEvC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAE/B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;IAEjC,IAAI,GAAG,CAAC,MAAM,GAAG,EAAE,EAAE;QACnB,OAAO,MAAM,CAAC,IAAI,CAAC,iCAAiC,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;KACnF;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IAEvB,MAAM,CAAC,GAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;IAE/B,MAAM,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAE9B,MAAM,CAAC,GAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;IAE3B,MAAM,CAAC,GAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAE/B,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;IAE7B,MAAM,EAAE,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IAErD,MAAM,CAAC,GAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAExB,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;IAEvB,MAAM,CAAC,GAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAGrB,IAAI,CAAC,CAAC,OAAO,EAAE;QACb,OAAO,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,MAAM,CAAC,CAAA;KACtE;IAGD,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;QACnB,OAAO,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAA;KAC7D;IAGD,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;QACvB,OAAO,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAA;KAC/E;IAED,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;AAC5B,CAAC;AAED,MAAM,UAAU,OAAO,CACrB,SAAiB,EACjB,OAAiB,EACjB,OAAiB,EACjB,OAAiB;IAEjB,MAAM,GAAG,GAAK,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;IACnC,MAAM,GAAG,GAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IACjC,MAAM,GAAG,GAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IACjC,MAAM,IAAI,GAAI,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAC9C,MAAM,KAAK,GAAG,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACpD,MAAM,IAAI,GAAI,MAAM,CAAC,mBAAmB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAA;IACjF,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAA;IACzB,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAA;IAClC,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;IACtC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAC7B,CAAC;AAED,MAAM,UAAU,SAAS,CACvB,OAAgB,EAChB,MAAgB,EAChB,OAAsB;IAEtB,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,YAAY,GAAG,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,OAAO,CAAC,CAAA;IAC/E,IAAI,CAAQ,CAAA;IACZ,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;KACtB;SAAM,IAAI,QAAQ,KAAK,SAAS,EAAE;QACjC,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;KACrC;SAAM;QACL,MAAM,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;QAEnD,MAAM,CAAC,GAAG,MAAM,CAAC,aAAa,EAAE,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;QAExD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAA;QAExC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,EAAE,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAE,CAAC,CAAA;KAChD;IAED,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAEnE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA,CAAC,CAAC,CAAC,CAAA;IACrD,OAAO,EAAE,CAAC,IAAI,CAAA;AAChB,CAAC"}