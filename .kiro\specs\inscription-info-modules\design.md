# Design Document

## Overview

本设计文档描述了在BRC20铭文铸造工具中添加铭文信息模块和持有人列表模块的技术实现方案。这两个模块将在现有的"内存池透视"容器上方以并排布局的形式展示，为用户提供全面的铭文数据和持有人统计信息。

## Architecture

### 布局结构
- 新模块将插入到现有的 `.rune-progress-container` 容器之前
- 采用Bootstrap的栅格系统实现响应式布局
- 桌面端：两个模块并排显示（各占50%宽度）
- 移动端：两个模块垂直堆叠显示

### 数据流
1. 用户输入Tick名称触发数据更新
2. 通过现有的API接口获取铭文和持有人数据
3. 使用JavaScript动态更新模块内容
4. 实现错误处理和加载状态显示

## Components and Interfaces

### 1. 铭文信息模块 (Inscription Info Module)

#### HTML结构
```html
<div class="bg-white p-4 mb-4 rounded shadow transaction-container inscription-info-container">
  <div class="inscription-header">
    <h6 class="bold-label">铭文信息</h6>
  </div>
  <div class="inscription-content">
    <!-- 铭文基本信息 -->
  </div>
</div>
```

#### 显示内容
- 铭文名称 (Tick)
- 总供应量 (Total Supply)
- 已铸造数量 (Minted)
- 铸造进度条和百分比
- 单次铸造限制 (Limit per Mint)
- 部署者地址 (Deployer)
- 部署哈希 (Deploy Hash)

### 2. 持有人列表模块 (Holders List Module)

#### HTML结构
```html
<div class="bg-white p-4 mb-4 rounded shadow transaction-container holders-list-container">
  <div class="holders-header">
    <h6 class="bold-label">持有人列表</h6>
    <span class="holders-count">持有人: <span id="holdersTotal">132</span></span>
  </div>
  <div class="holders-content">
    <!-- 持有人列表 -->
  </div>
</div>
```

#### 显示内容
- 持有人总数统计
- Top 6持有人列表
- 每个持有人显示：排名、地址（截断显示）、持有数量、占比

### 3. 响应式容器

#### 桌面端布局
```html
<div class="row mb-4 inscription-modules-row">
  <div class="col-md-6">
    <!-- 铭文信息模块 -->
  </div>
  <div class="col-md-6">
    <!-- 持有人列表模块 -->
  </div>
</div>
```

#### 移动端适配
- 使用 `col-12` 类实现全宽显示
- 垂直堆叠布局
- 调整内边距和字体大小

## Data Models

### 铭文信息数据结构
```javascript
const inscriptionInfo = {
  tick: "ordi",
  totalSupply: "21000000",
  mintedSupply: "20139340",
  mintProgress: 95.9,
  limitPerMint: "1000",
  deployer: "bc1q...h6gd",
  deployHash: "1868bb...ecc8",
  holders: 132
};
```

### 持有人数据结构
```javascript
const holdersData = {
  total: 132,
  list: [
    {
      rank: 1,
      address: "bc1a30...56j5",
      amount: "750.00",
      percentage: "7.8036%"
    },
    // ... 更多持有人数据
  ]
};
```

## Error Handling

### 数据加载错误
- 显示友好的错误提示信息
- 提供重试机制
- 保持界面布局稳定

### 网络连接问题
- 实现超时处理
- 显示加载状态指示器
- 提供手动刷新选项

## Testing Strategy

### 单元测试
- 测试数据格式化函数
- 测试响应式布局切换
- 测试错误处理逻辑

### 集成测试
- 测试与现有Tick输入框的联动
- 测试API数据获取和显示
- 测试暗黑模式切换

### 用户体验测试
- 测试不同屏幕尺寸下的显示效果
- 测试加载性能
- 测试交互响应速度

## Implementation Notes

### CSS样式继承
- 复用现有的 `.transaction-container` 样式
- 使用现有的颜色变量和字体设置
- 确保暗黑模式兼容性

### JavaScript集成
- 集成到现有的Tick变更监听器中
- 复用现有的API调用模式
- 保持代码结构一致性

### 性能优化
- 实现数据缓存机制
- 避免重复API调用
- 优化DOM更新操作