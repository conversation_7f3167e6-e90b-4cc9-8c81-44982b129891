import { Buff, Bytes } from '@cmdcode/buff-utils';
import { PointData } from './types.js';
export declare const Noble: import("@noble/curves/abstract/weierstrass").ProjConstructor<bigint>;
export declare function is_even(p: PointData): boolean;
export declare function is_point(point?: unknown): point is PointData;
export declare function is_valid(point?: unknown): point is PointData;
export declare function assert_valid(p: unknown): asserts p is PointData;
export declare function negate(a: PointData): PointData | null;
export declare function add(a: PointData | null, b: PointData | null): PointData | null;
export declare function sub(a: PointData | null, b: PointData | null): PointData | null;
export declare function eq(a: PointData | null, b: PointData | null): boolean;
export declare function mul(a: PointData | null, b: Bytes): PointData | null;
export declare function gen(b: Bytes): PointData;
export declare function lift_x(bytes: Bytes, xonly?: boolean): PointData;
export declare function to_bytes(p: PointData): Buff;
//# sourceMappingURL=point.d.ts.map