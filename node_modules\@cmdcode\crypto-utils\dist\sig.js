import { Buff } from '@cmdcode/buff-utils';
import { _0n } from './const.js';
import { Field, Point } from './ecc.js';
import { get_shared_key } from './ecdh.js';
import { digest } from './hash.js';
import { get_pubkey, convert_32 } from './keys.js';
import { sign_config } from './config.js';
import * as assert from './assert.js';
export function sign(message, secret, options) {
    const opt = sign_config(options);
    const { adaptor, tweak, xonly } = opt;
    const m = Buff.bytes(message);
    let dp = Field.mod(secret);
    if (tweak !== undefined) {
        if (xonly)
            dp = dp.negated;
        dp = dp.add(tweak);
    }
    const P = dp.point;
    const d = (xonly) ? dp.negated : dp;
    const n = gen_nonce(m, d, opt);
    let kp = Field.mod(n);
    if (adaptor !== undefined) {
        if (xonly)
            kp = kp.negated;
        kp = kp.add(adaptor);
    }
    const R = kp.point;
    const k = (xonly) ? kp.negated.big : kp.big;
    const ch = digest('BIP0340/challenge', R.x.raw, P.x.raw, m);
    const c = Field.mod(ch);
    const s = Field.mod(k + (c.big * d.big));
    const rx = (xonly) ? R.x.raw : R.raw;
    return Buff.join([rx, s.raw]);
}
export function verify(signature, message, pubkey, options) {
    const { throws } = sign_config(options);
    const msg = Buff.bytes(message);
    const sig = Buff.bytes(signature);
    if (sig.length < 64) {
        return assert.fail('Signature length is too small: ' + String(sig.length), throws);
    }
    assert.size(pubkey, 32);
    const P = Point.from_x(pubkey);
    const rx = sig.subarray(0, 32);
    const R = Point.from_x(rx);
    const s = sig.subarray(32, 64);
    const sG = Field.mod(s).point;
    const ch = digest('BIP0340/challenge', R.x, P.x, msg);
    const c = Field.mod(ch);
    const eP = P.mul(c.big);
    const r = sG.sub(eP);
    if (R.hasOddY) {
        return assert.fail('Signature R value has odd Y coordinate!', throws);
    }
    if (R.x.big === _0n) {
        return assert.fail('Signature R value is infinite!', throws);
    }
    if (R.x.big !== r.x.big) {
        return assert.fail(`Signature is invalid! R: ${R.x.hex} r:${r.x.hex}`, throws);
    }
    return R.x.big === r.x.big;
}
export function recover(signature, message, pub_key, rec_key) {
    const sig = Buff.bytes(signature);
    const msg = Buff.bytes(message);
    const pub = Buff.bytes(pub_key);
    const seed = get_shared_key(rec_key, pub_key);
    const nonce = digest('BIP0340/nonce', seed, message);
    const chal = digest('BIP0340/challenge', sig.slice(0, 32), convert_32(pub), msg);
    const c = new Field(chal);
    const k = new Field(nonce).negated;
    const s = new Field(sig.slice(32, 64));
    return s.sub(k).div(c).buff;
}
export function gen_nonce(message, secret, options) {
    const { aux, nonce, nonce_tweaks = [], recovery, xonly } = sign_config(options);
    let n;
    if (nonce !== undefined) {
        n = Buff.bytes(nonce);
    }
    else if (recovery !== undefined) {
        n = get_shared_key(secret, recovery);
    }
    else {
        const seed = (aux === null) ? Buff.num(0, 32) : aux;
        const a = digest('BIP0340/aux', seed ?? Buff.random(32));
        const t = Buff.bytes(secret).big ^ a.big;
        n = Buff.join([t, get_pubkey(secret, xonly)]);
    }
    let sn = Field.mod(digest('BIP0340/nonce', n, Buff.bytes(message)));
    nonce_tweaks.forEach(e => { sn = sn.add(e).negated; });
    return sn.buff;
}
//# sourceMappingURL=sig.js.map