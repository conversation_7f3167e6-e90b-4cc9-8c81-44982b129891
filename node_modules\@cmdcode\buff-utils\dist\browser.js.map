{"version": 3, "file": "browser.js", "sources": ["../node_modules/@noble/hashes/esm/_assert.js", "../node_modules/@noble/hashes/esm/utils.js", "../node_modules/@noble/hashes/esm/_sha2.js", "../node_modules/@noble/hashes/esm/sha256.js", "../node_modules/@scure/base/lib/esm/index.js", "../src/encode.ts", "../src/assert.ts", "../src/format/big.ts", "../src/format/num.ts", "../src/format/str.ts", "../src/utils.ts", "../src/buff.ts", "../src/format/bin.ts"], "sourcesContent": ["function number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`Wrong positive integer: ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`Expected boolean, not ${b}`);\n}\nfunction bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new Error('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n    if (typeof hash !== 'function' || typeof hash.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\nexport { number, bool, bytes, hash, exists, output };\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n//# sourceMappingURL=_assert.js.map", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nconst u8a = (a) => a instanceof Uint8Array;\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!u8a(data))\n        throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\n// For runtime check if class implements interface\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "import { exists, output } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nexport class SHA2 extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        exists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        exists(this);\n        output(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_sha2.js.map", "import { SHA2 } from './_sha2.js';\nimport { rotr, wrapConstructor } from './utils.js';\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n//# sourceMappingURL=sha256.js.map", "/*! scure-base - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Utilities\n/**\n * @__NO_SIDE_EFFECTS__\n */\nexport function assertNumber(n) {\n    if (!Number.isSafeInteger(n))\n        throw new Error(`Wrong integer: ${n}`);\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n    // Wrap call in closure so JIT can inline calls\n    const wrap = (a, b) => (c) => a(b(c));\n    // Construct chain of args[-1].encode(args[-2].encode([...]))\n    const encode = Array.from(args)\n        .reverse()\n        .reduce((acc, i) => (acc ? wrap(acc, i.encode) : i.encode), undefined);\n    // Construct chain of args[0].decode(args[1].decode(...))\n    const decode = args.reduce((acc, i) => (acc ? wrap(acc, i.decode) : i.decode), undefined);\n    return { encode, decode };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(alphabet) {\n    return {\n        encode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('alphabet.encode input should be an array of numbers');\n            return digits.map((i) => {\n                assertNumber(i);\n                if (i < 0 || i >= alphabet.length)\n                    throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n                return alphabet[i];\n            });\n        },\n        decode: (input) => {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('alphabet.decode input should be array of strings');\n            return input.map((letter) => {\n                if (typeof letter !== 'string')\n                    throw new Error(`alphabet.decode: not string element=${letter}`);\n                const index = alphabet.indexOf(letter);\n                if (index === -1)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n                return index;\n            });\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n    if (typeof separator !== 'string')\n        throw new Error('join separator should be string');\n    return {\n        encode: (from) => {\n            if (!Array.isArray(from) || (from.length && typeof from[0] !== 'string'))\n                throw new Error('join.encode input should be array of strings');\n            for (let i of from)\n                if (typeof i !== 'string')\n                    throw new Error(`join.encode: non-string input=${i}`);\n            return from.join(separator);\n        },\n        decode: (to) => {\n            if (typeof to !== 'string')\n                throw new Error('join.decode input should be string');\n            return to.split(separator);\n        },\n    };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n    assertNumber(bits);\n    if (typeof chr !== 'string')\n        throw new Error('padding chr should be string');\n    return {\n        encode(data) {\n            if (!Array.isArray(data) || (data.length && typeof data[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of data)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.encode: non-string input=${i}`);\n            while ((data.length * bits) % 8)\n                data.push(chr);\n            return data;\n        },\n        decode(input) {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of input)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.decode: non-string input=${i}`);\n            let end = input.length;\n            if ((end * bits) % 8)\n                throw new Error('Invalid padding: string should have whole number of bytes');\n            for (; end > 0 && input[end - 1] === chr; end--) {\n                if (!(((end - 1) * bits) % 8))\n                    throw new Error('Invalid padding: string has too much padding');\n            }\n            return input.slice(0, end);\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize(fn) {\n    if (typeof fn !== 'function')\n        throw new Error('normalize fn should be function');\n    return { encode: (from) => from, decode: (to) => fn(to) };\n}\n/**\n * Slow: O(n^2) time complexity\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix(data, from, to) {\n    // base 1 is impossible\n    if (from < 2)\n        throw new Error(`convertRadix: wrong from=${from}, base cannot be less than 2`);\n    if (to < 2)\n        throw new Error(`convertRadix: wrong to=${to}, base cannot be less than 2`);\n    if (!Array.isArray(data))\n        throw new Error('convertRadix: data should be array');\n    if (!data.length)\n        return [];\n    let pos = 0;\n    const res = [];\n    const digits = Array.from(data);\n    digits.forEach((d) => {\n        assertNumber(d);\n        if (d < 0 || d >= from)\n            throw new Error(`Wrong integer: ${d}`);\n    });\n    while (true) {\n        let carry = 0;\n        let done = true;\n        for (let i = pos; i < digits.length; i++) {\n            const digit = digits[i];\n            const digitBase = from * carry + digit;\n            if (!Number.isSafeInteger(digitBase) ||\n                (from * carry) / from !== carry ||\n                digitBase - digit !== from * carry) {\n                throw new Error('convertRadix: carry overflow');\n            }\n            carry = digitBase % to;\n            const rounded = Math.floor(digitBase / to);\n            digits[i] = rounded;\n            if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n                throw new Error('convertRadix: carry overflow');\n            if (!done)\n                continue;\n            else if (!rounded)\n                pos = i;\n            else\n                done = false;\n        }\n        res.push(carry);\n        if (done)\n            break;\n    }\n    for (let i = 0; i < data.length - 1 && data[i] === 0; i++)\n        res.push(0);\n    return res.reverse();\n}\nconst gcd = /* @__NO_SIDE_EFFECTS__ */ (a, b) => (!b ? a : gcd(b, a % b));\nconst radix2carry = /*@__NO_SIDE_EFFECTS__ */ (from, to) => from + (to - gcd(from, to));\n/**\n * Implemented with numbers, because BigInt is 5x slower\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix2(data, from, to, padding) {\n    if (!Array.isArray(data))\n        throw new Error('convertRadix2: data should be array');\n    if (from <= 0 || from > 32)\n        throw new Error(`convertRadix2: wrong from=${from}`);\n    if (to <= 0 || to > 32)\n        throw new Error(`convertRadix2: wrong to=${to}`);\n    if (radix2carry(from, to) > 32) {\n        throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n    }\n    let carry = 0;\n    let pos = 0; // bitwise position in current element\n    const mask = 2 ** to - 1;\n    const res = [];\n    for (const n of data) {\n        assertNumber(n);\n        if (n >= 2 ** from)\n            throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n        carry = (carry << from) | n;\n        if (pos + from > 32)\n            throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n        pos += from;\n        for (; pos >= to; pos -= to)\n            res.push(((carry >> (pos - to)) & mask) >>> 0);\n        carry &= 2 ** pos - 1; // clean carry, otherwise it will cause overflow\n    }\n    carry = (carry << (to - pos)) & mask;\n    if (!padding && pos >= from)\n        throw new Error('Excess padding');\n    if (!padding && carry)\n        throw new Error(`Non-zero padding: ${carry}`);\n    if (padding && pos > 0)\n        res.push(carry >>> 0);\n    return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n    assertNumber(num);\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix.encode input should be Uint8Array');\n            return convertRadix(Array.from(bytes), 2 ** 8, num);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix.decode input should be array of strings');\n            return Uint8Array.from(convertRadix(digits, num, 2 ** 8));\n        },\n    };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n    assertNumber(bits);\n    if (bits <= 0 || bits > 32)\n        throw new Error('radix2: bits should be in (0..32]');\n    if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n        throw new Error('radix2: carry overflow');\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix2.encode input should be Uint8Array');\n            return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix2.decode input should be array of strings');\n            return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction unsafeWrapper(fn) {\n    if (typeof fn !== 'function')\n        throw new Error('unsafeWrapper fn should be function');\n    return function (...args) {\n        try {\n            return fn.apply(null, args);\n        }\n        catch (e) { }\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction checksum(len, fn) {\n    assertNumber(len);\n    if (typeof fn !== 'function')\n        throw new Error('checksum fn should be function');\n    return {\n        encode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.encode: input should be Uint8Array');\n            const checksum = fn(data).slice(0, len);\n            const res = new Uint8Array(data.length + len);\n            res.set(data);\n            res.set(checksum, data.length);\n            return res;\n        },\n        decode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.decode: input should be Uint8Array');\n            const payload = data.slice(0, -len);\n            const newChecksum = fn(payload).slice(0, len);\n            const oldChecksum = data.slice(-len);\n            for (let i = 0; i < len; i++)\n                if (newChecksum[i] !== oldChecksum[i])\n                    throw new Error('Invalid checksum');\n            return payload;\n        },\n    };\n}\nexport const utils = { alphabet, chain, checksum, radix, radix2, join, padding };\n// RFC 4648 aka RFC 3548\n// ---------------------\nexport const base16 = /* @__PURE__ */ chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\nexport const base32 = /* @__PURE__ */ chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), padding(5), join(''));\nexport const base32hex = /* @__PURE__ */ chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), padding(5), join(''));\nexport const base32crockford = /* @__PURE__ */ chain(radix2(5), alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'), join(''), normalize((s) => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1')));\nexport const base64 = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\nexport const base64url = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\nexport const base64urlnopad = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), join(''));\n// base58 code\n// -----------\nconst genBase58 = (abc) => chain(radix(58), alphabet(abc), join(''));\nexport const base58 = /* @__PURE__ */ genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\nexport const base58flickr = /* @__PURE__ */ genBase58('123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ');\nexport const base58xrp = /* @__PURE__ */ genBase58('rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz');\n// xmr ver is done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n// Block encoding significantly reduces quadratic complexity of base58.\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\nexport const base58xmr = {\n    encode(data) {\n        let res = '';\n        for (let i = 0; i < data.length; i += 8) {\n            const block = data.subarray(i, i + 8);\n            res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length], '1');\n        }\n        return res;\n    },\n    decode(str) {\n        let res = [];\n        for (let i = 0; i < str.length; i += 11) {\n            const slice = str.slice(i, i + 11);\n            const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n            const block = base58.decode(slice);\n            for (let j = 0; j < block.length - blockLen; j++) {\n                if (block[j] !== 0)\n                    throw new Error('base58xmr: wrong padding');\n            }\n            res = res.concat(Array.from(block.slice(block.length - blockLen)));\n        }\n        return Uint8Array.from(res);\n    },\n};\nexport const base58check = /* @__PURE__ */ (sha256) => chain(checksum(4, (data) => sha256(sha256(data))), base58);\nconst BECH_ALPHABET = /* @__PURE__ */ chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bech32Polymod(pre) {\n    const b = pre >> 25;\n    let chk = (pre & 0x1ffffff) << 5;\n    for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n        if (((b >> i) & 1) === 1)\n            chk ^= POLYMOD_GENERATORS[i];\n    }\n    return chk;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n    const len = prefix.length;\n    let chk = 1;\n    for (let i = 0; i < len; i++) {\n        const c = prefix.charCodeAt(i);\n        if (c < 33 || c > 126)\n            throw new Error(`Invalid prefix (${prefix})`);\n        chk = bech32Polymod(chk) ^ (c >> 5);\n    }\n    chk = bech32Polymod(chk);\n    for (let i = 0; i < len; i++)\n        chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n    for (let v of words)\n        chk = bech32Polymod(chk) ^ v;\n    for (let i = 0; i < 6; i++)\n        chk = bech32Polymod(chk);\n    chk ^= encodingConst;\n    return BECH_ALPHABET.encode(convertRadix2([chk % 2 ** 30], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n    const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n    const _words = radix2(5);\n    const fromWords = _words.decode;\n    const toWords = _words.encode;\n    const fromWordsUnsafe = unsafeWrapper(fromWords);\n    function encode(prefix, words, limit = 90) {\n        if (typeof prefix !== 'string')\n            throw new Error(`bech32.encode prefix should be string, not ${typeof prefix}`);\n        if (!Array.isArray(words) || (words.length && typeof words[0] !== 'number'))\n            throw new Error(`bech32.encode words should be array of numbers, not ${typeof words}`);\n        const actualLength = prefix.length + 7 + words.length;\n        if (limit !== false && actualLength > limit)\n            throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n        const lowered = prefix.toLowerCase();\n        const sum = bechChecksum(lowered, words, ENCODING_CONST);\n        return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n    }\n    function decode(str, limit = 90) {\n        if (typeof str !== 'string')\n            throw new Error(`bech32.decode input should be string, not ${typeof str}`);\n        if (str.length < 8 || (limit !== false && str.length > limit))\n            throw new TypeError(`Wrong string length: ${str.length} (${str}). Expected (8..${limit})`);\n        // don't allow mixed case\n        const lowered = str.toLowerCase();\n        if (str !== lowered && str !== str.toUpperCase())\n            throw new Error(`String must be lowercase or uppercase`);\n        str = lowered;\n        const sepIndex = str.lastIndexOf('1');\n        if (sepIndex === 0 || sepIndex === -1)\n            throw new Error(`Letter \"1\" must be present between prefix and data only`);\n        const prefix = str.slice(0, sepIndex);\n        const _words = str.slice(sepIndex + 1);\n        if (_words.length < 6)\n            throw new Error('Data must be at least 6 characters long');\n        const words = BECH_ALPHABET.decode(_words).slice(0, -6);\n        const sum = bechChecksum(prefix, words, ENCODING_CONST);\n        if (!_words.endsWith(sum))\n            throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n        return { prefix, words };\n    }\n    const decodeUnsafe = unsafeWrapper(decode);\n    function decodeToBytes(str) {\n        const { prefix, words } = decode(str, false);\n        return { prefix, words, bytes: fromWords(words) };\n    }\n    return { encode, decode, decodeToBytes, decodeUnsafe, fromWords, fromWordsUnsafe, toWords };\n}\nexport const bech32 = /* @__PURE__ */ genBech32('bech32');\nexport const bech32m = /* @__PURE__ */ genBech32('bech32m');\nexport const utf8 = {\n    encode: (data) => new TextDecoder().decode(data),\n    decode: (str) => new TextEncoder().encode(str),\n};\nexport const hex = /* @__PURE__ */ chain(radix2(4), alphabet('0123456789abcdef'), join(''), normalize((s) => {\n    if (typeof s !== 'string' || s.length % 2)\n        throw new TypeError(`hex.decode: expected string, got ${typeof s} with length ${s.length}`);\n    return s.toLowerCase();\n}));\n// prettier-ignore\nconst CODERS = {\n    utf8, hex, base16, base32, base64, base64url, base58, base58xmr\n};\nconst coderTypeError = 'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\nexport const bytesToString = (type, bytes) => {\n    if (typeof type !== 'string' || !CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (!(bytes instanceof Uint8Array))\n        throw new TypeError('bytesToString() expects Uint8Array');\n    return CODERS[type].encode(bytes);\n};\nexport const str = bytesToString; // as in python, but for bytes only\nexport const stringToBytes = (type, str) => {\n    if (!CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (typeof str !== 'string')\n        throw new TypeError('stringToBytes() expects string');\n    return CODERS[type].decode(str);\n};\nexport const bytes = stringToBytes;\n", null, null, null, null, null, null, null, null], "names": ["exists", "instance", "checkFinished", "destroyed", "Error", "finished", "output", "out", "b", "lengths", "Uint8Array", "length", "includes", "bytes", "min", "outputLen", "u8a", "a", "createView", "arr", "DataView", "buffer", "byteOffset", "byteLength", "rotr", "word", "shift", "Uint32Array", "toBytes", "data", "str", "TextEncoder", "encode", "utf8ToBytes", "Hash", "clone", "this", "_cloneInto", "wrapConstructor", "hashCons", "hashC", "msg", "update", "digest", "tmp", "blockLen", "create", "SHA2", "constructor", "padOffset", "isLE", "super", "pos", "view", "len", "take", "Math", "set", "subarray", "process", "dataView", "roundClean", "digestInto", "fill", "i", "value", "setBigUint64", "_32n", "BigInt", "_u32_max", "wh", "Number", "wl", "h", "l", "setUint32", "oview", "outLen", "state", "get", "res", "slice", "destroy", "to", "Maj", "c", "SHA256_K", "IV", "SHA256_W", "SHA256", "A", "B", "C", "D", "E", "F", "G", "H", "offset", "getUint32", "W15", "W2", "s0", "s1", "T1", "T2", "sha256", "chain", "args", "wrap", "Array", "from", "reverse", "reduce", "acc", "undefined", "decode", "alphabet", "digits", "isArray", "map", "input", "letter", "index", "indexOf", "join", "separator", "split", "padding", "bits", "chr", "push", "end", "convertRadix", "for<PERSON>ach", "d", "carry", "done", "digit", "digitBase", "isSafeInteger", "rounded", "floor", "gcd", "radix2carry", "convertRadix2", "mask", "n", "radix2", "revPadding", "unsafeWrapper", "fn", "apply", "e", "base64", "base64url", "genBase58", "abc", "num", "base58", "base58check", "checksum", "payload", "newChe<PERSON><PERSON>", "oldChecksum", "BECH_ALPHABET", "POLYMOD_GENERATORS", "bech32Polymod", "pre", "chk", "bechChecksum", "prefix", "words", "encodingConst", "charCodeAt", "v", "genBech32", "encoding", "ENCODING_CONST", "_words", "fromWords", "to<PERSON><PERSON>s", "fromWordsUnsafe", "limit", "TypeError", "lowered", "toLowerCase", "toUpperCase", "sepIndex", "lastIndexOf", "sum", "endsWith", "actualLength", "decodeToBytes", "decodeUnsafe", "bech32", "bech32m", "to_words", "within_size", "size", "_0n", "_255n", "_256n", "ec", "dc", "TextDecoder", "strToBytes", "getRandomValues", "globalThis", "window", "crypto", "random", "is_hex", "numToBuff", "big", "bigToBuff", "bin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raw", "rawToBuff", "str<PERSON><PERSON><PERSON><PERSON>", "hex", "hex<PERSON><PERSON><PERSON><PERSON>", "json", "json<PERSON>oBuff", "base64ToBuff", "b64url", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bech32ToBuff", "bech32mToBuff", "b58chk", "b58chk<PERSON>oBuff", "fmt.strToBytes", "fmt.bytesToStr", "parse", "parse_data", "is_bytes", "util.is_bytes", "util.is_hex", "rand", "util.random", "Buff", "now", "stamp", "Date", "endian", "fmt.buffer_data", "to_num", "to_big", "to_str", "to_hex", "to_bin", "to_hash", "id", "stream", "fmt.bytesToNum", "bins", "binary"], "mappings": "wCAoBA,SAASA,EAAOC,EAAUC,GAAgB,GACtC,GAAID,EAASE,UACT,MAAM,IAAIC,MAAM,oCACpB,GAAIF,GAAiBD,EAASI,SAC1B,MAAM,IAAID,MAAM,wCACxB,CACA,SAASE,EAAOC,EAAKN,IAlBrB,SAAeO,KAAMC,GACjB,KAAMD,aAAaE,YACf,MAAM,IAAIN,MAAM,uBACpB,GAAIK,EAAQE,OAAS,IAAMF,EAAQG,SAASJ,EAAEG,QAC1C,MAAM,IAAIP,MAAM,iCAAiCK,oBAA0BD,EAAEG,SACrF,CAcIE,CAAMN,GACN,MAAMO,EAAMb,EAASc,UACrB,GAAIR,EAAII,OAASG,EACb,MAAM,IAAIV,MAAM,yDAAyDU,IAEjF;sECxBA,MAAME,EAAOC,GAAMA,aAAaP,WAKnBQ,EAAcC,GAAQ,IAAIC,SAASD,EAAIE,OAAQF,EAAIG,WAAYH,EAAII,YAEnEC,EAAO,CAACC,EAAMC,IAAWD,GAAS,GAAKC,EAAWD,IAASC,EAIxE,KADgF,KAA5D,IAAIhB,WAAW,IAAIiB,YAAY,CAAC,YAAaN,QAAQ,IAErE,MAAM,IAAIjB,MAAM,+CAiEb,SAASwB,EAAQC,GAGpB,GAFoB,iBAATA,IACPA,EAZD,SAAqBC,GACxB,GAAmB,iBAARA,EACP,MAAM,IAAI1B,MAAM,2CAA2C0B,GAC/D,OAAO,IAAIpB,YAAW,IAAIqB,aAAcC,OAAOF,GACnD,CAQeG,CAAYJ,KAClBb,EAAIa,GACL,MAAM,IAAIzB,MAAM,mCAAmCyB,GACvD,OAAOA,CACX,CAgBO,MAAMK,EAET,KAAAC,GACI,OAAOC,KAAKC,YACf,EASE,SAASC,EAAgBC,GAC5B,MAAMC,EAASC,GAAQF,IAAWG,OAAOd,EAAQa,IAAME,SACjDC,EAAML,IAIZ,OAHAC,EAAMzB,UAAY6B,EAAI7B,UACtByB,EAAMK,SAAWD,EAAIC,SACrBL,EAAMM,OAAS,IAAMP,IACdC,CACX,CC/GO,MAAMO,UAAab,EACtB,WAAAc,CAAYH,EAAU9B,EAAWkC,EAAWC,GACxCC,QACAf,KAAKS,SAAWA,EAChBT,KAAKrB,UAAYA,EACjBqB,KAAKa,UAAYA,EACjBb,KAAKc,KAAOA,EACZd,KAAK/B,UAAW,EAChB+B,KAAKzB,OAAS,EACdyB,KAAKgB,IAAM,EACXhB,KAAKjC,WAAY,EACjBiC,KAAKf,OAAS,IAAIX,WAAWmC,GAC7BT,KAAKiB,KAAOnC,EAAWkB,KAAKf,OAC/B,CACD,MAAAqB,CAAOb,GACH7B,EAAOoC,MACP,MAAMiB,KAAEA,EAAIhC,OAAEA,EAAMwB,SAAEA,GAAaT,KAE7BkB,GADNzB,EAAOD,EAAQC,IACElB,OACjB,IAAK,IAAIyC,EAAM,EAAGA,EAAME,GAAM,CAC1B,MAAMC,EAAOC,KAAK1C,IAAI+B,EAAWT,KAAKgB,IAAKE,EAAMF,GAEjD,GAAIG,IAASV,EAMbxB,EAAOoC,IAAI5B,EAAK6B,SAASN,EAAKA,EAAMG,GAAOnB,KAAKgB,KAChDhB,KAAKgB,KAAOG,EACZH,GAAOG,EACHnB,KAAKgB,MAAQP,IACbT,KAAKuB,QAAQN,EAAM,GACnBjB,KAAKgB,IAAM,OAXf,CACI,MAAMQ,EAAW1C,EAAWW,GAC5B,KAAOgB,GAAYS,EAAMF,EAAKA,GAAOP,EACjCT,KAAKuB,QAAQC,EAAUR,EAE9B,CAQJ,CAGD,OAFAhB,KAAKzB,QAAUkB,EAAKlB,OACpByB,KAAKyB,aACEzB,IACV,CACD,UAAA0B,CAAWvD,GACPP,EAAOoC,MACP9B,EAAOC,EAAK6B,MACZA,KAAK/B,UAAW,EAIhB,MAAMgB,OAAEA,EAAMgC,KAAEA,EAAIR,SAAEA,EAAQK,KAAEA,GAASd,KACzC,IAAIgB,IAAEA,GAAQhB,KAEdf,EAAO+B,KAAS,IAChBhB,KAAKf,OAAOqC,SAASN,GAAKW,KAAK,GAE3B3B,KAAKa,UAAYJ,EAAWO,IAC5BhB,KAAKuB,QAAQN,EAAM,GACnBD,EAAM,GAGV,IAAK,IAAIY,EAAIZ,EAAKY,EAAInB,EAAUmB,IAC5B3C,EAAO2C,GAAK,GAxExB,SAAsBX,EAAM/B,EAAY2C,EAAOf,GAC3C,GAAiC,mBAAtBG,EAAKa,aACZ,OAAOb,EAAKa,aAAa5C,EAAY2C,EAAOf,GAChD,MAAMiB,EAAOC,OAAO,IACdC,EAAWD,OAAO,YAClBE,EAAKC,OAAQN,GAASE,EAAQE,GAC9BG,EAAKD,OAAON,EAAQI,GACpBI,EAAIvB,EAAO,EAAI,EACfwB,EAAIxB,EAAO,EAAI,EACrBG,EAAKsB,UAAUrD,EAAamD,EAAGH,EAAIpB,GACnCG,EAAKsB,UAAUrD,EAAaoD,EAAGF,EAAItB,EACvC,CAiEQgB,CAAab,EAAMR,EAAW,EAAGuB,OAAqB,EAAdhC,KAAKzB,QAAauC,GAC1Dd,KAAKuB,QAAQN,EAAM,GACnB,MAAMuB,EAAQ1D,EAAWX,GACnB+C,EAAMlB,KAAKrB,UAEjB,GAAIuC,EAAM,EACN,MAAM,IAAIlD,MAAM,+CACpB,MAAMyE,EAASvB,EAAM,EACfwB,EAAQ1C,KAAK2C,MACnB,GAAIF,EAASC,EAAMnE,OACf,MAAM,IAAIP,MAAM,sCACpB,IAAK,IAAI4D,EAAI,EAAGA,EAAIa,EAAQb,IACxBY,EAAMD,UAAU,EAAIX,EAAGc,EAAMd,GAAId,EACxC,CACD,MAAAP,GACI,MAAMtB,OAAEA,EAAMN,UAAEA,GAAcqB,KAC9BA,KAAK0B,WAAWzC,GAChB,MAAM2D,EAAM3D,EAAO4D,MAAM,EAAGlE,GAE5B,OADAqB,KAAK8C,UACEF,CACV,CACD,UAAA3C,CAAW8C,GACPA,IAAOA,EAAK,IAAI/C,KAAKY,aACrBmC,EAAG1B,OAAOrB,KAAK2C,OACf,MAAMlC,SAAEA,EAAQxB,OAAEA,EAAMV,OAAEA,EAAMN,SAAEA,EAAQF,UAAEA,EAASiD,IAAEA,GAAQhB,KAO/D,OANA+C,EAAGxE,OAASA,EACZwE,EAAG/B,IAAMA,EACT+B,EAAG9E,SAAWA,EACd8E,EAAGhF,UAAYA,EACXQ,EAASkC,GACTsC,EAAG9D,OAAOoC,IAAIpC,GACX8D,CACV,EC1GL,MAEMC,EAAM,CAACnE,EAAGT,EAAG6E,IAAOpE,EAAIT,EAAMS,EAAIoE,EAAM7E,EAAI6E,EAI5CC,EAA2B,IAAI3D,YAAY,CAC7C,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WACpF,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UACpF,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UACpF,UAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,aAIlF4D,EAAqB,IAAI5D,YAAY,CACvC,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,aAIlF6D,EAA2B,IAAI7D,YAAY,IACjD,MAAM8D,UAAe1C,EACjB,WAAAC,GACIG,MAAM,GAAI,GAAI,GAAG,GAGjBf,KAAKsD,EAAY,EAARH,EAAG,GACZnD,KAAKuD,EAAY,EAARJ,EAAG,GACZnD,KAAKwD,EAAY,EAARL,EAAG,GACZnD,KAAKyD,EAAY,EAARN,EAAG,GACZnD,KAAK0D,EAAY,EAARP,EAAG,GACZnD,KAAK2D,EAAY,EAARR,EAAG,GACZnD,KAAK4D,EAAY,EAART,EAAG,GACZnD,KAAK6D,EAAY,EAARV,EAAG,EACf,CACD,GAAAR,GACI,MAAMW,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,GAAM7D,KACnC,MAAO,CAACsD,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAChC,CAED,GAAAxC,CAAIiC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACrB7D,KAAKsD,EAAQ,EAAJA,EACTtD,KAAKuD,EAAQ,EAAJA,EACTvD,KAAKwD,EAAQ,EAAJA,EACTxD,KAAKyD,EAAQ,EAAJA,EACTzD,KAAK0D,EAAQ,EAAJA,EACT1D,KAAK2D,EAAQ,EAAJA,EACT3D,KAAK4D,EAAQ,EAAJA,EACT5D,KAAK6D,EAAQ,EAAJA,CACZ,CACD,OAAAtC,CAAQN,EAAM6C,GAEV,IAAK,IAAIlC,EAAI,EAAGA,EAAI,GAAIA,IAAKkC,GAAU,EACnCV,EAASxB,GAAKX,EAAK8C,UAAUD,GAAQ,GACzC,IAAK,IAAIlC,EAAI,GAAIA,EAAI,GAAIA,IAAK,CAC1B,MAAMoC,EAAMZ,EAASxB,EAAI,IACnBqC,EAAKb,EAASxB,EAAI,GAClBsC,EAAK9E,EAAK4E,EAAK,GAAK5E,EAAK4E,EAAK,IAAOA,IAAQ,EAC7CG,EAAK/E,EAAK6E,EAAI,IAAM7E,EAAK6E,EAAI,IAAOA,IAAO,GACjDb,EAASxB,GAAMuC,EAAKf,EAASxB,EAAI,GAAKsC,EAAKd,EAASxB,EAAI,IAAO,CAClE,CAED,IAAI0B,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,GAAM7D,KACjC,IAAK,IAAI4B,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MACMwC,EAAMP,GADGzE,EAAKsE,EAAG,GAAKtE,EAAKsE,EAAG,IAAMtE,EAAKsE,EAAG,OAnEjD7E,EAoE4B6E,GAAGC,GApEP9E,EAoEU+E,GAAKV,EAAStB,GAAKwB,EAASxB,GAAM,EAE/DyC,GADSjF,EAAKkE,EAAG,GAAKlE,EAAKkE,EAAG,IAAMlE,EAAKkE,EAAG,KAC7BN,EAAIM,EAAGC,EAAGC,GAAM,EACrCK,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKD,EAAIW,EAAM,EACfX,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKc,EAAKC,EAAM,CACnB,CA/EG,IAACxF,EAiFLyE,EAAKA,EAAItD,KAAKsD,EAAK,EACnBC,EAAKA,EAAIvD,KAAKuD,EAAK,EACnBC,EAAKA,EAAIxD,KAAKwD,EAAK,EACnBC,EAAKA,EAAIzD,KAAKyD,EAAK,EACnBC,EAAKA,EAAI1D,KAAK0D,EAAK,EACnBC,EAAKA,EAAI3D,KAAK2D,EAAK,EACnBC,EAAKA,EAAI5D,KAAK4D,EAAK,EACnBC,EAAKA,EAAI7D,KAAK6D,EAAK,EACnB7D,KAAKqB,IAAIiC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EACjC,CACD,UAAApC,GACI2B,EAASzB,KAAK,EACjB,CACD,OAAAmB,GACI9C,KAAKqB,IAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC9BrB,KAAKf,OAAO0C,KAAK,EACpB,EAqBE,MAAM2C,EAAyBpE,GAAgB,IAAM,IAAImD;oEC/GhE,SAASkB,KAASC,GAEd,MAAMC,EAAO,CAAC5F,EAAGT,IAAO6E,GAAMpE,EAAET,EAAE6E,IAOlC,MAAO,CAAErD,OALM8E,MAAMC,KAAKH,GACrBI,UACAC,QAAO,CAACC,EAAKlD,IAAOkD,EAAML,EAAKK,EAAKlD,EAAEhC,QAAUgC,EAAEhC,aAASmF,GAG/CC,OADFR,EAAKK,QAAO,CAACC,EAAKlD,IAAOkD,EAAML,EAAKK,EAAKlD,EAAEoD,QAAUpD,EAAEoD,aAASD,GAEnF,CAKA,SAASE,EAASA,GACd,MAAO,CACHrF,OAASsF,IACL,IAAKR,MAAMS,QAAQD,IAAYA,EAAO3G,QAA+B,iBAAd2G,EAAO,GAC1D,MAAM,IAAIlH,MAAM,uDACpB,OAAOkH,EAAOE,KAAKxD,IAEf,GAAIA,EAAI,GAAKA,GAAKqD,EAAS1G,OACvB,MAAM,IAAIP,MAAM,iCAAiC4D,gBAAgBqD,EAAS1G,WAC9E,OAAO0G,EAASrD,EAAE,GACpB,EAENoD,OAASK,IACL,IAAKX,MAAMS,QAAQE,IAAWA,EAAM9G,QAA8B,iBAAb8G,EAAM,GACvD,MAAM,IAAIrH,MAAM,oDACpB,OAAOqH,EAAMD,KAAKE,IACd,GAAsB,iBAAXA,EACP,MAAM,IAAItH,MAAM,uCAAuCsH,KAC3D,MAAMC,EAAQN,EAASO,QAAQF,GAC/B,IAAe,IAAXC,EACA,MAAM,IAAIvH,MAAM,oBAAoBsH,gBAAqBL,KAC7D,OAAOM,CAAK,GACd,EAGd,CAIA,SAASE,EAAKC,EAAY,IACtB,GAAyB,iBAAdA,EACP,MAAM,IAAI1H,MAAM,mCACpB,MAAO,CACH4B,OAAS+E,IACL,IAAKD,MAAMS,QAAQR,IAAUA,EAAKpG,QAA6B,iBAAZoG,EAAK,GACpD,MAAM,IAAI3G,MAAM,gDACpB,IAAK,IAAI4D,KAAK+C,EACV,GAAiB,iBAAN/C,EACP,MAAM,IAAI5D,MAAM,iCAAiC4D,KACzD,OAAO+C,EAAKc,KAAKC,EAAU,EAE/BV,OAASjC,IACL,GAAkB,iBAAPA,EACP,MAAM,IAAI/E,MAAM,sCACpB,OAAO+E,EAAG4C,MAAMD,EAAU,EAGtC,CAKA,SAASE,EAAQC,EAAMC,EAAM,KAEzB,GAAmB,iBAARA,EACP,MAAM,IAAI9H,MAAM,gCACpB,MAAO,CACH,MAAA4B,CAAOH,GACH,IAAKiF,MAAMS,QAAQ1F,IAAUA,EAAKlB,QAA6B,iBAAZkB,EAAK,GACpD,MAAM,IAAIzB,MAAM,mDACpB,IAAK,IAAI4D,KAAKnC,EACV,GAAiB,iBAANmC,EACP,MAAM,IAAI5D,MAAM,oCAAoC4D,KAC5D,KAAQnC,EAAKlB,OAASsH,EAAQ,GAC1BpG,EAAKsG,KAAKD,GACd,OAAOrG,CACV,EACD,MAAAuF,CAAOK,GACH,IAAKX,MAAMS,QAAQE,IAAWA,EAAM9G,QAA8B,iBAAb8G,EAAM,GACvD,MAAM,IAAIrH,MAAM,mDACpB,IAAK,IAAI4D,KAAKyD,EACV,GAAiB,iBAANzD,EACP,MAAM,IAAI5D,MAAM,oCAAoC4D,KAC5D,IAAIoE,EAAMX,EAAM9G,OAChB,GAAKyH,EAAMH,EAAQ,EACf,MAAM,IAAI7H,MAAM,6DACpB,KAAOgI,EAAM,GAAKX,EAAMW,EAAM,KAAOF,EAAKE,IACtC,MAAQA,EAAM,GAAKH,EAAQ,GACvB,MAAM,IAAI7H,MAAM,gDAExB,OAAOqH,EAAMxC,MAAM,EAAGmD,EACzB,EAET,CAaA,SAASC,EAAaxG,EAAMkF,EAAM5B,GAE9B,GAAI4B,EAAO,EACP,MAAM,IAAI3G,MAAM,4BAA4B2G,iCAChD,GAAI5B,EAAK,EACL,MAAM,IAAI/E,MAAM,0BAA0B+E,iCAC9C,IAAK2B,MAAMS,QAAQ1F,GACf,MAAM,IAAIzB,MAAM,sCACpB,IAAKyB,EAAKlB,OACN,MAAO,GACX,IAAIyC,EAAM,EACV,MAAM4B,EAAM,GACNsC,EAASR,MAAMC,KAAKlF,GAM1B,IALAyF,EAAOgB,SAASC,IAEZ,GAAIA,EAAI,GAAKA,GAAKxB,EACd,MAAM,IAAI3G,MAAM,kBAAkBmI,IAAI,MAEjC,CACT,IAAIC,EAAQ,EACRC,GAAO,EACX,IAAK,IAAIzE,EAAIZ,EAAKY,EAAIsD,EAAO3G,OAAQqD,IAAK,CACtC,MAAM0E,EAAQpB,EAAOtD,GACf2E,EAAY5B,EAAOyB,EAAQE,EACjC,IAAKnE,OAAOqE,cAAcD,IACrB5B,EAAOyB,EAASzB,IAASyB,GAC1BG,EAAYD,GAAU3B,EAAOyB,EAC7B,MAAM,IAAIpI,MAAM,gCAEpBoI,EAAQG,EAAYxD,EACpB,MAAM0D,EAAUrF,KAAKsF,MAAMH,EAAYxD,GAEvC,GADAmC,EAAOtD,GAAK6E,GACPtE,OAAOqE,cAAcC,IAAYA,EAAU1D,EAAKqD,IAAUG,EAC3D,MAAM,IAAIvI,MAAM,gCACfqI,IAEKI,EAGNJ,GAAO,EAFPrF,EAAMY,EAGb,CAED,GADAgB,EAAImD,KAAKK,GACLC,EACA,KACP,CACD,IAAK,IAAIzE,EAAI,EAAGA,EAAInC,EAAKlB,OAAS,GAAiB,IAAZkB,EAAKmC,GAAUA,IAClDgB,EAAImD,KAAK,GACb,OAAOnD,EAAIgC,SACf,CACA,MAAM+B,EAAiC,CAAC9H,EAAGT,IAAQA,EAAQuI,EAAIvI,EAAGS,EAAIT,GAAfS,EACjD+H,EAAwC,CAACjC,EAAM5B,IAAO4B,GAAQ5B,EAAK4D,EAAIhC,EAAM5B,IAKnF,SAAS8D,EAAcpH,EAAMkF,EAAM5B,EAAI6C,GACnC,IAAKlB,MAAMS,QAAQ1F,GACf,MAAM,IAAIzB,MAAM,uCACpB,GAAI2G,GAAQ,GAAKA,EAAO,GACpB,MAAM,IAAI3G,MAAM,6BAA6B2G,KACjD,GAAI5B,GAAM,GAAKA,EAAK,GAChB,MAAM,IAAI/E,MAAM,2BAA2B+E,KAC/C,GAAI6D,EAAYjC,EAAM5B,GAAM,GACxB,MAAM,IAAI/E,MAAM,sCAAsC2G,QAAW5B,eAAgB6D,EAAYjC,EAAM5B,MAEvG,IAAIqD,EAAQ,EACRpF,EAAM,EACV,MAAM8F,EAAO,GAAK/D,EAAK,EACjBH,EAAM,GACZ,IAAK,MAAMmE,KAAKtH,EAAM,CAElB,GAAIsH,GAAK,GAAKpC,EACV,MAAM,IAAI3G,MAAM,oCAAoC+I,UAAUpC,KAElE,GADAyB,EAASA,GAASzB,EAAQoC,EACtB/F,EAAM2D,EAAO,GACb,MAAM,IAAI3G,MAAM,qCAAqCgD,UAAY2D,KAErE,IADA3D,GAAO2D,EACA3D,GAAO+B,EAAI/B,GAAO+B,EACrBH,EAAImD,MAAOK,GAAUpF,EAAM+B,EAAO+D,KAAU,GAChDV,GAAS,GAAKpF,EAAM,CACvB,CAED,GADAoF,EAASA,GAAUrD,EAAK/B,EAAQ8F,GAC3BlB,GAAW5E,GAAO2D,EACnB,MAAM,IAAI3G,MAAM,kBACpB,IAAK4H,GAAWQ,EACZ,MAAM,IAAIpI,MAAM,qBAAqBoI,KAGzC,OAFIR,GAAW5E,EAAM,GACjB4B,EAAImD,KAAKK,IAAU,GAChBxD,CACX,CAwBA,SAASoE,EAAOnB,EAAMoB,GAAa,GAE/B,GAAIpB,GAAQ,GAAKA,EAAO,GACpB,MAAM,IAAI7H,MAAM,qCACpB,GAAI4I,EAAY,EAAGf,GAAQ,IAAMe,EAAYf,EAAM,GAAK,GACpD,MAAM,IAAI7H,MAAM,0BACpB,MAAO,CACH4B,OAASnB,IACL,KAAMA,aAAiBH,YACnB,MAAM,IAAIN,MAAM,4CACpB,OAAO6I,EAAcnC,MAAMC,KAAKlG,GAAQ,EAAGoH,GAAOoB,EAAW,EAEjEjC,OAASE,IACL,IAAKR,MAAMS,QAAQD,IAAYA,EAAO3G,QAA+B,iBAAd2G,EAAO,GAC1D,MAAM,IAAIlH,MAAM,kDACpB,OAAOM,WAAWqG,KAAKkC,EAAc3B,EAAQW,EAAM,EAAGoB,GAAY,EAG9E,CAIA,SAASC,EAAcC,GACnB,GAAkB,mBAAPA,EACP,MAAM,IAAInJ,MAAM,uCACpB,OAAO,YAAawG,GAChB,IACI,OAAO2C,EAAGC,MAAM,KAAM5C,EACzB,CACD,MAAO6C,GAAM,CACrB,CACA,CAsCO,MAAMC,EAAyB/C,EAAMyC,EAAO,GAAI/B,EAAS,oEAAqEW,EAAQ,GAAIH,EAAK,KACzI8B,EAA4BhD,EAAMyC,EAAO,GAAI/B,EAAS,oEAAqEW,EAAQ,GAAIH,EAAK,KAInJ+B,EAAaC,IAAQlD,UA9FZmD,EA8FwB,GA5F5B,CACH9H,OAASnB,IACL,KAAMA,aAAiBH,YACnB,MAAM,IAAIN,MAAM,2CACpB,OAAOiI,EAAavB,MAAMC,KAAKlG,GAAQ,IAAQiJ,EAAI,EAEvD1C,OAASE,IACL,IAAKR,MAAMS,QAAQD,IAAYA,EAAO3G,QAA+B,iBAAd2G,EAAO,GAC1D,MAAM,IAAIlH,MAAM,iDACpB,OAAOM,WAAWqG,KAAKsB,EAAaf,EAAQwC,EAAK,KAAQ,IAmFzBzC,EAASwC,GAAMhC,EAAK,KA9FhE,IAAeiC,CA8FqD,EACvDC,EAAyBH,EAAU,8DA+BnCI,EAA+BtD,GAAWC,EAvEvD,SAAkBrD,EAAKiG,GAEnB,GAAkB,mBAAPA,EACP,MAAM,IAAInJ,MAAM,kCACpB,MAAO,CACH,MAAA4B,CAAOH,GACH,KAAMA,aAAgBnB,YAClB,MAAM,IAAIN,MAAM,+CACpB,MAAM6J,EAAWV,EAAG1H,GAAMoD,MAAM,EAAG3B,GAC7B0B,EAAM,IAAItE,WAAWmB,EAAKlB,OAAS2C,GAGzC,OAFA0B,EAAIvB,IAAI5B,GACRmD,EAAIvB,IAAIwG,EAAUpI,EAAKlB,QAChBqE,CACV,EACD,MAAAoC,CAAOvF,GACH,KAAMA,aAAgBnB,YAClB,MAAM,IAAIN,MAAM,+CACpB,MAAM8J,EAAUrI,EAAKoD,MAAM,GAAI3B,GACzB6G,EAAcZ,EAAGW,GAASjF,MAAM,EAAG3B,GACnC8G,EAAcvI,EAAKoD,OAAO3B,GAChC,IAAK,IAAIU,EAAI,EAAGA,EAAIV,EAAKU,IACrB,GAAImG,EAAYnG,KAAOoG,EAAYpG,GAC/B,MAAM,IAAI5D,MAAM,oBACxB,OAAO8J,CACV,EAET,CA6C6DD,CAAS,GAAIpI,GAAS6E,EAAOA,EAAO7E,MAASkI,GACpGM,EAAgC1D,EAAMU,EAAS,oCAAqCQ,EAAK,KACzFyC,EAAqB,CAAC,UAAY,UAAY,UAAY,WAAY,WAI5E,SAASC,EAAcC,GACnB,MAAMhK,EAAIgK,GAAO,GACjB,IAAIC,GAAa,SAAND,IAAoB,EAC/B,IAAK,IAAIxG,EAAI,EAAGA,EAAIsG,EAAmB3J,OAAQqD,IACpB,IAAjBxD,GAAKwD,EAAK,KACZyG,GAAOH,EAAmBtG,IAElC,OAAOyG,CACX,CAIA,SAASC,EAAaC,EAAQC,EAAOC,EAAgB,GACjD,MAAMvH,EAAMqH,EAAOhK,OACnB,IAAI8J,EAAM,EACV,IAAK,IAAIzG,EAAI,EAAGA,EAAIV,EAAKU,IAAK,CAC1B,MAAMqB,EAAIsF,EAAOG,WAAW9G,GAC5B,GAAIqB,EAAI,IAAMA,EAAI,IACd,MAAM,IAAIjF,MAAM,mBAAmBuK,MACvCF,EAAMF,EAAcE,GAAQpF,GAAK,CACpC,CACDoF,EAAMF,EAAcE,GACpB,IAAK,IAAIzG,EAAI,EAAGA,EAAIV,EAAKU,IACrByG,EAAMF,EAAcE,GAA+B,GAAvBE,EAAOG,WAAW9G,GAClD,IAAK,IAAI+G,KAAKH,EACVH,EAAMF,EAAcE,GAAOM,EAC/B,IAAK,IAAI/G,EAAI,EAAGA,EAAI,EAAGA,IACnByG,EAAMF,EAAcE,GAExB,OADAA,GAAOI,EACAR,EAAcrI,OAAOiH,EAAc,CAACwB,EAAM,GAAK,IAAK,GAAI,GAAG,GACtE,CAIA,SAASO,EAAUC,GACf,MAAMC,EAA8B,WAAbD,EAAwB,EAAI,UAC7CE,EAAS/B,EAAO,GAChBgC,EAAYD,EAAO/D,OACnBiE,EAAUF,EAAOnJ,OACjBsJ,EAAkBhC,EAAc8B,GAatC,SAAShE,EAAOtF,EAAKyJ,EAAQ,IACzB,GAAmB,iBAARzJ,EACP,MAAM,IAAI1B,MAAM,oDAAoD0B,GACxE,GAAIA,EAAInB,OAAS,IAAgB,IAAV4K,GAAmBzJ,EAAInB,OAAS4K,EACnD,MAAM,IAAIC,UAAU,wBAAwB1J,EAAInB,WAAWmB,oBAAsByJ,MAErF,MAAME,EAAU3J,EAAI4J,cACpB,GAAI5J,IAAQ2J,GAAW3J,IAAQA,EAAI6J,cAC/B,MAAM,IAAIvL,MAAM,yCAEpB,MAAMwL,GADN9J,EAAM2J,GACeI,YAAY,KACjC,GAAiB,IAAbD,IAAgC,IAAdA,EAClB,MAAM,IAAIxL,MAAM,2DACpB,MAAMuK,EAAS7I,EAAImD,MAAM,EAAG2G,GACtBT,EAASrJ,EAAImD,MAAM2G,EAAW,GACpC,GAAIT,EAAOxK,OAAS,EAChB,MAAM,IAAIP,MAAM,2CACpB,MAAMwK,EAAQP,EAAcjD,OAAO+D,GAAQlG,MAAM,GAAI,GAC/C6G,EAAMpB,EAAaC,EAAQC,EAAOM,GACxC,IAAKC,EAAOY,SAASD,GACjB,MAAM,IAAI1L,MAAM,uBAAuB0B,gBAAkBgK,MAC7D,MAAO,CAAEnB,SAAQC,QACpB,CAMD,MAAO,CAAE5I,OAxCT,SAAgB2I,EAAQC,EAAOW,EAAQ,IACnC,GAAsB,iBAAXZ,EACP,MAAM,IAAIvK,MAAM,qDAAqDuK,GACzE,IAAK7D,MAAMS,QAAQqD,IAAWA,EAAMjK,QAA8B,iBAAbiK,EAAM,GACvD,MAAM,IAAIxK,MAAM,8DAA8DwK,GAClF,MAAMoB,EAAerB,EAAOhK,OAAS,EAAIiK,EAAMjK,OAC/C,IAAc,IAAV4K,GAAmBS,EAAeT,EAClC,MAAM,IAAIC,UAAU,UAAUQ,mBAA8BT,KAChE,MAAME,EAAUd,EAAOe,cACjBI,EAAMpB,EAAae,EAASb,EAAOM,GACzC,MAAO,GAAGO,KAAWpB,EAAcrI,OAAO4I,KAASkB,GACtD,EA6BgB1E,SAAQ6E,cAJzB,SAAuBnK,GACnB,MAAM6I,OAAEA,EAAMC,MAAEA,GAAUxD,EAAOtF,GAAK,GACtC,MAAO,CAAE6I,SAAQC,QAAO/J,MAAOuK,EAAUR,GAC5C,EACuCsB,aALnB5C,EAAclC,GAKmBgE,YAAWE,kBAAiBD,UACtF,CACO,MAAMc,EAAyBnB,EAAU,UACnCoB,EAA0BpB,EAAU,aCna5B,8KAgBNqB,SAAAF,EAAAd,mBAOKD,wSCnCpB,SAAAkB,EAA2BzK,EAAA0K,GAS3B,GAAA1K,EAAAlB,OAAA4L,EASA,MAAA,IAAAf,6CAMC3J,EAAAlB,YAAA4L,IAQD,4iBChCA,MAAAC,EAAcpI,OAAA,GAyBdqI,EAAArI,OAAA,KAuBAsI,EAAAtI,OAAA,imBClCA,SAAA0F,GAsBA,GAAAA,GAAA,yTC9BA,MAAA6C,EAAA,IAAA5K,YAIA6K,EAAA,IAAAC,YAiBA,SAAAC,KAsBA,OAAAH,EAAA3K,OAAAF,EAQA,2DAKA,WAAgByK,mCAGfpF,IAAAoF,0cC5DDQ,gBAAAA,YAAiCC,mBAKhCC,OAAAC,OAED,SAAAC,GAAAZ,EAAsB,IAQN,GAAe,mBAAfQ,GAsBhB,OAAAG,OAAAH,gBACE,IAAerM,WAAe6L,IA0BhB,MAAA,IAAAnM,MAAA,yCAMhB,CAMA,SAAAgN,GAAA3F,8/CCtEQ,iBAAqB/G,uBACAoJ,IAAAuD,EAAA,aACAC,IAAAC,EAAA,aACAC,IAAAC,EAAA,aACAC,IAAAC,EAAA,aACH7L,IAAA8L,EAAA,aACIC,IAAAC,EAAA,aACEjN,MAAAQ,EAAA,aACA0M,KAAAC,EAAA,aACAtE,OAAAuE,EAAA,aAChBC,OAAiBC,EAAA,aACDhC,OAAAiC,EAAA,aACEhC,QAAAiC,EAAA,aACAC,OAAAC,EAAA,aACJvM,OAAAwM,CAAA,oBACGC,CAAA,aACFC,MAAAC,EAAA,QAEhBvM,KAAEwM,SAAIC,EAAa,QAKtBzM,KAAMgL,OAAA0B,EAAY,CAM1B,aAAA3B,CAAAZ,EAAe,IAeb,MAAGwC,EAAYC,GAElBzC,GAEG,OAAS,IAAA0C,GAEZF,EAAAxC,EAEG,CAIJ,UAAO2C,CAAM3C,EAEZ,GAEG,MAAS4C,EAEZ3L,KAAAsF,MAAAsG,KAAAF,MAAA,KAEG,OAAS,IAAAD,GAAAE,EAEZ5C,EAEG,CAIJ,WAAAvJ,CAAgBnB,EAAA0K,EAEf8C,GAEG,GAAAxN,aAEHoN,SAEe9H,IAAZoF,EAIA,OAAY1K,EAYhBsB,MAJgBmM,GAEfzN,EAAA0K,EAAA8C,GASD,CAOA,OAAAlO,GAIA,MAAa,IAAIiB,KAKV,CAQP,OAAS0H,GASC,OACR1H,KAAMmN,QAQR,CACA,OAAAjC,GACA,YAAyBkC,QACzB,CACA,OAAA1N,GACA,YAAqB2N,QAErB,CAIA,OAAA5B,GAIA,OAAazL,KAAIsN,QAKZ,CAKL,OAAKhC,GAIG,OAAQ,IAAChN,WAAU0B,KAKtB,CAKL,OAAAoL,GAKM,OAAKpL,KAAMuN,QAIX,CAIN,UAAMrB,GAMN,OAAWlM,gBAMX,CAaD,UAAAsH,GAED,OAAAtH,gBAQA,CAQA,UAAA8L,GAQA,OAAA9L,gBAQA,CAQA,UAAAO,GAQA,OAAAP,KAAmBwN,SAWnB,CAMA,MAAAC,GAMA,OAAAzN,eACEyL,GAaF,CAcA,UAAAiC,GAMA,OAAA,YAUA,CACS,MAAAP,GAAa,MAehB,oFAASQ,CAdW,OAAAV,EAEXjN,KAAA4E,UAKA5E,KAcb,CAeD,MAAAoN,CAAAH,EAAA,4GAGM,OADSA,0uDCtYhB,YAoBA,MAAAW,EAAAC,EAAgBlI,UAAUP,IAAEjD", "x_google_ignoreList": [0, 1, 2, 3, 4]}