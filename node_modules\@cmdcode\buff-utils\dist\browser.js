var buff_utils=function(t){"use strict";function r(t,r=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(r&&t.finished)throw new Error("Hash#digest() has already been called")}function e(t,r){!function(t,...r){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");if(r.length>0&&!r.includes(t.length))throw new Error(`Expected Uint8Array of length ${r}, not of length=${t.length}`)}(t);const e=r.outputLen;if(t.length<e)throw new Error(`digestInto() expects output buffer of length at least ${e}`)}
/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */const n=t=>t instanceof Uint8Array,o=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),i=(t,r)=>t<<32-r|t>>>r;if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function s(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}(t)),!n(t))throw new Error("expected Uint8Array, got "+typeof t);return t}class f{clone(){return this._cloneInto()}}function c(t){const r=r=>t().update(s(r)).digest(),e=t();return r.outputLen=e.outputLen,r.blockLen=e.blockLen,r.create=()=>t(),r}class a extends f{constructor(t,r,e,n){super(),this.blockLen=t,this.outputLen=r,this.padOffset=e,this.isLE=n,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=o(this.buffer)}update(t){r(this);const{view:e,buffer:n,blockLen:i}=this,f=(t=s(t)).length;for(let r=0;r<f;){const s=Math.min(i-this.pos,f-r);if(s!==i)n.set(t.subarray(r,r+s),this.pos),this.pos+=s,r+=s,this.pos===i&&(this.process(e,0),this.pos=0);else{const e=o(t);for(;i<=f-r;r+=i)this.process(e,r)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){r(this),e(t,this),this.finished=!0;const{buffer:n,view:i,blockLen:s,isLE:f}=this;let{pos:c}=this;n[c++]=128,this.buffer.subarray(c).fill(0),this.padOffset>s-c&&(this.process(i,0),c=0);for(let t=c;t<s;t++)n[t]=0;!function(t,r,e,n){if("function"==typeof t.setBigUint64)return t.setBigUint64(r,e,n);const o=BigInt(32),i=BigInt(4294967295),s=Number(e>>o&i),f=Number(e&i),c=n?4:0,a=n?0:4;t.setUint32(r+c,s,n),t.setUint32(r+a,f,n)}(i,s-8,BigInt(8*this.length),f),this.process(i,0);const a=o(t),h=this.outputLen;if(h%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=h/4,d=this.get();if(u>d.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<u;t++)a.setUint32(4*t,d[t],f)}digest(){const{buffer:t,outputLen:r}=this;this.digestInto(t);const e=t.slice(0,r);return this.destroy(),e}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:r,buffer:e,length:n,finished:o,destroyed:i,pos:s}=this;return t.length=n,t.pos=s,t.finished=o,t.destroyed=i,n%r&&t.buffer.set(e),t}}const h=(t,r,e)=>t&r^t&e^r&e,u=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),d=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),w=new Uint32Array(64);class l extends a{constructor(){super(64,32,8,!1),this.A=0|d[0],this.B=0|d[1],this.C=0|d[2],this.D=0|d[3],this.E=0|d[4],this.F=0|d[5],this.G=0|d[6],this.H=0|d[7]}get(){const{A:t,B:r,C:e,D:n,E:o,F:i,G:s,H:f}=this;return[t,r,e,n,o,i,s,f]}set(t,r,e,n,o,i,s,f){this.A=0|t,this.B=0|r,this.C=0|e,this.D=0|n,this.E=0|o,this.F=0|i,this.G=0|s,this.H=0|f}process(t,r){for(let e=0;e<16;e++,r+=4)w[e]=t.getUint32(r,!1);for(let t=16;t<64;t++){const r=w[t-15],e=w[t-2],n=i(r,7)^i(r,18)^r>>>3,o=i(e,17)^i(e,19)^e>>>10;w[t]=o+w[t-7]+n+w[t-16]|0}let{A:e,B:n,C:o,D:s,E:f,F:c,G:a,H:d}=this;for(let t=0;t<64;t++){const r=d+(i(f,6)^i(f,11)^i(f,25))+((l=f)&c^~l&a)+u[t]+w[t]|0,g=(i(e,2)^i(e,13)^i(e,22))+h(e,n,o)|0;d=a,a=c,c=f,f=s+r|0,s=o,o=n,n=e,e=r+g|0}var l;e=e+this.A|0,n=n+this.B|0,o=o+this.C|0,s=s+this.D|0,f=f+this.E|0,c=c+this.F|0,a=a+this.G|0,d=d+this.H|0,this.set(e,n,o,s,f,c,a,d)}roundClean(){w.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const g=c((()=>new l));
/*! scure-base - MIT License (c) 2022 Paul Miller (paulmillr.com) */function y(...t){const r=(t,r)=>e=>t(r(e));return{encode:Array.from(t).reverse().reduce(((t,e)=>t?r(t,e.encode):e.encode),void 0),decode:t.reduce(((t,e)=>t?r(t,e.decode):e.decode),void 0)}}function p(t){return{encode:r=>{if(!Array.isArray(r)||r.length&&"number"!=typeof r[0])throw new Error("alphabet.encode input should be an array of numbers");return r.map((r=>{if(r<0||r>=t.length)throw new Error(`Digit index outside alphabet: ${r} (alphabet: ${t.length})`);return t[r]}))},decode:r=>{if(!Array.isArray(r)||r.length&&"string"!=typeof r[0])throw new Error("alphabet.decode input should be array of strings");return r.map((r=>{if("string"!=typeof r)throw new Error(`alphabet.decode: not string element=${r}`);const e=t.indexOf(r);if(-1===e)throw new Error(`Unknown letter: "${r}". Allowed: ${t}`);return e}))}}}function b(t=""){if("string"!=typeof t)throw new Error("join separator should be string");return{encode:r=>{if(!Array.isArray(r)||r.length&&"string"!=typeof r[0])throw new Error("join.encode input should be array of strings");for(let t of r)if("string"!=typeof t)throw new Error(`join.encode: non-string input=${t}`);return r.join(t)},decode:r=>{if("string"!=typeof r)throw new Error("join.decode input should be string");return r.split(t)}}}function E(t,r="="){if("string"!=typeof r)throw new Error("padding chr should be string");return{encode(e){if(!Array.isArray(e)||e.length&&"string"!=typeof e[0])throw new Error("padding.encode input should be array of strings");for(let t of e)if("string"!=typeof t)throw new Error(`padding.encode: non-string input=${t}`);for(;e.length*t%8;)e.push(r);return e},decode(e){if(!Array.isArray(e)||e.length&&"string"!=typeof e[0])throw new Error("padding.encode input should be array of strings");for(let t of e)if("string"!=typeof t)throw new Error(`padding.decode: non-string input=${t}`);let n=e.length;if(n*t%8)throw new Error("Invalid padding: string should have whole number of bytes");for(;n>0&&e[n-1]===r;n--)if(!((n-1)*t%8))throw new Error("Invalid padding: string has too much padding");return e.slice(0,n)}}}function m(t,r,e){if(r<2)throw new Error(`convertRadix: wrong from=${r}, base cannot be less than 2`);if(e<2)throw new Error(`convertRadix: wrong to=${e}, base cannot be less than 2`);if(!Array.isArray(t))throw new Error("convertRadix: data should be array");if(!t.length)return[];let n=0;const o=[],i=Array.from(t);for(i.forEach((t=>{if(t<0||t>=r)throw new Error(`Wrong integer: ${t}`)}));;){let t=0,s=!0;for(let o=n;o<i.length;o++){const f=i[o],c=r*t+f;if(!Number.isSafeInteger(c)||r*t/r!==t||c-f!=r*t)throw new Error("convertRadix: carry overflow");t=c%e;const a=Math.floor(c/e);if(i[o]=a,!Number.isSafeInteger(a)||a*e+t!==c)throw new Error("convertRadix: carry overflow");s&&(a?s=!1:n=o)}if(o.push(t),s)break}for(let r=0;r<t.length-1&&0===t[r];r++)o.push(0);return o.reverse()}const A=(t,r)=>r?A(r,t%r):t,x=(t,r)=>t+(r-A(t,r));function _(t,r,e,n){if(!Array.isArray(t))throw new Error("convertRadix2: data should be array");if(r<=0||r>32)throw new Error(`convertRadix2: wrong from=${r}`);if(e<=0||e>32)throw new Error(`convertRadix2: wrong to=${e}`);if(x(r,e)>32)throw new Error(`convertRadix2: carry overflow from=${r} to=${e} carryBits=${x(r,e)}`);let o=0,i=0;const s=2**e-1,f=[];for(const n of t){if(n>=2**r)throw new Error(`convertRadix2: invalid data word=${n} from=${r}`);if(o=o<<r|n,i+r>32)throw new Error(`convertRadix2: carry overflow pos=${i} from=${r}`);for(i+=r;i>=e;i-=e)f.push((o>>i-e&s)>>>0);o&=2**i-1}if(o=o<<e-i&s,!n&&i>=r)throw new Error("Excess padding");if(!n&&o)throw new Error(`Non-zero padding: ${o}`);return n&&i>0&&f.push(o>>>0),f}function v(t,r=!1){if(t<=0||t>32)throw new Error("radix2: bits should be in (0..32]");if(x(8,t)>32||x(t,8)>32)throw new Error("radix2: carry overflow");return{encode:e=>{if(!(e instanceof Uint8Array))throw new Error("radix2.encode input should be Uint8Array");return _(Array.from(e),8,t,!r)},decode:e=>{if(!Array.isArray(e)||e.length&&"number"!=typeof e[0])throw new Error("radix2.decode input should be array of strings");return Uint8Array.from(_(e,t,8,r))}}}function U(t){if("function"!=typeof t)throw new Error("unsafeWrapper fn should be function");return function(...r){try{return t.apply(null,r)}catch(t){}}}const $=y(v(6),p("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"),E(6),b("")),I=y(v(6),p("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"),E(6),b("")),B=t=>{return y((r=58,{encode:t=>{if(!(t instanceof Uint8Array))throw new Error("radix.encode input should be Uint8Array");return m(Array.from(t),256,r)},decode:t=>{if(!Array.isArray(t)||t.length&&"number"!=typeof t[0])throw new Error("radix.decode input should be array of strings");return Uint8Array.from(m(t,r,256))}}),p(t),b(""));var r},k=B("123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"),L=t=>y(function(t,r){if("function"!=typeof r)throw new Error("checksum fn should be function");return{encode(e){if(!(e instanceof Uint8Array))throw new Error("checksum.encode: input should be Uint8Array");const n=r(e).slice(0,t),o=new Uint8Array(e.length+t);return o.set(e),o.set(n,e.length),o},decode(e){if(!(e instanceof Uint8Array))throw new Error("checksum.decode: input should be Uint8Array");const n=e.slice(0,-t),o=r(n).slice(0,t),i=e.slice(-t);for(let r=0;r<t;r++)if(o[r]!==i[r])throw new Error("Invalid checksum");return n}}}(4,(r=>t(t(r)))),k),T=y(p("qpzry9x8gf2tvdw0s3jn54khce6mua7l"),b("")),j=[996825010,642813549,513874426,1027748829,705979059];function N(t){const r=t>>25;let e=(33554431&t)<<5;for(let t=0;t<j.length;t++)1==(r>>t&1)&&(e^=j[t]);return e}function R(t,r,e=1){const n=t.length;let o=1;for(let r=0;r<n;r++){const e=t.charCodeAt(r);if(e<33||e>126)throw new Error(`Invalid prefix (${t})`);o=N(o)^e>>5}o=N(o);for(let r=0;r<n;r++)o=N(o)^31&t.charCodeAt(r);for(let t of r)o=N(o)^t;for(let t=0;t<6;t++)o=N(o);return o^=e,T.encode(_([o%2**30],30,5,!1))}function z(t){const r="bech32"===t?1:734539939,e=v(5),n=e.decode,o=e.encode,i=U(n);function s(t,e=90){if("string"!=typeof t)throw new Error("bech32.decode input should be string, not "+typeof t);if(t.length<8||!1!==e&&t.length>e)throw new TypeError(`Wrong string length: ${t.length} (${t}). Expected (8..${e})`);const n=t.toLowerCase();if(t!==n&&t!==t.toUpperCase())throw new Error("String must be lowercase or uppercase");const o=(t=n).lastIndexOf("1");if(0===o||-1===o)throw new Error('Letter "1" must be present between prefix and data only');const i=t.slice(0,o),s=t.slice(o+1);if(s.length<6)throw new Error("Data must be at least 6 characters long");const f=T.decode(s).slice(0,-6),c=R(i,f,r);if(!s.endsWith(c))throw new Error(`Invalid checksum in ${t}: expected "${c}"`);return{prefix:i,words:f}}return{encode:function(t,e,n=90){if("string"!=typeof t)throw new Error("bech32.encode prefix should be string, not "+typeof t);if(!Array.isArray(e)||e.length&&"number"!=typeof e[0])throw new Error("bech32.encode words should be array of numbers, not "+typeof e);const o=t.length+7+e.length;if(!1!==n&&o>n)throw new TypeError(`Length ${o} exceeds limit ${n}`);const i=t.toLowerCase(),s=R(i,e,r);return`${i}1${T.encode(e)}${s}`},decode:s,decodeToBytes:function(t){const{prefix:r,words:e}=s(t,!1);return{prefix:r,words:e,bytes:n(e)}},decodeUnsafe:U(s),fromWords:n,fromWordsUnsafe:i,toWords:o}}const D=z("bech32"),C=z("bech32m"),S={b58chk:{encode:t=>L(g).encode(t),decode:t=>L(g).decode(t)},base64:{encode:t=>$.encode(t),decode:t=>$.decode(t)},b64url:{encode:t=>I.encode(t),decode:t=>I.decode(t)},bech32:{to_words:D.toWords,to_bytes:D.fromWords,encode:(t,r,e=!1)=>D.encode(t,r,e),decode:(t,r=!1)=>{const{prefix:e,words:n}=D.decode(t,r);return{prefix:e,words:n}}},bech32m:{to_words:C.toWords,to_bytes:C.fromWords,encode:(t,r,e=!1)=>C.encode(t,r,e),decode:(t,r=!1)=>{const{prefix:e,words:n}=C.decode(t,r);return{prefix:e,words:n}}}};function W(t,r){if(t.length>r)throw new TypeError(`Data is larger than array size: ${t.length} > ${r}`)}function O(t){if(null!==t.match(/[^a-fA-f0-9]/))throw new TypeError("Invalid characters in hex string: "+t);if(t.length%2!=0)throw new Error(`Length of hex string is invalid: ${t.length}`)}function H(t){if(t>Number.MAX_SAFE_INTEGER)throw new TypeError("Number exceeds safe bounds!")}function V(t,r){if(t!==r)throw new TypeError(`Bech32 prefix does not match: ${t} !== ${r}`)}var F=Object.freeze({__proto__:null,is_hex:O,is_json:function(t){try{JSON.parse(t)}catch{throw new TypeError("JSON string is invalid!")}},is_prefix:V,is_safe_num:H,within_size:W});const G=BigInt(0),M=BigInt(255),J=BigInt(256);function q(t,r,e="be"){void 0===r&&(r=function(t){if(t<=0xffn)return 1;if(t<=0xffffn)return 2;if(t<=0xffffffffn)return 4;if(t<=0xffffffffffffffffn)return 8;if(t<=0xffffffffffffffffffffffffffffffffn)return 16;if(t<=0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn)return 32;throw new TypeError("Must specify a fixed buffer size for bigints greater than 32 bytes.")}(t));const n="le"===e,o=new ArrayBuffer(r),i=new DataView(o);let s=n?0:r-1;for(;t>G;){const r=t&M,e=Number(r);n?i.setUint8(s++,e):i.setUint8(s--,e),t=(t-r)/J}return new Uint8Array(o)}function X(t,r,e="be"){void 0===r&&(r=function(t){if(t<=255)return 1;if(t<=65535)return 2;if(t<=4294967295)return 4;throw new TypeError("Numbers larger than 4 bytes must specify a fixed size!")}(t));const n="le"===e,o=new ArrayBuffer(r),i=new DataView(o);let s=n?0:r-1;for(;t>0;){const r=255&t;n?i.setUint8(s++,t):i.setUint8(s--,t),t=(t-r)/256}return new Uint8Array(o)}const K=new TextEncoder,P=new TextDecoder;function Q(t){return K.encode(t)}function Y(t){return P.decode(t)}function Z(t,r,e="le"){r=function(t,r){O(t);const e=t.length/2;if(void 0===r&&(r=e),e>r)throw new TypeError(`Hex string is larger than array size: ${e} > ${r}`);return r}(t,r);const n="le"===e,o=new ArrayBuffer(r),i=new DataView(o);let s=n?0:r-1;for(let r=0;r<t.length;r+=2){const e=t.substring(r,r+2),o=parseInt(e,16);n?i.setUint8(s++,o):i.setUint8(s--,o)}return new Uint8Array(o)}function tt(t){let r="";for(let e=0;e<t.length;e++)r+=t[e].toString(16).padStart(2,"0");return r}const rt={encode:tt,decode:Z},et={encode:Q,decode:Y},{getRandomValues:nt}=crypto??globalThis.crypto??window.crypto;function ot(t=32){if("function"==typeof nt)return crypto.getRandomValues(new Uint8Array(t));throw new Error("Crypto module missing getRandomValues!")}function it(t){return null===t.match(/[^a-fA-f0-9]/)&&t.length%2==0}function st(t){return!("string"!=typeof t||!it(t))||("number"==typeof t||"bigint"==typeof t||t instanceof Uint8Array||!(!Array.isArray(t)||!t.every((t=>"number"==typeof t))))}function ft(t,r,e="be"){void 0===r&&(r=t.length),W(t,r);const n=new Uint8Array(r).fill(0),o="be"===e?0:r-t.length;return n.set(t,o),n}function ct(t){let r,e=0;const n=t.reduce(((t,r)=>t+r.length),0),o=new Uint8Array(n);for(r=0;r<t.length;r++){const n=t[r];o.set(n,e),e+=n.length}return o}function at(t,r){return"bigint"==typeof r?`${r}n`:r}function ht(t,r){return"string"==typeof r&&/n$/.test(r)?BigInt(r.slice(0,-1)):r}function ut(t,r,e){const n=t.length,o=e/r;if(e%r!=0)throw new TypeError(`Invalid parameters: ${e} % ${r} !== 0`);if(n!==e)throw new TypeError(`Invalid data stream: ${n} !== ${e}`);if(n%r!=0)throw new TypeError(`Invalid data stream: ${n} % ${r} !== 0`);const i=new Array(o);for(let e=0;e<o;e++){const n=e*r;i[e]=t.subarray(n,n+r)}return i}var dt=Object.freeze({__proto__:null,bigint_replacer:at,bigint_reviver:ht,is_bytes:st,is_hex:it,join_array:ct,parse_data:ut,random:ot,set_buffer:ft});function wt(t,r,e){if(t instanceof ArrayBuffer)return new Uint8Array(t);if(t instanceof Uint8Array)return ft(t,r,e);if(Array.isArray(t)){return ct(t.map((t=>wt(t,r,e))))}if("string"==typeof t)return Z(t,r,e);if("bigint"==typeof t)return q(t,r,e);if("number"==typeof t)return X(t,r,e);if("boolean"==typeof t)return Uint8Array.of(t?1:0);throw new TypeError("Unsupported format:"+String(typeof t))}class lt extends Uint8Array{static{this.num=gt}static{this.big=pt}static{this.bin=yt}static{this.raw=bt}static{this.str=Et}static{this.hex=mt}static{this.bytes=kt}static{this.json=At}static{this.base64=xt}static{this.b64url=_t}static{this.bech32=vt}static{this.bech32m=Ut}static{this.b58chk=$t}static{this.encode=Q}static{this.decode=Y}static{this.parse=It}static{this.is_bytes=st}static{this.is_hex=it}static random(t=32){const r=ot(t);return new lt(r,t)}static now(t=4){const r=Math.floor(Date.now()/1e3);return new lt(r,t)}constructor(t,r,e){if(t instanceof lt&&void 0===r)return t;super(wt(t,r,e))}get arr(){return[...this]}get num(){return this.to_num()}get big(){return this.to_big()}get str(){return this.to_str()}get hex(){return this.to_hex()}get raw(){return new Uint8Array(this)}get bin(){return this.to_bin()}get b58chk(){return this.to_b58chk()}get base64(){return this.to_base64()}get b64url(){return this.to_b64url()}get digest(){return this.to_hash()}get id(){return this.to_hash().hex}get stream(){return new Bt(this)}to_num(t="be"){return function(t){let r=0;for(let e=t.length-1;e>=0;e--)r=256*r+t[e],H(r);return r}("be"===t?this.reverse():this)}to_big(t="be"){return function(t){let r=BigInt(0);for(let e=t.length-1;e>=0;e--)r=r*J+BigInt(t[e]);return BigInt(r)}("be"===t?this.reverse():this)}to_bin(){return function(t){const r=new Array(8*t.length);let e=0;for(const n of t){if(n>255)throw new Error(`Invalid byte value: ${n}. Byte values must be between 0 and 255.`);for(let t=7;t>=0;t--,e++)r[e]=n>>t&1}return r.join("")}(this)}to_hash(){const t=g(this);return new lt(t)}to_json(t){void 0===t&&(t=ht);const r=Y(this);return JSON.parse(r,t)}to_bech32(t,r){const{encode:e,to_words:n}=S.bech32;return e(t,n(this),r)}to_bech32m(t,r){const{encode:e,to_words:n}=S.bech32m;return e(t,n(this),r)}to_str(){return Y(this)}to_hex(){return tt(this)}to_bytes(){return new Uint8Array(this)}to_b58chk(){return S.b58chk.encode(this)}to_base64(){return S.base64.encode(this)}to_b64url(){return S.b64url.encode(this)}append(t){return lt.join([this,lt.bytes(t)])}prepend(t){return lt.join([lt.bytes(t),this])}reverse(){const t=new Uint8Array(this).reverse();return new lt(t)}slice(t,r){const e=new Uint8Array(this).slice(t,r);return new lt(e)}set(t,r){this.set(t,r)}subarray(t,r){const e=new Uint8Array(this).subarray(t,r);return new lt(e)}write(t,r){const e=lt.bytes(t);this.set(e,r)}add_varint(t){const r=lt.calc_varint(this.length,t);return lt.join([r,this])}static from(t){return new lt(Uint8Array.from(t))}static of(...t){return new lt(Uint8Array.of(...t))}static join(t){const r=ct(t.map((t=>lt.bytes(t))));return new lt(r)}static sort(t,r){const e=t.map((t=>kt(t,r).hex));return e.sort(),e.map((t=>lt.hex(t,r)))}static calc_varint(t,r){if(t<253)return lt.num(t,1);if(t<65536)return lt.of(253,...lt.num(t,2,r));if(t<4294967296)return lt.of(254,...lt.num(t,4,r));if(BigInt(t)<0x10000000000000000n)return lt.of(255,...lt.num(t,8,r));throw new Error(`Value is too large: ${t}`)}}function gt(t,r,e){return new lt(t,r,e)}function yt(t,r,e){return new lt(function(t){const r=t.split("").map(Number);if(r.length%8!=0)throw new Error(`Binary array is invalid length: ${t.length}`);const e=new Uint8Array(r.length/8);for(let t=0,n=0;t<r.length;t+=8,n++){let o=0;for(let e=0;e<8;e++)o|=r[t+e]<<7-e;e[n]=o}return e}(t),r,e)}function pt(t,r,e){return new lt(t,r,e)}function bt(t,r,e){return new lt(t,r,e)}function Et(t,r,e){return new lt(Q(t),r,e)}function mt(t,r,e){return new lt(t,r,e)}function At(t,r){void 0===r&&(r=at);const e=JSON.stringify(t,r);return new lt(Q(e))}function xt(t){return new lt(S.base64.decode(t))}function _t(t){return new lt(S.b64url.decode(t))}function vt(t,r,e){const{decode:n,to_bytes:o}=S.bech32,{prefix:i,words:s}=n(t,r),f=o(s);return"string"==typeof e&&V(i,e),new lt(f)}function Ut(t,r,e){const{decode:n,to_bytes:o}=S.bech32m,{prefix:i,words:s}=n(t,r),f=o(s);return"string"==typeof e&&V(i,e),new lt(f)}function $t(t){return new lt(S.b58chk.decode(t))}function It(t,r,e){return ut(wt(t),r,e).map((t=>lt.bytes(t)))}class Bt{constructor(t){this.data=lt.bytes(t),this.size=this.data.length}peek(t){if(t>this.size)throw new Error(`Size greater than stream: ${t} > ${this.size}`);return new lt(this.data.slice(0,t))}read(t){const r=this.peek(t);return this.data=this.data.slice(t),this.size=this.data.length,r}read_varint(t){const r=this.read(1).num;switch(!0){case r>=0&&r<253:return r;case 253===r:return this.read(2).to_num(t);case 254===r:return this.read(4).to_num(t);case 255===r:return this.read(8).to_num(t);default:throw new Error(`Varint is out of range: ${r}`)}}}function kt(t,r,e){return new lt(t,r,e)}return t.Buff=lt,t.Encoder=S,t.Hex=rt,t.Stream=Bt,t.Txt=et,t.assert=F,t.buffer=kt,t.util=dt,t}({});
//# sourceMappingURL=browser.js.map
