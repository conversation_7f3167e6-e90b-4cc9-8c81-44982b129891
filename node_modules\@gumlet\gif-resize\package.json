{"name": "@gumlet/gif-resize", "version": "1.3.1", "description": "Nodejs plugin to resize GIFs", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/gumlet/gif-resize.git"}, "engines": {"node": ">=10"}, "scripts": {"test": "jest --coverage"}, "files": ["index.js"], "main": "src/index.js", "keywords": ["compress", "gif", "resize", "gifsicle", "gulpplugin", "image", "img", "minify", "optimize"], "dependencies": {"execa": "^5.0.0", "gifsicle": "^5.1.0", "is-gif": "^3.0.0"}, "devDependencies": {"get-pixels": "^3.3.2", "jest": "^25.0.0"}, "bugs": {"url": "https://github.com/gumlet/gif-resize/issues"}, "homepage": "https://github.com/gumlet/gif-resize#readme", "directories": {"test": "tests"}, "author": "<PERSON><PERSON><PERSON>"}