import { Buff, Bytes } from '@cmdcode/buff-utils';
import { ProjPointType } from '@noble/curves/abstract/weierstrass';
type ECPoint = ProjPointType<bigint>;
type FieldValue = string | number | bigint | Uint8Array | Field;
type PointValue = string | number | bigint | Uint8Array | Point;
export declare const fd: Readonly<import("@noble/curves/abstract/modular").IField<bigint> & Required<Pick<import("@noble/curves/abstract/modular").IField<bigint>, "isOdd">>>;
export declare class Field extends Uint8Array {
    static N: bigint;
    static add(x: FieldValue[]): Field;
    static mod(x: FieldValue): Field;
    static mul(x: FieldValue[]): Field;
    static is_valid(value: Bytes, throws?: boolean): boolean;
    constructor(x: FieldValue);
    get buff(): Buff;
    get raw(): Uint8Array;
    get big(): bigint;
    get hex(): string;
    get point(): Point;
    get hasOddY(): boolean;
    get negated(): Field;
    gt(value: FieldValue): boolean;
    lt(value: FieldValue): boolean;
    eq(value: FieldValue): boolean;
    ne(value: FieldValue): boolean;
    add(value: FieldValue): Field;
    sub(value: FieldValue): Field;
    mul(value: FieldValue): Field;
    pow(value: FieldValue): Field;
    div(value: FieldValue): Field;
    negate(): Field;
    generate(): Point;
}
export declare class Point {
    static P: bigint;
    static G: Point;
    static curve: Readonly<{
        readonly nBitLength: number;
        readonly nByteLength: number;
        readonly Fp: import("@noble/curves/abstract/modular").IField<bigint>;
        readonly n: bigint;
        readonly h: bigint;
        readonly hEff?: bigint | undefined;
        readonly Gx: bigint;
        readonly Gy: bigint;
        readonly allowInfinityPoint?: boolean | undefined;
        readonly a: bigint;
        readonly b: bigint;
        readonly allowedPrivateKeyLengths?: readonly number[] | undefined;
        readonly wrapPrivateKey?: boolean | undefined;
        readonly endo?: {
            beta: bigint;
            splitScalar: (k: bigint) => {
                k1neg: boolean;
                k1: bigint;
                k2neg: boolean;
                k2: bigint;
            };
        } | undefined;
        readonly isTorsionFree?: ((c: import("@noble/curves/abstract/weierstrass").ProjConstructor<bigint>, point: ProjPointType<bigint>) => boolean) | undefined;
        readonly clearCofactor?: ((c: import("@noble/curves/abstract/weierstrass").ProjConstructor<bigint>, point: ProjPointType<bigint>) => ProjPointType<bigint>) | undefined;
        readonly hash: import("@noble/curves/abstract/utils").CHash;
        readonly hmac: (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array;
        readonly randomBytes: (bytesLength?: number | undefined) => Uint8Array;
        lowS: boolean;
        readonly bits2int?: ((bytes: Uint8Array) => bigint) | undefined;
        readonly bits2int_modN?: ((bytes: Uint8Array) => bigint) | undefined;
        readonly p: bigint;
    }>;
    static base: ProjPointType<bigint>;
    static from_x(bytes: Bytes, even_y?: boolean): Point;
    static generate(value: FieldValue): Point;
    static mul: typeof Point.generate;
    static import(point: Point | ECPoint): Point;
    readonly _p: ECPoint;
    constructor(x: bigint, y: bigint);
    get p(): ECPoint;
    get x(): Buff;
    get y(): Buff;
    get buff(): Buff;
    get raw(): Uint8Array;
    get hex(): string;
    get hasEvenY(): boolean;
    get hasOddY(): boolean;
    get negated(): Point;
    eq(value: PointValue): boolean;
    add(x: PointValue): Point;
    sub(x: PointValue): Point;
    mul(value: PointValue): Point;
    negate(): Point;
}
export {};
//# sourceMappingURL=ecc.d.ts.map