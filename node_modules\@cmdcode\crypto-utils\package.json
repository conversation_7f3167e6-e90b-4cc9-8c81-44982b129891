{"name": "@cmdcode/crypto-utils", "description": "Simple library for using secp256k1 cryptography.", "author": "<PERSON>", "license": "CC-BY-1.0", "keywords": ["crypto", "utils", "library"], "version": "2.4.6", "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/main.cjs"}}, "./ecc": {"import": "./dist/ecc.js", "types": "./dist/ecc.d.ts", "default": "./dist/ecc.js"}, "./ecdh": {"import": "./dist/ecdh.js", "types": "./dist/ecdh.d.ts", "default": "./dist/ecdh.js"}, "./hash": {"import": "./dist/hash.js", "types": "./dist/hash.d.ts", "default": "./dist/hash.js"}, "./hd": {"import": "./dist/hd.js", "types": "./dist/hd.d.ts", "default": "./dist/hd.js"}, "./keys": {"import": "./dist/keys.js", "types": "./dist/keys.d.ts", "default": "./dist/keys.js"}, "./math": {"import": "./dist/math.js", "types": "./dist/math.d.ts", "default": "./dist/math.js"}, "./pt": {"import": "./dist/point.js", "types": "./dist/point.d.ts", "default": "./dist/point.js"}, "./proof": {"import": "./dist/proof.js", "types": "./dist/proof.d.ts", "default": "./dist/proof.js"}, "./signer": {"import": "./dist/sig.js", "types": "./dist/sig.d.ts", "default": "./dist/sig.js"}}, "types": "./dist/index.d.ts", "main": "./dist/main.cjs", "unpkg": "./dist/browser.js", "files": ["README.md", "LICENSE", "dist", "src"], "repository": {"type": "git", "url": "https://github.com/cmdruid/crypto-utils.git"}, "publishConfig": {"registry": "https://registry.npmjs.org", "access": "public"}, "scripts": {"bench": "tsx test/bench.ts", "build": "rollup -c rollup.config.ts --configPlugin typescript", "clean": "rm -rf dist/* coverage .nyc_output", "scratch": "tsx test/scratch.ts", "lint": "eslint . --ext .ts", "test": "tsx test/tap.ts | tap-spec", "types": "tsc", "release": "yarn clean && yarn test && yarn types && yarn lint && yarn build"}, "dependencies": {"@cmdcode/buff-utils": "^2.0.0", "@noble/curves": "^1.1.0", "@noble/hashes": "^1.3.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.3", "@types/node": "^20.5.9", "@types/tape": "^5.6.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "eslint": "^8.46.0", "rollup": "^3.27.0", "tap-spec": "^5.0.0", "tape": "^5.6.6", "tiny-secp256k1": "^2.2.3", "tslib": "^2.6.2", "tsx": "^3.12.8", "typescript": "^5.1.6"}}