import { Buff, Bytes } from '@cmdcode/buff-utils';
export declare function sha256(msg: Bytes): Buff;
export declare function sha512(msg: Bytes): Buff;
export declare function ripe160(msg: Bytes): Buff;
export declare function hash256(msg: Bytes): Buff;
export declare function hash160(msg: Bytes): Buff;
export declare function hmac256(key: Bytes, msg: Bytes): Buff;
export declare function hmac512(key: Bytes, msg: Bytes): Buff;
export declare function taghash(tag: string): Buff;
export declare function digest(tag: string, ...data: Bytes[]): Buff;
//# sourceMappingURL=hash.d.ts.map