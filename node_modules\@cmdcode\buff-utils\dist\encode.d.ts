export declare const Encoder: {
    b58chk: {
        encode: (data: Uint8Array) => string;
        decode: (data: string) => Uint8Array;
    };
    base64: {
        encode: (data: Uint8Array) => string;
        decode: (data: string) => Uint8Array;
    };
    b64url: {
        encode: (data: Uint8Array) => string;
        decode: (data: string) => Uint8Array;
    };
    bech32: {
        to_words: (from: Uint8Array) => number[];
        to_bytes: (to: number[]) => Uint8Array;
        encode: (prefix: string, words: number[], limit?: number | false) => `${Lowercase<string>}1${string}`;
        decode: (data: string, limit?: number | false) => {
            prefix: string;
            words: number[];
        };
    };
    bech32m: {
        to_words: (from: Uint8Array) => number[];
        to_bytes: (to: number[]) => Uint8Array;
        encode: (prefix: string, words: number[], limit?: number | false) => `${Lowercase<string>}1${string}`;
        decode: (data: string, limit?: number | false) => {
            prefix: string;
            words: number[];
        };
    };
};
//# sourceMappingURL=encode.d.ts.map