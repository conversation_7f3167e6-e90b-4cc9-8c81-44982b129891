var tapscript=function(t){"use strict";function e(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function n(t,...e){if(!(t instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new TypeError(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}const r={number:e,bool:function(t){if("boolean"!=typeof t)throw new Error(`Expected boolean, not ${t}`)},bytes:n,hash:function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");e(t.outputLen),e(t.blockLen)},exists:function(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")},output:function(t,e){n(t);const r=e.outputLen;if(t.length<r)throw new Error(`digestInto() expects output buffer of length at least ${r}`)}};var s=r;
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const i=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),o=(t,e)=>t<<32-e|t>>>e;if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function a(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new TypeError("utf8ToBytes expected string, got "+typeof t);return(new TextEncoder).encode(t)}(t)),!(t instanceof Uint8Array))throw new TypeError(`Expected input type is Uint8Array (got ${typeof t})`);return t}Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));let u=class{clone(){return this._cloneInto()}};function c(t){const e=e=>t().update(a(e)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e}let f=class extends u{constructor(t,e,n,r){super(),this.blockLen=t,this.outputLen=e,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=i(this.buffer)}update(t){s.exists(this);const{view:e,buffer:n,blockLen:r}=this,o=(t=a(t)).length;for(let s=0;s<o;){const a=Math.min(r-this.pos,o-s);if(a!==r)n.set(t.subarray(s,s+a),this.pos),this.pos+=a,s+=a,this.pos===r&&(this.process(e,0),this.pos=0);else{const e=i(t);for(;r<=o-s;s+=r)this.process(e,s)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){s.exists(this),s.output(t,this),this.finished=!0;const{buffer:e,view:n,blockLen:r,isLE:o}=this;let{pos:a}=this;e[a++]=128,this.buffer.subarray(a).fill(0),this.padOffset>r-a&&(this.process(n,0),a=0);for(let t=a;t<r;t++)e[t]=0;!function(t,e,n,r){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const s=BigInt(32),i=BigInt(4294967295),o=Number(n>>s&i),a=Number(n&i),u=r?4:0,c=r?0:4;t.setUint32(e+u,o,r),t.setUint32(e+c,a,r)}(n,r-8,BigInt(8*this.length),o),this.process(n,0);const u=i(t),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const f=c/4,h=this.get();if(f>h.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<f;t++)u.setUint32(4*t,h[t],o)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const n=t.slice(0,e);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:n,length:r,finished:s,destroyed:i,pos:o}=this;return t.length=r,t.pos=o,t.finished=s,t.destroyed=i,r%e&&t.buffer.set(n),t}};const h=(t,e,n)=>t&e^t&n^e&n,d=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),l=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),p=new Uint32Array(64);let g=class extends f{constructor(){super(64,32,8,!1),this.A=0|l[0],this.B=0|l[1],this.C=0|l[2],this.D=0|l[3],this.E=0|l[4],this.F=0|l[5],this.G=0|l[6],this.H=0|l[7]}get(){const{A:t,B:e,C:n,D:r,E:s,F:i,G:o,H:a}=this;return[t,e,n,r,s,i,o,a]}set(t,e,n,r,s,i,o,a){this.A=0|t,this.B=0|e,this.C=0|n,this.D=0|r,this.E=0|s,this.F=0|i,this.G=0|o,this.H=0|a}process(t,e){for(let n=0;n<16;n++,e+=4)p[n]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=p[t-15],n=p[t-2],r=o(e,7)^o(e,18)^e>>>3,s=o(n,17)^o(n,19)^n>>>10;p[t]=s+p[t-7]+r+p[t-16]|0}let{A:n,B:r,C:s,D:i,E:a,F:u,G:c,H:f}=this;for(let t=0;t<64;t++){const e=f+(o(a,6)^o(a,11)^o(a,25))+((l=a)&u^~l&c)+d[t]+p[t]|0,g=(o(n,2)^o(n,13)^o(n,22))+h(n,r,s)|0;f=c,c=u,u=a,a=i+e|0,i=s,s=r,r=n,n=e+g|0}var l;n=n+this.A|0,r=r+this.B|0,s=s+this.C|0,i=i+this.D|0,a=a+this.E|0,u=u+this.F|0,c=c+this.G|0,f=f+this.H|0,this.set(n,r,s,i,a,u,c,f)}roundClean(){p.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}},y=class extends g{constructor(){super(),this.A=-**********,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=**********,this.G=**********,this.H=-**********,this.outputLen=28}};const m=c((()=>new g));function w(t){if(t>Number.MAX_SAFE_INTEGER)throw new TypeError("Number exceeds safe bounds!")}c((()=>new y));const{getRandomValues:b}=crypto??globalThis.crypto??window.crypto;function v(t,e,n="be"){void 0===e&&(e=t.length),function(t,e){if(t.length>e)throw new TypeError(`Data is larger than array size: ${t.length} > ${e}`)}(t,e);const r=new Uint8Array(e).fill(0),s="be"===n?0:e-t.length;return r.set(t,s),r}function _(t){let e,n=0;const r=t.reduce(((t,e)=>t+e.length),0),s=new Uint8Array(r);for(e=0;e<t.length;e++){const r=t[e];s.set(r,n),n+=r.length}return s}const x=new TextEncoder,E=[{name:"base58",charset:"**********************************************************"}];function S(t){for(const e of E)if(e.name===t)return e.charset;throw TypeError("Charset does not exist: "+t)}function k(t){return m(m(t))}const A={encode:function(t,e,n=!1){"string"==typeof t&&(t=x.encode(t));const r=S(e),s=r.length,i=[];let o,a,u,c="",f=0;for(o=0;o<t.length;o++)for(f=0,a=t[o],c+=a>0||(c.length^o)>0?"":"1";f in i||a>0;)u=i[f],u=u>0?256*u+a:a,a=u/s|0,i[f]=u%s,f++;for(;f-- >0;)c+=r[i[f]];return n&&c.length%4>0?c+"=".repeat(4-c.length%4):c},decode:function(t,e){const n=S(e),r=n.length,s=[],i=[];t=t.replace("=","");let o,a,u,c=0;for(o=0;o<t.length;o++){if(c=0,a=n.indexOf(t[o]),a<0)throw new Error(`Character range out of bounds: ${a}`);for(a>0||(i.length^o)>0||i.push(0);c in s||a>0;)u=s[c],u=u>0?u*r+a:a,a=u>>8,s[c]=u%256,c++}for(;c-- >0;)i.push(s[c]);return new Uint8Array(i)}},O={encode:t=>{const e=function(t){return _([t,k(t).slice(0,4)])}(t);return A.encode(e,"base58")},decode:t=>function(t){const e=t.slice(0,-4),n=t.slice(-4);if(k(e).slice(0,4).toString()!==n.toString())throw new Error("Invalid checksum!");return e}(A.decode(t,"base58"))},I="qpzry9x8gf2tvdw0s3jn54khce6mua7l",P=[996825010,642813549,513874426,1027748829,705979059],B=[{version:0,name:"bech32",const:1},{version:1,name:"bech32m",const:734539939}];function T(t){let e=1;for(let n=0;n<t.length;++n){const r=e>>25;e=(33554431&e)<<5^t[n];for(let t=0;t<5;++t)0!=(r>>t&1)&&(e^=P[t])}return e}function U(t){const e=[];let n;for(n=0;n<t.length;++n)e.push(t.charCodeAt(n)>>5);for(e.push(0),n=0;n<t.length;++n)e.push(31&t.charCodeAt(n));return e}function C(t,e,n,r=!0){const s=[];let i=0,o=0;const a=(1<<n)-1,u=(1<<e+n-1)-1;for(const r of t){if(r<0||r>>e>0)throw new Error("Failed to perform base conversion. Invalid value: "+String(r));for(i=(i<<e|r)&u,o+=e;o>=n;)o-=n,s.push(i>>o&a)}if(r)o>0&&s.push(i<<n-o&a);else if(o>=e||(i<<n-o&a)>0)throw new Error("Failed to perform base conversion. Invalid Size!");return s}function N(t,e,n){const r=e.concat(function(t,e,n){const r=T(U(t).concat(e).concat([0,0,0,0,0,0]))^n.const,s=[];for(let t=0;t<6;++t)s.push(r>>5*(5-t)&31);return s}(t,e,n));let s=t+"1";for(let t=0;t<r.length;++t)s+=I.charAt(r[t]);return s}function L(t){if(!function(t){let e,n,r=!1,s=!1;for(e=0;e<t.length;++e){if(n=t.charCodeAt(e),n<33||n>126)return!1;n>=97&&n<=122&&(r=!0),n>=65&&n<=90&&(s=!0)}return!r||!s}(t))throw new Error("Encoded string goes out of bounds!");if(!function(t){const e=t.lastIndexOf("1");return!(e<1||e+7>t.length||t.length>90)}(t=t.toLowerCase()))throw new Error("Encoded string has invalid separator!");const e=[],n=t.lastIndexOf("1"),r=t.substring(0,n);for(let r=n+1;r<t.length;++r){const n=I.indexOf(t.charAt(r));if(-1===n)throw new Error("Character idx out of bounds: "+String(r));e.push(n)}const s=B.find((t=>t.version===e[0]))??B[0];if(!function(t,e,n){return T(U(t).concat(e))===n.const}(r,e,s))throw new Error("Checksum verification failed!");return[r,e.slice(0,e.length-6)]}function Z(t){const e=(t=t.toLowerCase()).split("1",1)[0],[n,r]=L(t),s=C(r.slice(1),5,8,!1),i=s.length;switch(!0){case e!==n:throw new Error("Returned hrp string is invalid.");case null===s||i<2||i>40:throw new Error("Decoded string is invalid or out of spec.");case r[0]>16:throw new Error("Returned version bit is out of range.");default:return Uint8Array.from(s)}}const R={encode:function(t,e="bc",n=0){const r=N(e,[n,...C([...t],8,5)],B.find((t=>t.version===n))??B[0]);return Z(r),r},decode:Z,version:function(t){t=t.toLowerCase();const[e,n]=L(t);return n[0]}},H="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",D=new TextEncoder;function $(t,e=!1,n=!0){"string"==typeof t&&(t=D.encode(t));const r=e?j:H;let s="",i=0,o=0;for(let e=0;e<t.length;e++)for(o=o<<8|t[e],i+=8;i>=6;)i-=6,s+=r[o>>i&63];if(i>0)for(o<<=6-i,s+=r[63&o];i<6;)s+=n?"=":"",i+=2;return s}function z(t,e=!1){const n=e||t.includes("-")||t.includes("_")?j.split(""):H.split(""),r=(t=t.replace(/=+$/,"")).split("");let s=0,i=0;const o=[];for(let t=0;t<r.length;t++){const e=r[t],a=n.indexOf(e);if(-1===a)throw new Error("Invalid character: "+e);s+=6,i<<=6,i|=a,s>=8&&(s-=8,o.push(i>>>s&255))}return new Uint8Array(o)}const K={encode:$,decode:z},F={encode:t=>$(t,!0,!1),decode:t=>z(t,!0)},q=BigInt(0),V=BigInt(255),M=BigInt(256);function G(t,e,n="be"){void 0===e&&(e=function(t){if(t<=0xffn)return 1;if(t<=0xffffn)return 2;if(t<=0xffffffffn)return 4;if(t<=0xffffffffffffffffn)return 8;if(t<=0xffffffffffffffffffffffffffffffffn)return 16;if(t<=0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn)return 32;throw new TypeError("Must specify a fixed buffer size for bigints greater than 32 bytes.")}(t));const r="le"===n,s=new ArrayBuffer(e),i=new DataView(s);let o=r?0:e-1;for(;t>q;){const e=t&V,n=Number(e);r?i.setUint8(o++,n):i.setUint8(o--,n),t=(t-e)/M}return new Uint8Array(s)}function W(t,e,n="be"){void 0===e&&(e=function(t){if(t<=255)return 1;if(t<=65535)return 2;if(t<=4294967295)return 4;throw new TypeError("Numbers larger than 4 bytes must specify a fixed size!")}(t));const r="le"===n,s=new ArrayBuffer(e),i=new DataView(s);let o=r?0:e-1;for(;t>0;){const e=255&t;r?i.setUint8(o++,t):i.setUint8(o--,t),t=(t-e)/256}return new Uint8Array(s)}const Y=new TextEncoder,J=new TextDecoder;function Q(t){return Y.encode(t)}function X(t){return J.decode(t)}function tt(t,e){!function(t){if(null!==t.match(/[^a-fA-f0-9]/))throw new TypeError("Invalid characters in hex string: "+t);if(t.length%2!=0)throw new Error(`Length of hex string is invalid: ${t.length}`)}(t);const n=t.length/2;if(void 0===e&&(e=n),n>e)throw new TypeError(`Hex string is larger than array size: ${n} > ${e}`);return e}function et(t,e,n){if(t instanceof ArrayBuffer)return new Uint8Array(t);if(t instanceof Uint8Array)return v(t,e,n);if("string"==typeof t)return function(t,e,n="le"){e=tt(t,e);const r="le"===n,s=new ArrayBuffer(e),i=new DataView(s);let o=r?0:e-1;for(let e=0;e<t.length;e+=2){const n=t.substring(e,e+2),s=parseInt(n,16);r?i.setUint8(o++,s):i.setUint8(o--,s)}return new Uint8Array(s)}(t,e,n);if("bigint"==typeof t)return G(t,e,n);if("number"==typeof t)return W(t,e,n);if("boolean"==typeof t)return Uint8Array.of(t?1:0);throw TypeError("Unsupported format:"+String(typeof t))}let nt=class t extends Uint8Array{static{this.num=rt}static{this.big=it}static{this.bin=st}static{this.raw=ot}static{this.str=at}static{this.hex=ut}static{this.bytes=ct}static{this.json=ft}static{this.base64=ht}static{this.b64url=dt}static{this.bech32=lt}static{this.b58chk=pt}static{this.encode=Q}static{this.decode=X}static random(e=32){const n=function(t=32){if("function"==typeof b)return crypto.getRandomValues(new Uint8Array(t));throw new Error("Crypto module missing getRandomValues!")}(e);return new t(n,e)}constructor(t,e,n){super(et(t,e,n))}get arr(){return[...this]}get num(){return this.toNum()}get big(){return this.toBig()}get str(){return this.toStr()}get hex(){return this.toHex()}get raw(){return new Uint8Array(this)}get bin(){return this.toBin()}get b58chk(){return this.tob58chk()}get base64(){return this.toBase64()}get b64url(){return this.toB64url()}get digest(){return this.toHash()}get id(){return this.toHash().hex}get stream(){return new gt(this)}toNum(t="be"){return function(t){let e=0;for(let n=t.length-1;n>=0;n--)e=256*e+t[n],w(e);return e}("be"===t?this.reverse():this)}toBin(){return function(t){const e=new Array(8*t.length);let n=0;for(const r of t){if(r>255)throw new Error(`Invalid byte value: ${r}. Byte values must be between 0 and 255.`);for(let t=7;t>=0;t--,n++)e[n]=r>>t&1}return e.join("")}(this)}toBig(t="be"){return function(t){let e=BigInt(0);for(let n=t.length-1;n>=0;n--)e=e*M+BigInt(t[n]);return BigInt(e)}("be"===t?this.reverse():this)}toHash(){const e=m(this);return new t(e)}toJson(){const t=X(this);return JSON.parse(t)}toBech32(t,e=0){return R.encode(this,t,e)}toStr(){return X(this)}toHex(){return function(t){let e="";for(let n=0;n<t.length;n++)e+=t[n].toString(16).padStart(2,"0");return e}(this)}toBytes(){return new Uint8Array(this)}tob58chk(){return O.encode(this)}toBase64(){return K.encode(this)}toB64url(){return F.encode(this)}prepend(e){return t.join([t.bytes(e),this])}append(e){return t.join([this,t.bytes(e)])}slice(e,n){const r=new Uint8Array(this).slice(e,n);return new t(r)}subarray(e,n){const r=new Uint8Array(this).subarray(e,n);return new t(r)}reverse(){const e=new Uint8Array(this).reverse();return new t(e)}write(e,n){const r=t.bytes(e);this.set(r,n)}prefixSize(e){const n=t.varInt(this.length,e);return t.join([n,this])}static from(e){return new t(Uint8Array.from(e))}static of(...e){return new t(Uint8Array.of(...e))}static join(e){const n=_(e.map((e=>t.bytes(e))));return new t(n)}static varInt(e,n){if(e<253)return t.num(e,1);if(e<65536)return t.of(253,...t.num(e,2,n));if(e<4294967296)return t.of(254,...t.num(e,4,n));if(BigInt(e)<0x10000000000000000n)return t.of(255,...t.num(e,8,n));throw new Error(`Value is too large: ${e}`)}};function rt(t,e,n){return new nt(t,e,n)}function st(t,e,n){return new nt(function(t){const e=t.split("").map(Number);if(e.length%8!=0)throw new Error(`Binary array is invalid length: ${t.length}`);const n=new Uint8Array(e.length/8);for(let t=0,r=0;t<e.length;t+=8,r++){let s=0;for(let n=0;n<8;n++)s|=e[t+n]<<7-n;n[r]=s}return n}(t),e,n)}function it(t,e,n){return new nt(t,e,n)}function ot(t,e,n){return new nt(t,e,n)}function at(t,e,n){return new nt(Q(t),e,n)}function ut(t,e,n){return new nt(t,e,n)}function ct(t,e,n){return new nt(t,e,n)}function ft(t){return new nt((e=t,Q(JSON.stringify(e,((t,e)=>"bigint"==typeof e?`${e}n`:e)))));var e}function ht(t){return new nt(K.decode(t))}function dt(t){return new nt(F.decode(t))}function lt(t){return new nt(R.decode(t))}function pt(t){return new nt(O.decode(t))}let gt=class{constructor(t){this.data=nt.bytes(t),this.size=this.data.length}peek(t){if(t>this.size)throw new Error(`Size greater than stream: ${t} > ${this.size}`);return new nt(this.data.slice(0,t))}read(t){t=t??this.readSize();const e=this.peek(t);return this.data=this.data.slice(t),this.size=this.data.length,e}readSize(t){const e=this.read(1).num;switch(!0){case e>=0&&e<253:return e;case 253===e:return this.read(2).toNum(t);case 254===e:return this.read(4).toNum(t);case 255===e:return this.read(8).toNum(t);default:throw new Error(`Varint is out of range: ${e}`)}}};function yt(t,e){const n=nt.bytes(t);if(n.length!==e)throw new Error(`Invalid input size: ${n.hex} !== ${e}`)}function mt(t,e){if(e)throw new Error(t);return!1}function wt(t,...e){const n=nt.str(t).digest.raw,r=e.map((t=>nt.bytes(t)));return nt.join([n,n,nt.join(r)]).digest}const bt={OP_0:0,OP_PUSHDATA1:76,OP_PUSHDATA2:77,OP_PUSHDATA4:78,OP_1NEGATE:79,OP_SUCCESS80:80,OP_1:81,OP_2:82,OP_3:83,OP_4:84,OP_5:85,OP_6:86,OP_7:87,OP_8:88,OP_9:89,OP_10:90,OP_11:91,OP_12:92,OP_13:93,OP_14:94,OP_15:95,OP_16:96,OP_NOP:97,OP_SUCCESS98:98,OP_IF:99,OP_NOTIF:100,OP_ELSE:103,OP_ENDIF:104,OP_VERIFY:105,OP_RETURN:106,OP_TOALTSTACK:107,OP_FROMALTSTACK:108,OP_2DROP:109,OP_2DUP:110,OP_3DUP:111,OP_2OVER:112,OP_2ROT:113,OP_2SWAP:114,OP_IFDUP:115,OP_DEPTH:116,OP_DROP:117,OP_DUP:118,OP_NIP:119,OP_OVER:120,OP_PICK:121,OP_ROLL:122,OP_ROT:123,OP_SWAP:124,OP_TUCK:125,OP_SUCCESS126:126,OP_SUCCESS127:127,OP_SUCCESS128:128,OP_SUCCESS129:129,OP_SIZE:130,OP_SUCCESS131:131,OP_SUCCESS132:132,OP_SUCCESS133:133,OP_SUCCESS134:134,OP_EQUAL:135,OP_EQUALVERIFY:136,OP_SUCCESS137:137,OP_SUCCESS138:138,OP_1ADD:139,OP_1SUB:140,OP_SUCCESS141:141,OP_SUCCESS142:142,OP_NEGATE:143,OP_ABS:144,OP_NOT:145,OP_0NOTEQUAL:146,OP_ADD:147,OP_SUB:148,OP_SUCCESS149:149,OP_SUCCESS150:150,OP_SUCCESS151:151,OP_SUCCESS152:152,OP_SUCCESS153:153,OP_BOOLAND:154,OP_BOOLOR:155,OP_NUMEQUAL:156,OP_NUMEQUALVERIFY:157,OP_NUMNOTEQUAL:158,OP_LESSTHAN:159,OP_GREATERTHAN:160,OP_LESSTHANOREQUAL:161,OP_GREATERTHANOREQUAL:162,OP_MIN:163,OP_MAX:164,OP_WITHIN:165,OP_RIPEMD160:166,OP_SHA1:167,OP_SHA256:168,OP_HASH160:169,OP_HASH256:170,OP_CODESEPARATOR:171,OP_CHECKSIG:172,OP_CHECKSIGVERIFY:173,OP_CHECKMULTISIG:174,OP_CHECKMULTISIGVERIFY:175,OP_NOP1:176,OP_CHECKLOCKTIMEVERIFY:177,OP_CHECKSEQUENCEVERIFY:178,OP_NOP4:179,OP_NOP5:180,OP_NOP6:181,OP_NOP7:182,OP_NOP8:183,OP_NOP9:184,OP_NOP10:185,OP_CHECKSIGADD:186};function vt(t){if(t>186&&t<255)return"OP_SUCCESS"+String(t);for(const[e,n]of Object.entries(bt))if(n===t)return e;throw new Error("OPCODE not found:"+String(t))}function _t(t){switch(!0){case 0===t:return"opcode";case t>=1&&t<=75:return"varint";case 76===t:return"pushdata1";case 77===t:return"pushdata2";case 78===t:return"pushdata4";case t<=254:return"opcode";default:throw new Error(`Invalid word range: ${t}`)}}function xt(t){switch(!0){case"number"!=typeof t:return!1;case 0===t:return!0;case[].includes(t):return!1;case 75<t&&t<254:return!0;default:return!1}}function Et(t){return"string"==typeof t&&t.length%2==0&&/[0-9a-fA-F]/.test(t)}function St(t){return Et(t)||t instanceof Uint8Array}const kt=520;function At(t=[],e=!0){let n=nt.num(0);return Array.isArray(t)&&(n=nt.raw(Ot(t))),Et(t)&&(n=nt.hex(t)),t instanceof Uint8Array&&(n=nt.raw(t)),e&&(n=n.prefixSize("le")),n}function Ot(t){const e=[];for(const n of t)e.push(It(n));return e.length>0?nt.join(e):new Uint8Array}function It(t){let e=new Uint8Array;if("string"==typeof t){if(t.startsWith("OP_"))return nt.num(function(t){for(const[e,n]of Object.entries(bt))if(e===t)return Number(n);throw new Error("OPCODE not found:"+t)}(t),1);e=Et(t)?nt.hex(t):nt.str(t)}else if(e=nt.bytes(t),1===e.length&&e[0]<=16)return 0!==e[0]&&(e[0]+=80),e;if(e.length>kt){const t=function(t){const e=[],n=new gt(t);for(;n.size>kt;)e.push(n.read(kt));return e.push(n.read(n.size)),e}(e);return Ot(t)}return nt.join([Pt(e.length),e])}function Pt(t){const e=nt.num(76,1),n=nt.num(77,1);switch(!0){case t<=75:return nt.num(t);case t>75&&t<256:return nt.join([e,nt.num(t,1,"le")]);case t>=256&&t<=kt:return nt.join([n,nt.num(t,2,"le")]);default:throw new Error("Invalid word size:"+t.toString())}}function Bt(t,e=!1){let n=nt.bytes(t);if(e){const t=n.stream.readSize("le");if(n=n.slice(1),n.length!==t)throw new Error(`Varint does not match stream size: ${t} !== ${n.length}`)}return function(t){const e=new gt(t),n=[],r=e.size;let s,i,o,a=0;for(;a<r;)switch(s=e.read(1).num,i=_t(s),a++,i){case"varint":n.push(e.read(s).hex),a+=s;break;case"pushdata1":o=e.read(1).reverse().num,n.push(e.read(o).hex),a+=o+1;break;case"pushdata2":o=e.read(2).reverse().num,n.push(e.read(o).hex),a+=o+2;break;case"pushdata4":o=e.read(4).reverse().num,n.push(e.read(o).hex),a+=o+4;break;case"opcode":if(!xt(s))throw new Error(`Invalid OPCODE: ${s}`);n.push(vt(s));break;default:throw new Error(`Word type undefined: ${s}`)}return n}(n)}const Tt={toAsm:function(t,e){if(Array.isArray(t)&&(t=At(t,e)),t instanceof Uint8Array||Et(t))return Bt(t,e);throw new Error("Invalid format: "+String(typeof t))},toBytes:function(t,e){if((t instanceof Uint8Array||Et(t))&&(t=Bt(t,e)),Array.isArray(t))return At(t,e);throw new Error("Invalid format: "+String(typeof t))},toParam:function(t){if(!Array.isArray(t))return nt.bytes(t);throw new Error("Invalid format: "+String(typeof t))}},Ut={encode:At,decode:Bt,fmt:Tt};function Ct(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function Nt(t,...e){if(!(t instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new TypeError(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}const Lt={number:Ct,bool:function(t){if("boolean"!=typeof t)throw new Error(`Expected boolean, not ${t}`)},bytes:Nt,hash:function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");Ct(t.outputLen),Ct(t.blockLen)},exists:function(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")},output:function(t,e){Nt(t);const n=e.outputLen;if(t.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}};var Zt=Lt;const Rt="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0,Ht=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),jt=(t,e)=>t<<32-e|t>>>e;
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function Dt(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new TypeError("utf8ToBytes expected string, got "+typeof t);return(new TextEncoder).encode(t)}(t)),!(t instanceof Uint8Array))throw new TypeError(`Expected input type is Uint8Array (got ${typeof t})`);return t}Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));let $t=class{clone(){return this._cloneInto()}};function zt(t){const e=e=>t().update(Dt(e)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e}function Kt(t=32){if(Rt&&"function"==typeof Rt.getRandomValues)return Rt.getRandomValues(new Uint8Array(t));throw new Error("crypto.getRandomValues must be defined")}let Ft=class extends $t{constructor(t,e,n,r){super(),this.blockLen=t,this.outputLen=e,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=Ht(this.buffer)}update(t){Zt.exists(this);const{view:e,buffer:n,blockLen:r}=this,s=(t=Dt(t)).length;for(let i=0;i<s;){const o=Math.min(r-this.pos,s-i);if(o!==r)n.set(t.subarray(i,i+o),this.pos),this.pos+=o,i+=o,this.pos===r&&(this.process(e,0),this.pos=0);else{const e=Ht(t);for(;r<=s-i;i+=r)this.process(e,i)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){Zt.exists(this),Zt.output(t,this),this.finished=!0;const{buffer:e,view:n,blockLen:r,isLE:s}=this;let{pos:i}=this;e[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>r-i&&(this.process(n,0),i=0);for(let t=i;t<r;t++)e[t]=0;!function(t,e,n,r){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const s=BigInt(32),i=BigInt(4294967295),o=Number(n>>s&i),a=Number(n&i),u=r?4:0,c=r?0:4;t.setUint32(e+u,o,r),t.setUint32(e+c,a,r)}(n,r-8,BigInt(8*this.length),s),this.process(n,0);const o=Ht(t),a=this.outputLen;if(a%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=a/4,c=this.get();if(u>c.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<u;t++)o.setUint32(4*t,c[t],s)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const n=t.slice(0,e);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:n,length:r,finished:s,destroyed:i,pos:o}=this;return t.length=r,t.pos=o,t.finished=s,t.destroyed=i,r%e&&t.buffer.set(n),t}};const qt=(t,e,n)=>t&e^t&n^e&n,Vt=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Mt=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Gt=new Uint32Array(64);let Wt=class extends Ft{constructor(){super(64,32,8,!1),this.A=0|Mt[0],this.B=0|Mt[1],this.C=0|Mt[2],this.D=0|Mt[3],this.E=0|Mt[4],this.F=0|Mt[5],this.G=0|Mt[6],this.H=0|Mt[7]}get(){const{A:t,B:e,C:n,D:r,E:s,F:i,G:o,H:a}=this;return[t,e,n,r,s,i,o,a]}set(t,e,n,r,s,i,o,a){this.A=0|t,this.B=0|e,this.C=0|n,this.D=0|r,this.E=0|s,this.F=0|i,this.G=0|o,this.H=0|a}process(t,e){for(let n=0;n<16;n++,e+=4)Gt[n]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=Gt[t-15],n=Gt[t-2],r=jt(e,7)^jt(e,18)^e>>>3,s=jt(n,17)^jt(n,19)^n>>>10;Gt[t]=s+Gt[t-7]+r+Gt[t-16]|0}let{A:n,B:r,C:s,D:i,E:o,F:a,G:u,H:c}=this;for(let t=0;t<64;t++){const e=c+(jt(o,6)^jt(o,11)^jt(o,25))+((f=o)&a^~f&u)+Vt[t]+Gt[t]|0,h=(jt(n,2)^jt(n,13)^jt(n,22))+qt(n,r,s)|0;c=u,u=a,a=o,o=i+e|0,i=s,s=r,r=n,n=e+h|0}var f;n=n+this.A|0,r=r+this.B|0,s=s+this.C|0,i=i+this.D|0,o=o+this.E|0,a=a+this.F|0,u=u+this.G|0,c=c+this.H|0,this.set(n,r,s,i,o,a,u,c)}roundClean(){Gt.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}},Yt=class extends Wt{constructor(){super(),this.A=-**********,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=**********,this.G=**********,this.H=-**********,this.outputLen=28}};const Jt=zt((()=>new Wt));zt((()=>new Yt));
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const Qt=BigInt(0),Xt=BigInt(1),te=BigInt(2),ee=t=>t instanceof Uint8Array,ne=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));function re(t){if(!ee(t))throw new Error("Uint8Array expected");let e="";for(let n=0;n<t.length;n++)e+=ne[t[n]];return e}function se(t){const e=t.toString(16);return 1&e.length?`0${e}`:e}function ie(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);return BigInt(""===t?"0":`0x${t}`)}function oe(t){if("string"!=typeof t)throw new Error("hex string expected, got "+typeof t);if(t.length%2)throw new Error("hex string is invalid: unpadded "+t.length);const e=new Uint8Array(t.length/2);for(let n=0;n<e.length;n++){const r=2*n,s=t.slice(r,r+2),i=Number.parseInt(s,16);if(Number.isNaN(i)||i<0)throw new Error("invalid byte sequence");e[n]=i}return e}function ae(t){return ie(re(t))}function ue(t){if(!ee(t))throw new Error("Uint8Array expected");return ie(re(Uint8Array.from(t).reverse()))}const ce=(t,e)=>oe(t.toString(16).padStart(2*e,"0")),fe=(t,e)=>ce(t,e).reverse();function he(t,e,n){let r;if("string"==typeof e)try{r=oe(e)}catch(n){throw new Error(`${t} must be valid hex string, got "${e}". Cause: ${n}`)}else{if(!ee(e))throw new Error(`${t} must be hex string or Uint8Array`);r=Uint8Array.from(e)}const s=r.length;if("number"==typeof n&&s!==n)throw new Error(`${t} expected ${n} bytes, got ${s}`);return r}function de(...t){const e=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));let n=0;return t.forEach((t=>{if(!ee(t))throw new Error("Uint8Array expected");e.set(t,n),n+=t.length})),e}function le(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return(new TextEncoder).encode(t)}const pe=t=>(te<<BigInt(t-1))-Xt,ge=t=>new Uint8Array(t),ye=t=>Uint8Array.from(t);function me(t,e,n){if("number"!=typeof t||t<2)throw new Error("hashLen must be a number");if("number"!=typeof e||e<2)throw new Error("qByteLen must be a number");if("function"!=typeof n)throw new Error("hmacFn must be a function");let r=ge(t),s=ge(t),i=0;const o=()=>{r.fill(1),s.fill(0),i=0},a=(...t)=>n(s,r,...t),u=(t=ge())=>{s=a(ye([0]),t),r=a(),0!==t.length&&(s=a(ye([1]),t),r=a())},c=()=>{if(i++>=1e3)throw new Error("drbg: tried 1000 values");let t=0;const n=[];for(;t<e;){r=a();const e=r.slice();n.push(e),t+=r.length}return de(...n)};return(t,e)=>{let n;for(o(),u(t);!(n=e(c()));)u();return o(),n}}const we={bigint:t=>"bigint"==typeof t,function:t=>"function"==typeof t,boolean:t=>"boolean"==typeof t,string:t=>"string"==typeof t,isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>"function"==typeof t&&Number.isSafeInteger(t.outputLen)};function be(t,e,n={}){const r=(e,n,r)=>{const s=we[n];if("function"!=typeof s)throw new Error(`Invalid validator "${n}", expected function`);const i=t[e];if(!(r&&void 0===i||s(i,t)))throw new Error(`Invalid param ${String(e)}=${i} (${typeof i}), expected ${n}`)};for(const[t,n]of Object.entries(e))r(t,n,!1);for(const[t,e]of Object.entries(n))r(t,e,!0);return t}var ve=Object.freeze({__proto__:null,bitGet:(t,e)=>t>>BigInt(e)&1n,bitLen:function(t){let e;for(e=0;t>0n;t>>=Xt,e+=1);return e},bitMask:pe,bitSet:(t,e,n)=>t|(n?Xt:Qt)<<BigInt(e),bytesToHex:re,bytesToNumberBE:ae,bytesToNumberLE:ue,concatBytes:de,createHmacDrbg:me,ensureBytes:he,equalBytes:function(t,e){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++)if(t[n]!==e[n])return!1;return!0},hexToBytes:oe,hexToNumber:ie,numberToBytesBE:ce,numberToBytesLE:fe,numberToHexUnpadded:se,numberToVarBytesBE:t=>oe(se(t)),utf8ToBytes:le,validateObject:be});
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const _e=BigInt(0),xe=BigInt(1),Ee=BigInt(2),Se=BigInt(3),ke=BigInt(4),Ae=BigInt(5),Oe=BigInt(8);function Ie(t,e){const n=t%e;return n>=_e?n:e+n}function Pe(t,e,n){if(n<=_e||e<_e)throw new Error("Expected power/modulo > 0");if(n===xe)return _e;let r=xe;for(;e>_e;)e&xe&&(r=r*t%n),t=t*t%n,e>>=xe;return r}function Be(t,e,n){let r=t;for(;e-- >_e;)r*=r,r%=n;return r}function Te(t,e){if(t===_e||e<=_e)throw new Error(`invert: expected positive integers, got n=${t} mod=${e}`);let n=Ie(t,e),r=e,s=_e,i=xe;for(;n!==_e;){const t=r%n,e=s-i*(r/n);r=n,n=t,s=i,i=e}if(r!==xe)throw new Error("invert: does not exist");return Ie(s,e)}function Ue(t){if(t%ke===Se){const e=(t+xe)/ke;return function(t,n){const r=t.pow(n,e);if(!t.eql(t.sqr(r),n))throw new Error("Cannot find square root");return r}}if(t%Oe===Ae){const e=(t-Ae)/Oe;return function(t,n){const r=t.mul(n,Ee),s=t.pow(r,e),i=t.mul(n,s),o=t.mul(t.mul(i,Ee),s),a=t.mul(i,t.sub(o,t.ONE));if(!t.eql(t.sqr(a),n))throw new Error("Cannot find square root");return a}}return function(t){const e=(t-xe)/Ee;let n,r,s;for(n=t-xe,r=0;n%Ee===_e;n/=Ee,r++);for(s=Ee;s<t&&Pe(s,e,t)!==t-xe;s++);if(1===r){const e=(t+xe)/ke;return function(t,n){const r=t.pow(n,e);if(!t.eql(t.sqr(r),n))throw new Error("Cannot find square root");return r}}const i=(n+xe)/Ee;return function(t,o){if(t.pow(o,e)===t.neg(t.ONE))throw new Error("Cannot find square root");let a=r,u=t.pow(t.mul(t.ONE,s),n),c=t.pow(o,i),f=t.pow(o,n);for(;!t.eql(f,t.ONE);){if(t.eql(f,t.ZERO))return t.ZERO;let e=1;for(let n=t.sqr(f);e<a&&!t.eql(n,t.ONE);e++)n=t.sqr(n);const n=t.pow(u,xe<<BigInt(a-e-1));u=t.sqr(n),c=t.mul(c,n),f=t.mul(f,u),a=e}return c}}(t)}BigInt(9),BigInt(16);const Ce=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function Ne(t){return be(t,Ce.reduce(((t,e)=>(t[e]="function",t)),{ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"}))}function Le(t,e){const n=void 0!==e?e:t.toString(2).length;return{nBitLength:n,nByteLength:Math.ceil(n/8)}}function Ze(t,e,n=!1,r={}){if(t<=_e)throw new Error(`Expected Fp ORDER > 0, got ${t}`);const{nBitLength:s,nByteLength:i}=Le(t,e);if(i>2048)throw new Error("Field lengths over 2048 bytes are not supported");const o=Ue(t),a=Object.freeze({ORDER:t,BITS:s,BYTES:i,MASK:pe(s),ZERO:_e,ONE:xe,create:e=>Ie(e,t),isValid:e=>{if("bigint"!=typeof e)throw new Error("Invalid field element: expected bigint, got "+typeof e);return _e<=e&&e<t},is0:t=>t===_e,isOdd:t=>(t&xe)===xe,neg:e=>Ie(-e,t),eql:(t,e)=>t===e,sqr:e=>Ie(e*e,t),add:(e,n)=>Ie(e+n,t),sub:(e,n)=>Ie(e-n,t),mul:(e,n)=>Ie(e*n,t),pow:(t,e)=>function(t,e,n){if(n<_e)throw new Error("Expected power > 0");if(n===_e)return t.ONE;if(n===xe)return e;let r=t.ONE,s=e;for(;n>_e;)n&xe&&(r=t.mul(r,s)),s=t.sqr(s),n>>=xe;return r}(a,t,e),div:(e,n)=>Ie(e*Te(n,t),t),sqrN:t=>t*t,addN:(t,e)=>t+e,subN:(t,e)=>t-e,mulN:(t,e)=>t*e,inv:e=>Te(e,t),sqrt:r.sqrt||(t=>o(a,t)),invertBatch:t=>function(t,e){const n=new Array(e.length),r=e.reduce(((e,r,s)=>t.is0(r)?e:(n[s]=e,t.mul(e,r))),t.ONE),s=t.inv(r);return e.reduceRight(((e,r,s)=>t.is0(r)?e:(n[s]=t.mul(e,n[s]),t.mul(e,r))),s),n}(a,t),cmov:(t,e,n)=>n?e:t,toBytes:t=>n?fe(t,i):ce(t,i),fromBytes:t=>{if(t.length!==i)throw new Error(`Fp.fromBytes: expected ${i}, got ${t.length}`);return n?ue(t):ae(t)}});return Object.freeze(a)}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const Re=BigInt(0),He=BigInt(1);function je(t){return Ne(t.Fp),be(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...Le(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const{bytesToNumberBE:De,hexToBytes:$e}=ve,ze={Err:class extends Error{constructor(t=""){super(t)}},_parseInt(t){const{Err:e}=ze;if(t.length<2||2!==t[0])throw new e("Invalid signature integer tag");const n=t[1],r=t.subarray(2,n+2);if(!n||r.length!==n)throw new e("Invalid signature integer: wrong length");if(0===r[0]&&r[1]<=127)throw new e("Invalid signature integer: trailing length");return{d:De(r),l:t.subarray(n+2)}},toSig(t){const{Err:e}=ze,n="string"==typeof t?$e(t):t;if(!(n instanceof Uint8Array))throw new Error("ui8a expected");let r=n.length;if(r<2||48!=n[0])throw new e("Invalid signature tag");if(n[1]!==r-2)throw new e("Invalid signature: incorrect length");const{d:s,l:i}=ze._parseInt(n.subarray(2)),{d:o,l:a}=ze._parseInt(i);if(a.length)throw new e("Invalid signature: left bytes after parsing");return{r:s,s:o}},hexFromSig(t){const e=t=>Number.parseInt(t[0],16)>=8?"00"+t:t,n=t=>{const e=t.toString(16);return 1&e.length?`0${e}`:e},r=e(n(t.s)),s=e(n(t.r)),i=r.length/2,o=s.length/2,a=n(i),u=n(o);return`30${n(o+i+4)}02${u}${s}02${a}${r}`}},Ke=BigInt(0),Fe=BigInt(1),qe=BigInt(2),Ve=BigInt(3),Me=BigInt(4);function Ge(t){const e=function(t){const e=je(t);be(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:n,Fp:r,a:s}=e;if(n){if(!r.eql(s,r.ZERO))throw new Error("Endomorphism can only be defined for Koblitz curves that have a=0");if("object"!=typeof n||"bigint"!=typeof n.beta||"function"!=typeof n.splitScalar)throw new Error("Expected endomorphism with beta: bigint and splitScalar: function")}return Object.freeze({...e})}(t),{Fp:n}=e,r=e.toBytes||((t,e,r)=>{const s=e.toAffine();return de(Uint8Array.from([4]),n.toBytes(s.x),n.toBytes(s.y))}),s=e.fromBytes||(t=>{const e=t.subarray(1);return{x:n.fromBytes(e.subarray(0,n.BYTES)),y:n.fromBytes(e.subarray(n.BYTES,2*n.BYTES))}});function i(t){const{a:r,b:s}=e,i=n.sqr(t),o=n.mul(i,t);return n.add(n.add(o,n.mul(t,r)),s)}function o(t){return"bigint"==typeof t&&Ke<t&&t<e.n}function a(t){if(!o(t))throw new Error("Expected valid bigint: 0 < bigint < curve.n")}function u(t){const{allowedPrivateKeyLengths:n,nByteLength:r,wrapPrivateKey:s,n:i}=e;if(n&&"bigint"!=typeof t){if(t instanceof Uint8Array&&(t=re(t)),"string"!=typeof t||!n.includes(t.length))throw new Error("Invalid key");t=t.padStart(2*r,"0")}let o;try{o="bigint"==typeof t?t:ae(he("private key",t,r))}catch(e){throw new Error(`private key must be ${r} bytes, hex or bigint, not ${typeof t}`)}return s&&(o=Ie(o,i)),a(o),o}const c=new Map;function f(t){if(!(t instanceof h))throw new Error("ProjectivePoint expected")}class h{constructor(t,e,r){if(this.px=t,this.py=e,this.pz=r,null==t||!n.isValid(t))throw new Error("x required");if(null==e||!n.isValid(e))throw new Error("y required");if(null==r||!n.isValid(r))throw new Error("z required")}static fromAffine(t){const{x:e,y:r}=t||{};if(!t||!n.isValid(e)||!n.isValid(r))throw new Error("invalid affine point");if(t instanceof h)throw new Error("projective point not allowed");const s=t=>n.eql(t,n.ZERO);return s(e)&&s(r)?h.ZERO:new h(e,r,n.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(t){const e=n.invertBatch(t.map((t=>t.pz)));return t.map(((t,n)=>t.toAffine(e[n]))).map(h.fromAffine)}static fromHex(t){const e=h.fromAffine(s(he("pointHex",t)));return e.assertValidity(),e}static fromPrivateKey(t){return h.BASE.multiply(u(t))}_setWindowSize(t){this._WINDOW_SIZE=t,c.delete(this)}assertValidity(){if(this.is0()){if(e.allowInfinityPoint)return;throw new Error("bad point: ZERO")}const{x:t,y:r}=this.toAffine();if(!n.isValid(t)||!n.isValid(r))throw new Error("bad point: x or y not FE");const s=n.sqr(r),o=i(t);if(!n.eql(s,o))throw new Error("bad point: equation left != right");if(!this.isTorsionFree())throw new Error("bad point: not in prime-order subgroup")}hasEvenY(){const{y:t}=this.toAffine();if(n.isOdd)return!n.isOdd(t);throw new Error("Field doesn't support isOdd")}equals(t){f(t);const{px:e,py:r,pz:s}=this,{px:i,py:o,pz:a}=t,u=n.eql(n.mul(e,a),n.mul(i,s)),c=n.eql(n.mul(r,a),n.mul(o,s));return u&&c}negate(){return new h(this.px,n.neg(this.py),this.pz)}double(){const{a:t,b:r}=e,s=n.mul(r,Ve),{px:i,py:o,pz:a}=this;let u=n.ZERO,c=n.ZERO,f=n.ZERO,d=n.mul(i,i),l=n.mul(o,o),p=n.mul(a,a),g=n.mul(i,o);return g=n.add(g,g),f=n.mul(i,a),f=n.add(f,f),u=n.mul(t,f),c=n.mul(s,p),c=n.add(u,c),u=n.sub(l,c),c=n.add(l,c),c=n.mul(u,c),u=n.mul(g,u),f=n.mul(s,f),p=n.mul(t,p),g=n.sub(d,p),g=n.mul(t,g),g=n.add(g,f),f=n.add(d,d),d=n.add(f,d),d=n.add(d,p),d=n.mul(d,g),c=n.add(c,d),p=n.mul(o,a),p=n.add(p,p),d=n.mul(p,g),u=n.sub(u,d),f=n.mul(p,l),f=n.add(f,f),f=n.add(f,f),new h(u,c,f)}add(t){f(t);const{px:r,py:s,pz:i}=this,{px:o,py:a,pz:u}=t;let c=n.ZERO,d=n.ZERO,l=n.ZERO;const p=e.a,g=n.mul(e.b,Ve);let y=n.mul(r,o),m=n.mul(s,a),w=n.mul(i,u),b=n.add(r,s),v=n.add(o,a);b=n.mul(b,v),v=n.add(y,m),b=n.sub(b,v),v=n.add(r,i);let _=n.add(o,u);return v=n.mul(v,_),_=n.add(y,w),v=n.sub(v,_),_=n.add(s,i),c=n.add(a,u),_=n.mul(_,c),c=n.add(m,w),_=n.sub(_,c),l=n.mul(p,v),c=n.mul(g,w),l=n.add(c,l),c=n.sub(m,l),l=n.add(m,l),d=n.mul(c,l),m=n.add(y,y),m=n.add(m,y),w=n.mul(p,w),v=n.mul(g,v),m=n.add(m,w),w=n.sub(y,w),w=n.mul(p,w),v=n.add(v,w),y=n.mul(m,v),d=n.add(d,y),y=n.mul(_,v),c=n.mul(b,c),c=n.sub(c,y),y=n.mul(b,m),l=n.mul(_,l),l=n.add(l,y),new h(c,d,l)}subtract(t){return this.add(t.negate())}is0(){return this.equals(h.ZERO)}wNAF(t){return l.wNAFCached(this,c,t,(t=>{const e=n.invertBatch(t.map((t=>t.pz)));return t.map(((t,n)=>t.toAffine(e[n]))).map(h.fromAffine)}))}multiplyUnsafe(t){const r=h.ZERO;if(t===Ke)return r;if(a(t),t===Fe)return this;const{endo:s}=e;if(!s)return l.unsafeLadder(this,t);let{k1neg:i,k1:o,k2neg:u,k2:c}=s.splitScalar(t),f=r,d=r,p=this;for(;o>Ke||c>Ke;)o&Fe&&(f=f.add(p)),c&Fe&&(d=d.add(p)),p=p.double(),o>>=Fe,c>>=Fe;return i&&(f=f.negate()),u&&(d=d.negate()),d=new h(n.mul(d.px,s.beta),d.py,d.pz),f.add(d)}multiply(t){a(t);let r,s,i=t;const{endo:o}=e;if(o){const{k1neg:t,k1:e,k2neg:a,k2:u}=o.splitScalar(i);let{p:c,f:f}=this.wNAF(e),{p:d,f:p}=this.wNAF(u);c=l.constTimeNegate(t,c),d=l.constTimeNegate(a,d),d=new h(n.mul(d.px,o.beta),d.py,d.pz),r=c.add(d),s=f.add(p)}else{const{p:t,f:e}=this.wNAF(i);r=t,s=e}return h.normalizeZ([r,s])[0]}multiplyAndAddUnsafe(t,e,n){const r=h.BASE,s=(t,e)=>e!==Ke&&e!==Fe&&t.equals(r)?t.multiply(e):t.multiplyUnsafe(e),i=s(this,e).add(s(t,n));return i.is0()?void 0:i}toAffine(t){const{px:e,py:r,pz:s}=this,i=this.is0();null==t&&(t=i?n.ONE:n.inv(s));const o=n.mul(e,t),a=n.mul(r,t),u=n.mul(s,t);if(i)return{x:n.ZERO,y:n.ZERO};if(!n.eql(u,n.ONE))throw new Error("invZ was invalid");return{x:o,y:a}}isTorsionFree(){const{h:t,isTorsionFree:n}=e;if(t===Fe)return!0;if(n)return n(h,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:t,clearCofactor:n}=e;return t===Fe?this:n?n(h,this):this.multiplyUnsafe(e.h)}toRawBytes(t=!0){return this.assertValidity(),r(h,this,t)}toHex(t=!0){return re(this.toRawBytes(t))}}h.BASE=new h(e.Gx,e.Gy,n.ONE),h.ZERO=new h(n.ZERO,n.ONE,n.ZERO);const d=e.nBitLength,l=function(t,e){const n=(t,e)=>{const n=e.negate();return t?n:e},r=t=>({windows:Math.ceil(e/t)+1,windowSize:2**(t-1)});return{constTimeNegate:n,unsafeLadder(e,n){let r=t.ZERO,s=e;for(;n>Re;)n&He&&(r=r.add(s)),s=s.double(),n>>=He;return r},precomputeWindow(t,e){const{windows:n,windowSize:s}=r(e),i=[];let o=t,a=o;for(let t=0;t<n;t++){a=o,i.push(a);for(let t=1;t<s;t++)a=a.add(o),i.push(a);o=a.double()}return i},wNAF(e,s,i){const{windows:o,windowSize:a}=r(e);let u=t.ZERO,c=t.BASE;const f=BigInt(2**e-1),h=2**e,d=BigInt(e);for(let t=0;t<o;t++){const e=t*a;let r=Number(i&f);i>>=d,r>a&&(r-=h,i+=He);const o=e,l=e+Math.abs(r)-1,p=t%2!=0,g=r<0;0===r?c=c.add(n(p,s[o])):u=u.add(n(g,s[l]))}return{p:u,f:c}},wNAFCached(t,e,n,r){const s=t._WINDOW_SIZE||1;let i=e.get(t);return i||(i=this.precomputeWindow(t,s),1!==s&&e.set(t,r(i))),this.wNAF(s,i,n)}}}(h,e.endo?Math.ceil(d/2):d);return{CURVE:e,ProjectivePoint:h,normPrivateKeyToScalar:u,weierstrassEquation:i,isWithinCurveOrder:o}}function We(t){const e=function(t){const e=je(t);return be(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}(t),{Fp:n,n:r}=e,s=n.BYTES+1,i=2*n.BYTES+1;function o(t){return Ie(t,r)}function a(t){return Te(t,r)}const{ProjectivePoint:u,normPrivateKeyToScalar:c,weierstrassEquation:f,isWithinCurveOrder:h}=Ge({...e,toBytes(t,e,r){const s=e.toAffine(),i=n.toBytes(s.x),o=de;return r?o(Uint8Array.from([e.hasEvenY()?2:3]),i):o(Uint8Array.from([4]),i,n.toBytes(s.y))},fromBytes(t){const e=t.length,r=t[0],o=t.subarray(1);if(e!==s||2!==r&&3!==r){if(e===i&&4===r){return{x:n.fromBytes(o.subarray(0,n.BYTES)),y:n.fromBytes(o.subarray(n.BYTES,2*n.BYTES))}}throw new Error(`Point of length ${e} was invalid. Expected ${s} compressed bytes or ${i} uncompressed bytes`)}{const t=ae(o);if(!(Ke<(a=t)&&a<n.ORDER))throw new Error("Point is not on curve");const e=f(t);let s=n.sqrt(e);return 1==(1&r)!==((s&Fe)===Fe)&&(s=n.neg(s)),{x:t,y:s}}var a}}),d=t=>re(ce(t,e.nByteLength));function l(t){return t>r>>Fe}const p=(t,e,n)=>ae(t.slice(e,n));class g{constructor(t,e,n){this.r=t,this.s=e,this.recovery=n,this.assertValidity()}static fromCompact(t){const n=e.nByteLength;return t=he("compactSignature",t,2*n),new g(p(t,0,n),p(t,n,2*n))}static fromDER(t){const{r:e,s:n}=ze.toSig(he("DER",t));return new g(e,n)}assertValidity(){if(!h(this.r))throw new Error("r must be 0 < r < CURVE.n");if(!h(this.s))throw new Error("s must be 0 < s < CURVE.n")}addRecoveryBit(t){return new g(this.r,this.s,t)}recoverPublicKey(t){const{r:r,s:s,recovery:i}=this,c=b(he("msgHash",t));if(null==i||![0,1,2,3].includes(i))throw new Error("recovery id invalid");const f=2===i||3===i?r+e.n:r;if(f>=n.ORDER)throw new Error("recovery id 2 or 3 invalid");const h=0==(1&i)?"02":"03",l=u.fromHex(h+d(f)),p=a(f),g=o(-c*p),y=o(s*p),m=u.BASE.multiplyAndAddUnsafe(l,g,y);if(!m)throw new Error("point at infinify");return m.assertValidity(),m}hasHighS(){return l(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return oe(this.toDERHex())}toDERHex(){return ze.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return oe(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const y={isValidPrivateKey(t){try{return c(t),!0}catch(t){return!1}},normPrivateKeyToScalar:c,randomPrivateKey:()=>{const t=function(t,e,n=!1){const r=(t=he("privateHash",t)).length,s=Le(e).nByteLength+8;if(s<24||r<s||r>1024)throw new Error(`hashToPrivateScalar: expected ${s}-1024 bytes of input, got ${r}`);return Ie(n?ue(t):ae(t),e-xe)+xe}(e.randomBytes(n.BYTES+8),r);return ce(t,e.nByteLength)},precompute:(t=8,e=u.BASE)=>(e._setWindowSize(t),e.multiply(BigInt(3)),e)};function m(t){const e=t instanceof Uint8Array,n="string"==typeof t,r=(e||n)&&t.length;return e?r===s||r===i:n?r===2*s||r===2*i:t instanceof u}const w=e.bits2int||function(t){const n=ae(t),r=8*t.length-e.nBitLength;return r>0?n>>BigInt(r):n},b=e.bits2int_modN||function(t){return o(w(t))},v=pe(e.nBitLength);function _(t){if("bigint"!=typeof t)throw new Error("bigint expected");if(!(Ke<=t&&t<v))throw new Error(`bigint expected < 2^${e.nBitLength}`);return ce(t,e.nByteLength)}function x(t,r,s=E){if(["recovered","canonical"].some((t=>t in s)))throw new Error("sign() legacy options not supported");const{hash:i,randomBytes:f}=e;let{lowS:d,prehash:p,extraEntropy:y}=s;null==d&&(d=!0),t=he("msgHash",t),p&&(t=he("prehashed msgHash",i(t)));const m=b(t),v=c(r),x=[_(v),_(m)];if(null!=y){const t=!0===y?f(n.BYTES):y;x.push(he("extraEntropy",t,n.BYTES))}const S=de(...x),k=m;return{seed:S,k2sig:function(t){const e=w(t);if(!h(e))return;const n=a(e),r=u.BASE.multiply(e).toAffine(),s=o(r.x);if(s===Ke)return;const i=o(n*o(k+s*v));if(i===Ke)return;let c=(r.x===s?0:2)|Number(r.y&Fe),f=i;return d&&l(i)&&(f=function(t){return l(t)?o(-t):t}(i),c^=1),new g(s,f,c)}}}const E={lowS:e.lowS,prehash:!1},S={lowS:e.lowS,prehash:!1};return u.BASE._setWindowSize(8),{CURVE:e,getPublicKey:function(t,e=!0){return u.fromPrivateKey(t).toRawBytes(e)},getSharedSecret:function(t,e,n=!0){if(m(t))throw new Error("first arg must be private key");if(!m(e))throw new Error("second arg must be public key");return u.fromHex(e).multiply(c(t)).toRawBytes(n)},sign:function(t,n,r=E){const{seed:s,k2sig:i}=x(t,n,r);return me(e.hash.outputLen,e.nByteLength,e.hmac)(s,i)},verify:function(t,n,r,s=S){const i=t;if(n=he("msgHash",n),r=he("publicKey",r),"strict"in s)throw new Error("options.strict was renamed to lowS");const{lowS:c,prehash:f}=s;let h,d;try{if("string"==typeof i||i instanceof Uint8Array)try{h=g.fromDER(i)}catch(t){if(!(t instanceof ze.Err))throw t;h=g.fromCompact(i)}else{if("object"!=typeof i||"bigint"!=typeof i.r||"bigint"!=typeof i.s)throw new Error("PARSE");{const{r:t,s:e}=i;h=new g(t,e)}}d=u.fromHex(r)}catch(t){if("PARSE"===t.message)throw new Error("signature must be Signature instance, Uint8Array or hex string");return!1}if(c&&h.hasHighS())return!1;f&&(n=e.hash(n));const{r:l,s:p}=h,y=b(n),m=a(p),w=o(y*m),v=o(l*m),_=u.BASE.multiplyAndAddUnsafe(d,w,v)?.toAffine();return!!_&&o(_.x)===l},ProjectivePoint:u,Signature:g,utils:y}}let Ye=class extends $t{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,Zt.hash(t);const n=Dt(e);if(this.iHash=t.create(),"function"!=typeof this.iHash.update)throw new TypeError("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const r=this.blockLen,s=new Uint8Array(r);s.set(n.length>r?t.create().update(n).digest():n);for(let t=0;t<s.length;t++)s[t]^=54;this.iHash.update(s),this.oHash=t.create();for(let t=0;t<s.length;t++)s[t]^=106;this.oHash.update(s),s.fill(0)}update(t){return Zt.exists(this),this.iHash.update(t),this}digestInto(t){Zt.exists(this),Zt.bytes(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:n,finished:r,destroyed:s,blockLen:i,outputLen:o}=this;return t.finished=r,t.destroyed=s,t.blockLen=i,t.outputLen=o,t.oHash=e._cloneInto(t.oHash),t.iHash=n._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}};const Je=(t,e,n)=>new Ye(t,e).update(n).digest();
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
function Qe(t){return{hash:t,hmac:(e,...n)=>Je(t,e,function(...t){if(!t.every((t=>t instanceof Uint8Array)))throw new Error("Uint8Array list expected");if(1===t.length)return t[0];const e=t.reduce(((t,e)=>t+e.length),0),n=new Uint8Array(e);for(let e=0,r=0;e<t.length;e++){const s=t[e];n.set(s,r),r+=s.length}return n}(...n)),randomBytes:Kt}}Je.create=(t,e)=>new Ye(t,e);
/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */
const Xe=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),tn=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),en=BigInt(1),nn=BigInt(2),rn=(t,e)=>(t+e/nn)/e;function sn(t){const e=Xe,n=BigInt(3),r=BigInt(6),s=BigInt(11),i=BigInt(22),o=BigInt(23),a=BigInt(44),u=BigInt(88),c=t*t*t%e,f=c*c*t%e,h=Be(f,n,e)*f%e,d=Be(h,n,e)*f%e,l=Be(d,nn,e)*c%e,p=Be(l,s,e)*l%e,g=Be(p,i,e)*p%e,y=Be(g,a,e)*g%e,m=Be(y,u,e)*y%e,w=Be(m,a,e)*g%e,b=Be(w,n,e)*f%e,v=Be(b,o,e)*p%e,_=Be(v,r,e)*c%e,x=Be(_,nn,e);if(!on.eql(on.sqr(x),t))throw new Error("Cannot find square root");return x}const on=Ze(Xe,void 0,void 0,{sqrt:sn}),an=function(t,e){const n=e=>We({...t,...Qe(e)});return Object.freeze({...n(e),create:n})}({a:BigInt(0),b:BigInt(7),Fp:on,n:tn,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{const e=tn,n=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-en*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),s=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),i=n,o=BigInt("0x100000000000000000000000000000000"),a=rn(i*t,e),u=rn(-r*t,e);let c=Ie(t-a*n-u*s,e),f=Ie(-a*r-u*i,e);const h=c>o,d=f>o;if(h&&(c=e-c),d&&(f=e-f),c>o||f>o)throw new Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:h,k1:c,k2neg:d,k2:f}}}},Jt),un=BigInt(0),cn=t=>"bigint"==typeof t&&un<t&&t<Xe,fn=t=>"bigint"==typeof t&&un<t&&t<tn,hn={};function dn(t,...e){let n=hn[t];if(void 0===n){const e=Jt(Uint8Array.from(t,(t=>t.charCodeAt(0))));n=de(e,e),hn[t]=n}return Jt(de(n,...e))}const ln=t=>t.toRawBytes(!0).slice(1),pn=t=>ce(t,32),gn=t=>Ie(t,Xe),yn=t=>Ie(t,tn),mn=an.ProjectivePoint,wn=(t,e,n)=>mn.BASE.multiplyAndAddUnsafe(t,e,n);function bn(t){let e=an.utils.normPrivateKeyToScalar(t),n=mn.fromPrivateKey(e);return{scalar:n.hasEvenY()?e:yn(-e),bytes:ln(n)}}function vn(t){if(!cn(t))throw new Error("bad x: need 0 < x < p");const e=gn(t*t);let n=sn(gn(e*t+BigInt(7)));n%nn!==un&&(n=gn(-n));const r=new mn(t,n,en);return r.assertValidity(),r}function _n(...t){return yn(ae(dn("BIP0340/challenge",...t)))}function xn(t,e,n){const r=he("signature",t,64),s=he("message",e),i=he("publicKey",n,32);try{const t=vn(ae(i)),e=ae(r.subarray(0,32));if(!cn(e))return!1;const n=ae(r.subarray(32,64));if(!fn(n))return!1;const o=_n(pn(e),ln(t),s),a=wn(t,n,yn(-o));return!(!a||!a.hasEvenY()||a.toAffine().x!==e)}catch(t){return!1}}const En={getPublicKey:function(t){return bn(t).bytes},sign:function(t,e,n=Kt(32)){const r=he("message",t),{bytes:s,scalar:i}=bn(e),o=he("auxRand",n,32),a=pn(i^ae(dn("BIP0340/aux",o))),u=dn("BIP0340/nonce",a,s,r),c=yn(ae(u));if(c===un)throw new Error("sign failed: k is zero");const{bytes:f,scalar:h}=bn(c),d=_n(f,s,r),l=new Uint8Array(64);if(l.set(f,0),l.set(pn(yn(h+d*i)),32),!xn(l,r,s))throw new Error("sign: Invalid signature produced");return l},verify:xn,utils:{randomPrivateKey:an.utils.randomPrivateKey,lift_x:vn,pointToBytes:ln,numberToBytesBE:ce,bytesToNumberBE:ae,taggedHash:dn,mod:Ie}},Sn=function(t,e){const n=e.map((t=>Array.from(t).reverse()));return(e,r)=>{const[s,i,o,a]=n.map((n=>n.reduce(((n,r)=>t.add(t.mul(n,e),r)))));return e=t.div(s,i),r=t.mul(r,t.div(o,a)),{x:e,y:r}}}(on,[["0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7","0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581","0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262","0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c"],["0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b","0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14","0x0000000000000000000000000000000000000000000000000000000000000001"],["0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c","0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3","0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931","0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84"],["0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b","0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573","0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f","0x0000000000000000000000000000000000000000000000000000000000000001"]].map((t=>t.map((t=>BigInt(t)))))),kn=function(t,e){if(Ne(t),!t.isValid(e.A)||!t.isValid(e.B)||!t.isValid(e.Z))throw new Error("mapToCurveSimpleSWU: invalid opts");const n=function(t,e){const n=t.ORDER;let r=Ke;for(let t=n-Fe;t%qe===Ke;t/=qe)r+=Fe;const s=r,i=(n-Fe)/qe**s,o=(i-Fe)/qe,a=qe**s-Fe,u=qe**(s-Fe),c=t.pow(e,i),f=t.pow(e,(i+Fe)/qe);let h=(e,n)=>{let r=c,i=t.pow(n,a),h=t.sqr(i);h=t.mul(h,n);let d=t.mul(e,h);d=t.pow(d,o),d=t.mul(d,i),i=t.mul(d,n),h=t.mul(d,e);let l=t.mul(h,i);d=t.pow(l,u);let p=t.eql(d,t.ONE);i=t.mul(h,f),d=t.mul(l,r),h=t.cmov(i,h,p),l=t.cmov(d,l,p);for(let e=s;e>1;e--){let n=qe**(e-qe),s=t.pow(l,n);const o=t.eql(s,t.ONE);i=t.mul(h,r),r=t.mul(r,r),s=t.mul(l,r),h=t.cmov(i,h,o),l=t.cmov(s,l,o)}return{isValid:p,value:h}};if(t.ORDER%Me===Ve){const n=(t.ORDER-Ve)/Me,r=t.sqrt(t.neg(e));h=(e,s)=>{let i=t.sqr(s);const o=t.mul(e,s);i=t.mul(i,o);let a=t.pow(i,n);a=t.mul(a,o);const u=t.mul(a,r),c=t.mul(t.sqr(a),s),f=t.eql(c,e);return{isValid:f,value:t.cmov(u,a,f)}}}return h}(t,e.Z);if(!t.isOdd)throw new Error("Fp.isOdd is not implemented!");return r=>{let s,i,o,a,u,c,f,h;s=t.sqr(r),s=t.mul(s,e.Z),i=t.sqr(s),i=t.add(i,s),o=t.add(i,t.ONE),o=t.mul(o,e.B),a=t.cmov(e.Z,t.neg(i),!t.eql(i,t.ZERO)),a=t.mul(a,e.A),i=t.sqr(o),c=t.sqr(a),u=t.mul(c,e.A),i=t.add(i,u),i=t.mul(i,o),c=t.mul(c,a),u=t.mul(c,e.B),i=t.add(i,u),f=t.mul(s,o);const{isValid:d,value:l}=n(i,c);h=t.mul(s,r),h=t.mul(h,l),f=t.cmov(f,o,d),h=t.cmov(h,l,d);const p=t.isOdd(r)===t.isOdd(h);return h=t.cmov(t.neg(h),h,p),f=t.div(f,a),{x:f,y:h}}}(on,{A:BigInt("0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533"),B:BigInt("1771"),Z:on.create(BigInt("-11"))});function An(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function On(t,...e){if(!(t instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new TypeError(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}!function(t,e,n){if("function"!=typeof e)throw new Error("mapToCurve() must be defined")}(an.ProjectivePoint,(t=>{const{x:e,y:n}=kn(on.create(t[0]));return Sn(e,n)}),{DST:"secp256k1_XMD:SHA-256_SSWU_RO_",encodeDST:"secp256k1_XMD:SHA-256_SSWU_NU_",p:on.ORDER,m:1,k:128,expand:"xmd",hash:Jt});const In={number:An,bool:function(t){if("boolean"!=typeof t)throw new Error(`Expected boolean, not ${t}`)},bytes:On,hash:function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");An(t.outputLen),An(t.blockLen)},exists:function(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")},output:function(t,e){On(t);const n=e.outputLen;if(t.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}};var Pn=In;
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Bn=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),Tn=(t,e)=>t<<32-e|t>>>e;if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function Un(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new TypeError("utf8ToBytes expected string, got "+typeof t);return(new TextEncoder).encode(t)}(t)),!(t instanceof Uint8Array))throw new TypeError(`Expected input type is Uint8Array (got ${typeof t})`);return t}Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));let Cn=class{clone(){return this._cloneInto()}};function Nn(t){const e=e=>t().update(Un(e)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e}let Ln=class extends Cn{constructor(t,e,n,r){super(),this.blockLen=t,this.outputLen=e,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=Bn(this.buffer)}update(t){Pn.exists(this);const{view:e,buffer:n,blockLen:r}=this,s=(t=Un(t)).length;for(let i=0;i<s;){const o=Math.min(r-this.pos,s-i);if(o!==r)n.set(t.subarray(i,i+o),this.pos),this.pos+=o,i+=o,this.pos===r&&(this.process(e,0),this.pos=0);else{const e=Bn(t);for(;r<=s-i;i+=r)this.process(e,i)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){Pn.exists(this),Pn.output(t,this),this.finished=!0;const{buffer:e,view:n,blockLen:r,isLE:s}=this;let{pos:i}=this;e[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>r-i&&(this.process(n,0),i=0);for(let t=i;t<r;t++)e[t]=0;!function(t,e,n,r){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const s=BigInt(32),i=BigInt(4294967295),o=Number(n>>s&i),a=Number(n&i),u=r?4:0,c=r?0:4;t.setUint32(e+u,o,r),t.setUint32(e+c,a,r)}(n,r-8,BigInt(8*this.length),s),this.process(n,0);const o=Bn(t),a=this.outputLen;if(a%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=a/4,c=this.get();if(u>c.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<u;t++)o.setUint32(4*t,c[t],s)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const n=t.slice(0,e);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:n,length:r,finished:s,destroyed:i,pos:o}=this;return t.length=r,t.pos=o,t.finished=s,t.destroyed=i,r%e&&t.buffer.set(n),t}};const Zn=(t,e,n)=>t&e^t&n^e&n,Rn=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Hn=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),jn=new Uint32Array(64);let Dn=class extends Ln{constructor(){super(64,32,8,!1),this.A=0|Hn[0],this.B=0|Hn[1],this.C=0|Hn[2],this.D=0|Hn[3],this.E=0|Hn[4],this.F=0|Hn[5],this.G=0|Hn[6],this.H=0|Hn[7]}get(){const{A:t,B:e,C:n,D:r,E:s,F:i,G:o,H:a}=this;return[t,e,n,r,s,i,o,a]}set(t,e,n,r,s,i,o,a){this.A=0|t,this.B=0|e,this.C=0|n,this.D=0|r,this.E=0|s,this.F=0|i,this.G=0|o,this.H=0|a}process(t,e){for(let n=0;n<16;n++,e+=4)jn[n]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=jn[t-15],n=jn[t-2],r=Tn(e,7)^Tn(e,18)^e>>>3,s=Tn(n,17)^Tn(n,19)^n>>>10;jn[t]=s+jn[t-7]+r+jn[t-16]|0}let{A:n,B:r,C:s,D:i,E:o,F:a,G:u,H:c}=this;for(let t=0;t<64;t++){const e=c+(Tn(o,6)^Tn(o,11)^Tn(o,25))+((f=o)&a^~f&u)+Rn[t]+jn[t]|0,h=(Tn(n,2)^Tn(n,13)^Tn(n,22))+Zn(n,r,s)|0;c=u,u=a,a=o,o=i+e|0,i=s,s=r,r=n,n=e+h|0}var f;n=n+this.A|0,r=r+this.B|0,s=s+this.C|0,i=i+this.D|0,o=o+this.E|0,a=a+this.F|0,u=u+this.G|0,c=c+this.H|0,this.set(n,r,s,i,o,a,u,c)}roundClean(){jn.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}},$n=class extends Dn{constructor(){super(),this.A=-**********,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=**********,this.G=**********,this.H=-**********,this.outputLen=28}};const zn=Nn((()=>new Dn));function Kn(t){if(t>Number.MAX_SAFE_INTEGER)throw new TypeError("Number exceeds safe bounds!")}Nn((()=>new $n));const{getRandomValues:Fn}=crypto??globalThis.crypto??window.crypto;function qn(t,e,n="be"){void 0===e&&(e=t.length),function(t,e){if(t.length>e)throw new TypeError(`Data is larger than array size: ${t.length} > ${e}`)}(t,e);const r=new Uint8Array(e).fill(0),s="be"===n?0:e-t.length;return r.set(t,s),r}function Vn(t){let e,n=0;const r=t.reduce(((t,e)=>t+e.length),0),s=new Uint8Array(r);for(e=0;e<t.length;e++){const r=t[e];s.set(r,n),n+=r.length}return s}const Mn=new TextEncoder,Gn=[{name:"base58",charset:"**********************************************************"}];function Wn(t){for(const e of Gn)if(e.name===t)return e.charset;throw TypeError("Charset does not exist: "+t)}function Yn(t){return zn(zn(t))}const Jn={encode:function(t,e,n=!1){"string"==typeof t&&(t=Mn.encode(t));const r=Wn(e),s=r.length,i=[];let o,a,u,c="",f=0;for(o=0;o<t.length;o++)for(f=0,a=t[o],c+=a>0||(c.length^o)>0?"":"1";f in i||a>0;)u=i[f],u=u>0?256*u+a:a,a=u/s|0,i[f]=u%s,f++;for(;f-- >0;)c+=r[i[f]];return n&&c.length%4>0?c+"=".repeat(4-c.length%4):c},decode:function(t,e){const n=Wn(e),r=n.length,s=[],i=[];t=t.replace("=","");let o,a,u,c=0;for(o=0;o<t.length;o++){if(c=0,a=n.indexOf(t[o]),a<0)throw new Error(`Character range out of bounds: ${a}`);for(a>0||(i.length^o)>0||i.push(0);c in s||a>0;)u=s[c],u=u>0?u*r+a:a,a=u>>8,s[c]=u%256,c++}for(;c-- >0;)i.push(s[c]);return new Uint8Array(i)}},Qn={encode:t=>{const e=function(t){return Vn([t,Yn(t).slice(0,4)])}(t);return Jn.encode(e,"base58")},decode:t=>function(t){const e=t.slice(0,-4),n=t.slice(-4);if(Yn(e).slice(0,4).toString()!==n.toString())throw new Error("Invalid checksum!");return e}(Jn.decode(t,"base58"))},Xn="qpzry9x8gf2tvdw0s3jn54khce6mua7l",tr=[996825010,642813549,513874426,1027748829,705979059],er=[{version:0,name:"bech32",const:1},{version:1,name:"bech32m",const:734539939}];function nr(t){let e=1;for(let n=0;n<t.length;++n){const r=e>>25;e=(33554431&e)<<5^t[n];for(let t=0;t<5;++t)0!=(r>>t&1)&&(e^=tr[t])}return e}function rr(t){const e=[];let n;for(n=0;n<t.length;++n)e.push(t.charCodeAt(n)>>5);for(e.push(0),n=0;n<t.length;++n)e.push(31&t.charCodeAt(n));return e}function sr(t,e,n,r=!0){const s=[];let i=0,o=0;const a=(1<<n)-1,u=(1<<e+n-1)-1;for(const r of t){if(r<0||r>>e>0)throw new Error("Failed to perform base conversion. Invalid value: "+String(r));for(i=(i<<e|r)&u,o+=e;o>=n;)o-=n,s.push(i>>o&a)}if(r)o>0&&s.push(i<<n-o&a);else if(o>=e||(i<<n-o&a)>0)throw new Error("Failed to perform base conversion. Invalid Size!");return s}function ir(t,e,n){const r=e.concat(function(t,e,n){const r=nr(rr(t).concat(e).concat([0,0,0,0,0,0]))^n.const,s=[];for(let t=0;t<6;++t)s.push(r>>5*(5-t)&31);return s}(t,e,n));let s=t+"1";for(let t=0;t<r.length;++t)s+=Xn.charAt(r[t]);return s}function or(t){if(!function(t){let e,n,r=!1,s=!1;for(e=0;e<t.length;++e){if(n=t.charCodeAt(e),n<33||n>126)return!1;n>=97&&n<=122&&(r=!0),n>=65&&n<=90&&(s=!0)}return!r||!s}(t))throw new Error("Encoded string goes out of bounds!");if(!function(t){const e=t.lastIndexOf("1");return!(e<1||e+7>t.length||t.length>90)}(t=t.toLowerCase()))throw new Error("Encoded string has invalid separator!");const e=[],n=t.lastIndexOf("1"),r=t.substring(0,n);for(let r=n+1;r<t.length;++r){const n=Xn.indexOf(t.charAt(r));if(-1===n)throw new Error("Character idx out of bounds: "+String(r));e.push(n)}const s=er.find((t=>t.version===e[0]))??er[0];if(!function(t,e,n){return nr(rr(t).concat(e))===n.const}(r,e,s))throw new Error("Checksum verification failed!");return[r,e.slice(0,e.length-6)]}function ar(t){const e=(t=t.toLowerCase()).split("1",1)[0],[n,r]=or(t),s=sr(r.slice(1),5,8,!1),i=s.length;switch(!0){case e!==n:throw new Error("Returned hrp string is invalid.");case null===s||i<2||i>40:throw new Error("Decoded string is invalid or out of spec.");case r[0]>16:throw new Error("Returned version bit is out of range.");default:return Uint8Array.from(s)}}const ur={encode:function(t,e="bc",n=0){const r=ir(e,[n,...sr([...t],8,5)],er.find((t=>t.version===n))??er[0]);return ar(r),r},decode:ar,version:function(t){t=t.toLowerCase();const[e,n]=or(t);return n[0]}},cr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",hr=new TextEncoder;function dr(t,e=!1,n=!0){"string"==typeof t&&(t=hr.encode(t));const r=e?fr:cr;let s="",i=0,o=0;for(let e=0;e<t.length;e++)for(o=o<<8|t[e],i+=8;i>=6;)i-=6,s+=r[o>>i&63];if(i>0)for(o<<=6-i,s+=r[63&o];i<6;)s+=n?"=":"",i+=2;return s}function lr(t,e=!1){const n=e||t.includes("-")||t.includes("_")?fr.split(""):cr.split(""),r=(t=t.replace(/=+$/,"")).split("");let s=0,i=0;const o=[];for(let t=0;t<r.length;t++){const e=r[t],a=n.indexOf(e);if(-1===a)throw new Error("Invalid character: "+e);s+=6,i<<=6,i|=a,s>=8&&(s-=8,o.push(i>>>s&255))}return new Uint8Array(o)}const pr={encode:dr,decode:lr},gr={encode:t=>dr(t,!0,!1),decode:t=>lr(t,!0)},yr=BigInt(0),mr=BigInt(255),wr=BigInt(256);function br(t,e,n="be"){void 0===e&&(e=function(t){if(t<=0xffn)return 1;if(t<=0xffffn)return 2;if(t<=0xffffffffn)return 4;if(t<=0xffffffffffffffffn)return 8;if(t<=0xffffffffffffffffffffffffffffffffn)return 16;if(t<=0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn)return 32;throw new TypeError("Must specify a fixed buffer size for bigints greater than 32 bytes.")}(t));const r="le"===n,s=new ArrayBuffer(e),i=new DataView(s);let o=r?0:e-1;for(;t>yr;){const e=t&mr,n=Number(e);r?i.setUint8(o++,n):i.setUint8(o--,n),t=(t-e)/wr}return new Uint8Array(s)}function vr(t,e,n="be"){void 0===e&&(e=function(t){if(t<=255)return 1;if(t<=65535)return 2;if(t<=4294967295)return 4;throw new TypeError("Numbers larger than 4 bytes must specify a fixed size!")}(t));const r="le"===n,s=new ArrayBuffer(e),i=new DataView(s);let o=r?0:e-1;for(;t>0;){const e=255&t;r?i.setUint8(o++,t):i.setUint8(o--,t),t=(t-e)/256}return new Uint8Array(s)}const _r=new TextEncoder,xr=new TextDecoder;function Er(t){return _r.encode(t)}function Sr(t){return xr.decode(t)}function kr(t,e){!function(t){if(null!==t.match(/[^a-fA-f0-9]/))throw new TypeError("Invalid characters in hex string: "+t);if(t.length%2!=0)throw new Error(`Length of hex string is invalid: ${t.length}`)}(t);const n=t.length/2;if(void 0===e&&(e=n),n>e)throw new TypeError(`Hex string is larger than array size: ${n} > ${e}`);return e}function Ar(t,e,n){if(t instanceof ArrayBuffer)return new Uint8Array(t);if(t instanceof Uint8Array)return qn(t,e,n);if("string"==typeof t)return function(t,e,n="le"){e=kr(t,e);const r="le"===n,s=new ArrayBuffer(e),i=new DataView(s);let o=r?0:e-1;for(let e=0;e<t.length;e+=2){const n=t.substring(e,e+2),s=parseInt(n,16);r?i.setUint8(o++,s):i.setUint8(o--,s)}return new Uint8Array(s)}(t,e,n);if("bigint"==typeof t)return br(t,e,n);if("number"==typeof t)return vr(t,e,n);if("boolean"==typeof t)return Uint8Array.of(t?1:0);throw TypeError("Unsupported format:"+String(typeof t))}class Or extends Uint8Array{static{this.num=Ir}static{this.big=Br}static{this.bin=Pr}static{this.raw=Tr}static{this.str=Ur}static{this.hex=Cr}static{this.bytes=Nr}static{this.json=Lr}static{this.base64=Zr}static{this.b64url=Rr}static{this.bech32=Hr}static{this.b58chk=jr}static{this.encode=Er}static{this.decode=Sr}static random(t=32){const e=function(t=32){if("function"==typeof Fn)return crypto.getRandomValues(new Uint8Array(t));throw new Error("Crypto module missing getRandomValues!")}(t);return new Or(e,t)}constructor(t,e,n){super(Ar(t,e,n))}get arr(){return[...this]}get num(){return this.toNum()}get big(){return this.toBig()}get str(){return this.toStr()}get hex(){return this.toHex()}get raw(){return new Uint8Array(this)}get bin(){return this.toBin()}get b58chk(){return this.tob58chk()}get base64(){return this.toBase64()}get b64url(){return this.toB64url()}get digest(){return this.toHash()}get id(){return this.toHash().hex}get stream(){return new Dr(this)}toNum(t="be"){return function(t){let e=0;for(let n=t.length-1;n>=0;n--)e=256*e+t[n],Kn(e);return e}("be"===t?this.reverse():this)}toBin(){return function(t){const e=new Array(8*t.length);let n=0;for(const r of t){if(r>255)throw new Error(`Invalid byte value: ${r}. Byte values must be between 0 and 255.`);for(let t=7;t>=0;t--,n++)e[n]=r>>t&1}return e.join("")}(this)}toBig(t="be"){return function(t){let e=BigInt(0);for(let n=t.length-1;n>=0;n--)e=e*wr+BigInt(t[n]);return BigInt(e)}("be"===t?this.reverse():this)}toHash(){const t=zn(this);return new Or(t)}toJson(){const t=Sr(this);return JSON.parse(t)}toBech32(t,e=0){return ur.encode(this,t,e)}toStr(){return Sr(this)}toHex(){return function(t){let e="";for(let n=0;n<t.length;n++)e+=t[n].toString(16).padStart(2,"0");return e}(this)}toBytes(){return new Uint8Array(this)}tob58chk(){return Qn.encode(this)}toBase64(){return pr.encode(this)}toB64url(){return gr.encode(this)}prepend(t){return Or.join([Or.bytes(t),this])}append(t){return Or.join([this,Or.bytes(t)])}slice(t,e){const n=new Uint8Array(this).slice(t,e);return new Or(n)}subarray(t,e){const n=new Uint8Array(this).subarray(t,e);return new Or(n)}reverse(){const t=new Uint8Array(this).reverse();return new Or(t)}write(t,e){const n=Or.bytes(t);this.set(n,e)}prefixSize(t){const e=Or.varInt(this.length,t);return Or.join([e,this])}static from(t){return new Or(Uint8Array.from(t))}static of(...t){return new Or(Uint8Array.of(...t))}static join(t){const e=Vn(t.map((t=>Or.bytes(t))));return new Or(e)}static varInt(t,e){if(t<253)return Or.num(t,1);if(t<65536)return Or.of(253,...Or.num(t,2,e));if(t<4294967296)return Or.of(254,...Or.num(t,4,e));if(BigInt(t)<0x10000000000000000n)return Or.of(255,...Or.num(t,8,e));throw new Error(`Value is too large: ${t}`)}}function Ir(t,e,n){return new Or(t,e,n)}function Pr(t,e,n){return new Or(function(t){const e=t.split("").map(Number);if(e.length%8!=0)throw new Error(`Binary array is invalid length: ${t.length}`);const n=new Uint8Array(e.length/8);for(let t=0,r=0;t<e.length;t+=8,r++){let s=0;for(let n=0;n<8;n++)s|=e[t+n]<<7-n;n[r]=s}return n}(t),e,n)}function Br(t,e,n){return new Or(t,e,n)}function Tr(t,e,n){return new Or(t,e,n)}function Ur(t,e,n){return new Or(Er(t),e,n)}function Cr(t,e,n){return new Or(t,e,n)}function Nr(t,e,n){return new Or(t,e,n)}function Lr(t){return new Or((e=t,Er(JSON.stringify(e,((t,e)=>"bigint"==typeof e?`${e}n`:e)))));var e}function Zr(t){return new Or(pr.decode(t))}function Rr(t){return new Or(gr.decode(t))}function Hr(t){return new Or(ur.decode(t))}function jr(t){return new Or(Qn.decode(t))}class Dr{constructor(t){this.data=Or.bytes(t),this.size=this.data.length}peek(t){if(t>this.size)throw new Error(`Size greater than stream: ${t} > ${this.size}`);return new Or(this.data.slice(0,t))}read(t){t=t??this.readSize();const e=this.peek(t);return this.data=this.data.slice(t),this.size=this.data.length,e}readSize(t){const e=this.read(1).num;switch(!0){case e>=0&&e<253:return e;case 253===e:return this.read(2).toNum(t);case 254===e:return this.read(4).toNum(t);case 255===e:return this.read(8).toNum(t);default:throw new Error(`Varint is out of range: ${e}`)}}}const $r=an.CURVE,zr=$r.n,Kr=$r.p,Fr={x:$r.Gx,y:$r.Gy},qr=BigInt(0),Vr=BigInt(1),Mr=BigInt(2),Gr=BigInt(3),Wr=BigInt(4),Yr={N:zr,P:Kr,G:Fr,_0n:qr,_1n:Vr,_2n:Mr,_3n:Gr,_4n:Wr},Jr=Ze(zr,32,!0),Qr=t=>Ie(t,zr);function Xr(t,e=!1){if(e)throw new Error(t);return!1}function ts(t){return Or.random(t)}var es=Object.freeze({__proto__:null,fail:Xr,mod_bytes:function(t){const e=Or.bytes(t).big;return Or.big(Qr(e),32)},random:ts});const{N:ns,P:rs,_0n:ss}=Yr;const is=an.ProjectivePoint;class os extends Uint8Array{static{this.N=an.CURVE.n}static mod(t){return new os(t)}static is_valid(t,e){return function(t,e){return"bigint"==typeof t&&ss<t&&t<ns||Xr("x value is not in the field!",e),!0}(Or.bytes(t,32).big,e)}constructor(t){let e=function(t){if(t instanceof os)return t.big;if(t instanceof as)return t.x.big;if(t instanceof Uint8Array)return Or.raw(t).big;if("string"==typeof t)return Or.hex(t).big;if("number"==typeof t)return Or.num(t).big;if("bigint"==typeof t)return BigInt(t);throw TypeError("Invalid input type:"+typeof t)}(t);e=Qr(e),os.is_valid(e,!0),super(Or.big(e,32),32)}get buff(){return new Or(this)}get raw(){return this.buff.raw}get big(){return this.buff.big}get hex(){return this.buff.hex}get point(){return this.generate()}get hasOddY(){return this.point.hasOddY}get negated(){return this.hasOddY?this.negate():this}gt(t){return new os(t).big>this.big}lt(t){return new os(t).big<this.big}eq(t){return new os(t).big===this.big}ne(t){return new os(t).big!==this.big}add(t){const e=os.mod(t),n=Jr.add(this.big,e.big);return new os(n)}sub(t){const e=os.mod(t),n=Jr.sub(this.big,e.big);return new os(n)}mul(t){const e=os.mod(t),n=Jr.mul(this.big,e.big);return new os(n)}pow(t){const e=os.mod(t),n=Jr.pow(this.big,e.big);return new os(n)}div(t){const e=os.mod(t),n=Jr.div(this.big,e.big);return new os(n)}negate(){return new os(os.N-this.big)}generate(){const t=an.ProjectivePoint.BASE.multiply(this.big);return as.import(t)}}class as{static{this.P=Yr.P}static{this.G=Yr.G}static{this.base=an.ProjectivePoint.BASE}static from_x(t){let e=function(t){if(t instanceof os)return t.point.buff;if(t instanceof as)return t.buff;if(t instanceof Uint8Array||"string"==typeof t)return Or.bytes(t);if("number"==typeof t||"bigint"==typeof t)return Or.bytes(t,32);throw new TypeError("Unknown type: "+typeof t)}(t);32===e.length&&(e=e.prepend(2)),function(t,e,n){const r=Or.bytes(t);r.length===e||Xr(`Invalid byte size: ${r.hex} !== ${e}`,n)}(e,33);const n=is.fromHex(e.hex);return n.assertValidity(),new as(n.x,n.y)}static generate(t){const e=os.mod(t),n=as.base.multiply(e.big);return as.import(n)}static import(t){const e=t instanceof as?{x:t.x.big,y:t.y.big}:{x:t.x,y:t.y};return new as(e.x,e.y)}constructor(t,e){this._p=new is(t,e,1n),this.p.assertValidity()}get p(){return this._p}get x(){return Or.big(this.p.x,32)}get y(){return Or.big(this.p.y,32)}get buff(){return Or.raw(this.p.toRawBytes(!0))}get raw(){return this.buff.raw}get hex(){return this.buff.hex}get hasEvenY(){return this.p.hasEvenY()}get hasOddY(){return!this.p.hasEvenY()}eq(t){const e=t instanceof as?t:as.from_x(t);return this.x.big===e.x.big&&this.y.big===e.y.big}add(t){return t instanceof as?as.import(this.p.add(t.p)):as.import(this.p.add(as.generate(t).p))}sub(t){return t instanceof as?as.import(this.p.subtract(t.p)):as.import(this.p.subtract(as.generate(t).p))}mul(t){return t instanceof as?as.import(this.p.multiply(t.x.big)):as.import(this.p.multiply(os.mod(t).big))}negate(){return as.import(this.p.negate())}}function us(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function cs(t,...e){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new Error(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}const fs={number:us,bool:function(t){if("boolean"!=typeof t)throw new Error(`Expected boolean, not ${t}`)},bytes:cs,hash:function(t){if("function"!=typeof t||"function"!=typeof t.create)throw new Error("Hash should be wrapped by utils.wrapConstructor");us(t.outputLen),us(t.blockLen)},exists:function(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")},output:function(t,e){cs(t);const n=e.outputLen;if(t.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}};var hs=fs;
/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const ds=t=>t instanceof Uint8Array,ls=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),ps=(t,e)=>t<<32-e|t>>>e;if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error("Non little-endian hardware is not supported");function gs(t){if("string"==typeof t&&(t=function(t){if("string"!=typeof t)throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array((new TextEncoder).encode(t))}(t)),!ds(t))throw new Error("expected Uint8Array, got "+typeof t);return t}Array.from({length:256},((t,e)=>e.toString(16).padStart(2,"0")));class ys{clone(){return this._cloneInto()}}function ms(t){const e=e=>t().update(gs(e)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e}class ws extends ys{constructor(t,e,n,r){super(),this.blockLen=t,this.outputLen=e,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=ls(this.buffer)}update(t){hs.exists(this);const{view:e,buffer:n,blockLen:r}=this,s=(t=gs(t)).length;for(let i=0;i<s;){const o=Math.min(r-this.pos,s-i);if(o!==r)n.set(t.subarray(i,i+o),this.pos),this.pos+=o,i+=o,this.pos===r&&(this.process(e,0),this.pos=0);else{const e=ls(t);for(;r<=s-i;i+=r)this.process(e,i)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){hs.exists(this),hs.output(t,this),this.finished=!0;const{buffer:e,view:n,blockLen:r,isLE:s}=this;let{pos:i}=this;e[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>r-i&&(this.process(n,0),i=0);for(let t=i;t<r;t++)e[t]=0;!function(t,e,n,r){if("function"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const s=BigInt(32),i=BigInt(4294967295),o=Number(n>>s&i),a=Number(n&i),u=r?4:0,c=r?0:4;t.setUint32(e+u,o,r),t.setUint32(e+c,a,r)}(n,r-8,BigInt(8*this.length),s),this.process(n,0);const o=ls(t),a=this.outputLen;if(a%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=a/4,c=this.get();if(u>c.length)throw new Error("_sha2: outputLen bigger than state");for(let t=0;t<u;t++)o.setUint32(4*t,c[t],s)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const n=t.slice(0,e);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:n,length:r,finished:s,destroyed:i,pos:o}=this;return t.length=r,t.pos=o,t.finished=s,t.destroyed=i,r%e&&t.buffer.set(n),t}}const bs=(t,e,n)=>t&e^t&n^e&n,vs=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),_s=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),xs=new Uint32Array(64);class Es extends ws{constructor(){super(64,32,8,!1),this.A=0|_s[0],this.B=0|_s[1],this.C=0|_s[2],this.D=0|_s[3],this.E=0|_s[4],this.F=0|_s[5],this.G=0|_s[6],this.H=0|_s[7]}get(){const{A:t,B:e,C:n,D:r,E:s,F:i,G:o,H:a}=this;return[t,e,n,r,s,i,o,a]}set(t,e,n,r,s,i,o,a){this.A=0|t,this.B=0|e,this.C=0|n,this.D=0|r,this.E=0|s,this.F=0|i,this.G=0|o,this.H=0|a}process(t,e){for(let n=0;n<16;n++,e+=4)xs[n]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=xs[t-15],n=xs[t-2],r=ps(e,7)^ps(e,18)^e>>>3,s=ps(n,17)^ps(n,19)^n>>>10;xs[t]=s+xs[t-7]+r+xs[t-16]|0}let{A:n,B:r,C:s,D:i,E:o,F:a,G:u,H:c}=this;for(let t=0;t<64;t++){const e=c+(ps(o,6)^ps(o,11)^ps(o,25))+((f=o)&a^~f&u)+vs[t]+xs[t]|0,h=(ps(n,2)^ps(n,13)^ps(n,22))+bs(n,r,s)|0;c=u,u=a,a=o,o=i+e|0,i=s,s=r,r=n,n=e+h|0}var f;n=n+this.A|0,r=r+this.B|0,s=s+this.C|0,i=i+this.D|0,o=o+this.E|0,a=a+this.F|0,u=u+this.G|0,c=c+this.H|0,this.set(n,r,s,i,o,a,u,c)}roundClean(){xs.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}class Ss extends Es{constructor(){super(),this.A=-**********,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=**********,this.G=**********,this.H=-**********,this.outputLen=28}}const ks=ms((()=>new Es));ms((()=>new Ss));const As=BigInt(2**32-1),Os=BigInt(32);function Is(t,e=!1){return e?{h:Number(t&As),l:Number(t>>Os&As)}:{h:0|Number(t>>Os&As),l:0|Number(t&As)}}var Ps={fromBig:Is,split:function(t,e=!1){let n=new Uint32Array(t.length),r=new Uint32Array(t.length);for(let s=0;s<t.length;s++){const{h:i,l:o}=Is(t[s],e);[n[s],r[s]]=[i,o]}return[n,r]},toBig:(t,e)=>BigInt(t>>>0)<<Os|BigInt(e>>>0),shrSH:(t,e,n)=>t>>>n,shrSL:(t,e,n)=>t<<32-n|e>>>n,rotrSH:(t,e,n)=>t>>>n|e<<32-n,rotrSL:(t,e,n)=>t<<32-n|e>>>n,rotrBH:(t,e,n)=>t<<64-n|e>>>n-32,rotrBL:(t,e,n)=>t>>>n-32|e<<64-n,rotr32H:(t,e)=>e,rotr32L:(t,e)=>t,rotlSH:(t,e,n)=>t<<n|e>>>32-n,rotlSL:(t,e,n)=>e<<n|t>>>32-n,rotlBH:(t,e,n)=>e<<n-32|t>>>64-n,rotlBL:(t,e,n)=>t<<n-32|e>>>64-n,add:function(t,e,n,r){const s=(e>>>0)+(r>>>0);return{h:t+n+(s/2**32|0)|0,l:0|s}},add3L:(t,e,n)=>(t>>>0)+(e>>>0)+(n>>>0),add3H:(t,e,n,r)=>e+n+r+(t/2**32|0)|0,add4L:(t,e,n,r)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0),add4H:(t,e,n,r,s)=>e+n+r+s+(t/2**32|0)|0,add5H:(t,e,n,r,s,i)=>e+n+r+s+i+(t/2**32|0)|0,add5L:(t,e,n,r,s)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0)+(s>>>0)};const[Bs,Ts]=Ps.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map((t=>BigInt(t)))),Us=new Uint32Array(80),Cs=new Uint32Array(80);class Ns extends ws{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:t,Al:e,Bh:n,Bl:r,Ch:s,Cl:i,Dh:o,Dl:a,Eh:u,El:c,Fh:f,Fl:h,Gh:d,Gl:l,Hh:p,Hl:g}=this;return[t,e,n,r,s,i,o,a,u,c,f,h,d,l,p,g]}set(t,e,n,r,s,i,o,a,u,c,f,h,d,l,p,g){this.Ah=0|t,this.Al=0|e,this.Bh=0|n,this.Bl=0|r,this.Ch=0|s,this.Cl=0|i,this.Dh=0|o,this.Dl=0|a,this.Eh=0|u,this.El=0|c,this.Fh=0|f,this.Fl=0|h,this.Gh=0|d,this.Gl=0|l,this.Hh=0|p,this.Hl=0|g}process(t,e){for(let n=0;n<16;n++,e+=4)Us[n]=t.getUint32(e),Cs[n]=t.getUint32(e+=4);for(let t=16;t<80;t++){const e=0|Us[t-15],n=0|Cs[t-15],r=Ps.rotrSH(e,n,1)^Ps.rotrSH(e,n,8)^Ps.shrSH(e,n,7),s=Ps.rotrSL(e,n,1)^Ps.rotrSL(e,n,8)^Ps.shrSL(e,n,7),i=0|Us[t-2],o=0|Cs[t-2],a=Ps.rotrSH(i,o,19)^Ps.rotrBH(i,o,61)^Ps.shrSH(i,o,6),u=Ps.rotrSL(i,o,19)^Ps.rotrBL(i,o,61)^Ps.shrSL(i,o,6),c=Ps.add4L(s,u,Cs[t-7],Cs[t-16]),f=Ps.add4H(c,r,a,Us[t-7],Us[t-16]);Us[t]=0|f,Cs[t]=0|c}let{Ah:n,Al:r,Bh:s,Bl:i,Ch:o,Cl:a,Dh:u,Dl:c,Eh:f,El:h,Fh:d,Fl:l,Gh:p,Gl:g,Hh:y,Hl:m}=this;for(let t=0;t<80;t++){const e=Ps.rotrSH(f,h,14)^Ps.rotrSH(f,h,18)^Ps.rotrBH(f,h,41),w=Ps.rotrSL(f,h,14)^Ps.rotrSL(f,h,18)^Ps.rotrBL(f,h,41),b=f&d^~f&p,v=h&l^~h&g,_=Ps.add5L(m,w,v,Ts[t],Cs[t]),x=Ps.add5H(_,y,e,b,Bs[t],Us[t]),E=0|_,S=Ps.rotrSH(n,r,28)^Ps.rotrBH(n,r,34)^Ps.rotrBH(n,r,39),k=Ps.rotrSL(n,r,28)^Ps.rotrBL(n,r,34)^Ps.rotrBL(n,r,39),A=n&s^n&o^s&o,O=r&i^r&a^i&a;y=0|p,m=0|g,p=0|d,g=0|l,d=0|f,l=0|h,({h:f,l:h}=Ps.add(0|u,0|c,0|x,0|E)),u=0|o,c=0|a,o=0|s,a=0|i,s=0|n,i=0|r;const I=Ps.add3L(E,k,O);n=Ps.add3H(I,x,S,A),r=0|I}({h:n,l:r}=Ps.add(0|this.Ah,0|this.Al,0|n,0|r)),({h:s,l:i}=Ps.add(0|this.Bh,0|this.Bl,0|s,0|i)),({h:o,l:a}=Ps.add(0|this.Ch,0|this.Cl,0|o,0|a)),({h:u,l:c}=Ps.add(0|this.Dh,0|this.Dl,0|u,0|c)),({h:f,l:h}=Ps.add(0|this.Eh,0|this.El,0|f,0|h)),({h:d,l:l}=Ps.add(0|this.Fh,0|this.Fl,0|d,0|l)),({h:p,l:g}=Ps.add(0|this.Gh,0|this.Gl,0|p,0|g)),({h:y,l:m}=Ps.add(0|this.Hh,0|this.Hl,0|y,0|m)),this.set(n,r,s,i,o,a,u,c,f,h,d,l,p,g,y,m)}roundClean(){Us.fill(0),Cs.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}class Ls extends Ns{constructor(){super(),this.Ah=-1942145080,this.Al=424955298,this.Bh=1944164710,this.Bl=-1982016298,this.Ch=502970286,this.Cl=855612546,this.Dh=1738396948,this.Dl=1479516111,this.Eh=258812777,this.El=2077511080,this.Fh=2011393907,this.Fl=79989058,this.Gh=1067287976,this.Gl=1780299464,this.Hh=286451373,this.Hl=-1848208735,this.outputLen=28}}class Zs extends Ns{constructor(){super(),this.Ah=573645204,this.Al=-64227540,this.Bh=-1621794909,this.Bl=-934517566,this.Ch=596883563,this.Cl=1867755857,this.Dh=-1774684391,this.Dl=1497426621,this.Eh=-1775747358,this.El=-1467023389,this.Fh=-1101128155,this.Fl=1401305490,this.Gh=721525244,this.Gl=746961066,this.Hh=246885852,this.Hl=-2117784414,this.outputLen=32}}class Rs extends Ns{constructor(){super(),this.Ah=-876896931,this.Al=-**********,this.Bh=1654270250,this.Bl=914150663,this.Ch=-1856437926,this.Cl=812702999,this.Dh=355462360,this.Dl=-150054599,this.Eh=1731405415,this.El=-4191439,this.Fh=-1900787065,this.Fl=**********,this.Gh=-619958771,this.Gl=**********,this.Hh=1203062813,this.Hl=-**********,this.outputLen=48}}const Hs=ms((()=>new Ns));ms((()=>new Ls)),ms((()=>new Zs)),ms((()=>new Rs));const js=new Uint8Array([7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8]),Ds=Uint8Array.from({length:16},((t,e)=>e)),$s=Ds.map((t=>(9*t+5)%16));let zs=[Ds],Ks=[$s];for(let t=0;t<4;t++)for(let e of[zs,Ks])e.push(e[t].map((t=>js[t])));const Fs=[[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8],[12,13,11,15,6,9,9,7,12,15,11,13,7,8,7,7],[13,15,14,11,7,7,6,8,13,14,13,12,5,5,6,9],[14,11,12,14,8,6,5,5,15,12,15,14,9,9,8,6],[15,12,13,13,9,5,8,6,14,11,12,11,8,6,5,5]].map((t=>new Uint8Array(t))),qs=zs.map(((t,e)=>t.map((t=>Fs[e][t])))),Vs=Ks.map(((t,e)=>t.map((t=>Fs[e][t])))),Ms=new Uint32Array([0,1518500249,1859775393,2400959708,2840853838]),Gs=new Uint32Array([1352829926,1548603684,1836072691,2053994217,0]),Ws=(t,e)=>t<<e|t>>>32-e;function Ys(t,e,n,r){return 0===t?e^n^r:1===t?e&n|~e&r:2===t?(e|~n)^r:3===t?e&r|n&~r:e^(n|~r)}const Js=new Uint32Array(16);class Qs extends ws{constructor(){super(64,20,8,!0),this.h0=1732584193,this.h1=-271733879,this.h2=-1732584194,this.h3=271733878,this.h4=-1009589776}get(){const{h0:t,h1:e,h2:n,h3:r,h4:s}=this;return[t,e,n,r,s]}set(t,e,n,r,s){this.h0=0|t,this.h1=0|e,this.h2=0|n,this.h3=0|r,this.h4=0|s}process(t,e){for(let n=0;n<16;n++,e+=4)Js[n]=t.getUint32(e,!0);let n=0|this.h0,r=n,s=0|this.h1,i=s,o=0|this.h2,a=o,u=0|this.h3,c=u,f=0|this.h4,h=f;for(let t=0;t<5;t++){const e=4-t,d=Ms[t],l=Gs[t],p=zs[t],g=Ks[t],y=qs[t],m=Vs[t];for(let e=0;e<16;e++){const r=Ws(n+Ys(t,s,o,u)+Js[p[e]]+d,y[e])+f|0;n=f,f=u,u=0|Ws(o,10),o=s,s=r}for(let t=0;t<16;t++){const n=Ws(r+Ys(e,i,a,c)+Js[g[t]]+l,m[t])+h|0;r=h,h=c,c=0|Ws(a,10),a=i,i=n}}this.set(this.h1+o+c|0,this.h2+u+h|0,this.h3+f+r|0,this.h4+n+i|0,this.h0+s+a|0)}roundClean(){Js.fill(0)}destroy(){this.destroyed=!0,this.buffer.fill(0),this.set(0,0,0,0,0)}}const Xs=ms((()=>new Qs));class ti extends ys{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,hs.hash(t);const n=gs(e);if(this.iHash=t.create(),"function"!=typeof this.iHash.update)throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const r=this.blockLen,s=new Uint8Array(r);s.set(n.length>r?t.create().update(n).digest():n);for(let t=0;t<s.length;t++)s[t]^=54;this.iHash.update(s),this.oHash=t.create();for(let t=0;t<s.length;t++)s[t]^=106;this.oHash.update(s),s.fill(0)}update(t){return hs.exists(this),this.iHash.update(t),this}digestInto(t){hs.exists(this),hs.bytes(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:n,finished:r,destroyed:s,blockLen:i,outputLen:o}=this;return t.finished=r,t.destroyed=s,t.blockLen=i,t.outputLen=o,t.oHash=e._cloneInto(t.oHash),t.iHash=n._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const ei=(t,e,n)=>new ti(t,e).update(n).digest();function ni(t){const e=Or.bytes(t);return Or.raw(ks(ks(e)))}function ri(t){const e=Or.bytes(t);return Or.raw(Xs(ks(e)))}function si(t){return os.mod(t).buff}function ii(t,e=!1){const n=os.mod(t).point;return e?n.x:n.buff}function oi(t,e){const n=as.from_x(e),r=os.mod(t);return n.mul(r).buff}ei.create=(t,e)=>new ti(t,e);var ai=Object.freeze({__proto__:null,genSecretKey:function(t=32){return si(ts(t))},getPublicKey:ii,getSecretKey:si,getSharedCode:function(t,e,n="ecdh/code"){const r=function(t){const e=Or.str(t).digest;return Or.join([e,e])}(n),s=si(t),i=ii(s),o=Or.bytes(e),a=oi(s,o),u=[i.hex,o.hex];return u.sort(),function(t,e){const n=Or.bytes(t),r=Or.bytes(e);return Or.raw(ei(Hs,n,r))}(a,Or.join([r,...u]))},getSharedKey:oi,is_even_pub:function(t){const e=Or.bytes(t);switch(!0){case 32===e.length:case 33===e.length&&2===e[0]:return!0;case 33===e.length&&3===e[0]:return!1;default:throw new TypeError(`Invalid public key: ${e.hex}`)}},xonly_pub:function(t){const e=Or.bytes(t);switch(e.length){case 32:return e;case 33:return e.slice(1,33);default:throw new Error(`Invalid key length: ${e.length}`)}}});Or.random(32);const ui={secp:an,schnorr:En},ci={...ai,...es};function fi(t){const e=nt.bytes(t);return yt(e,33),ri(e)}function hi(t){return ri(Ut.fmt.toBytes(t,!1))}function di(t){return function(t){const e=Or.bytes(t);return Or.raw(ks(e))}(Ut.fmt.toBytes(t,!1))}function li(t,e="main"){const n="main"===e?["1"]:["m","n"];for(const e of n)if(t.startsWith(e))return!0;return!1}function pi(t,e="main"){const n=nt.bytes(t),r="main"===e?nt.num(0):nt.num(111);return yt(t,20),n.prepend(r).tob58chk()}const gi={check:li,encode:pi,decode:function(t,e="main"){if(!li(t,e))throw new TypeError("Invalid p2pkh address!");return nt.b58chk(t).slice(1)},hash:fi,scriptPubKey:function(t){const e=nt.bytes(t);return yt(e,20),["OP_DUP","OP_HASH160",e.hex,"OP_EQUALVERIFY","OP_CHECKSIG"]},fromPubKey:function(t,e){return pi(fi(t),e)}};function yi(t,e="main"){const n="main"===e?["3"]:["2"];for(const e of n)if(t.startsWith(e))return!0;return!1}function mi(t,e="main"){const n="main"===e?nt.num(5):nt.num(196),r=nt.bytes(t);return yt(r,20),r.prepend(n).tob58chk()}const wi={check:yi,encode:mi,decode:function(t,e="main"){if(!yi(t,e))throw new TypeError(`Invalid p2sh address for network ${e}:`+t);return nt.b58chk(t).slice(1)},hash:hi,scriptPubKey:function(t){return["OP_HASH160",nt.bytes(t).hex,"OP_EQUAL"]},fromScript:function(t,e){return mi(hi(t),e)}},bi={main:"bc",testnet:"tb",signet:"tb",regtest:"bcrt"},vi=["bc1q","tb1q","bcrt1q"];function _i(t){for(const e of vi)if(t.startsWith(e))return!0;return!1}function xi(t,e="main"){const n=bi[e],r=nt.bytes(t);return yt(r,20),r.toBech32(n,0)}const Ei={check:_i,encode:xi,decode:function(t){if(!_i(t))throw new TypeError("Invalid segwit address!");return nt.bech32(t)},hash:fi,scriptPubKey:function(t){const e=nt.bytes(t);return yt(e,20),["OP_0",e.hex]},fromPubKey:function(t,e){return xi(fi(t),e)}},Si=["bc1q","tb1q","bcrt1q"];function ki(t){for(const e of Si)if(t.startsWith(e))return!0;return!1}function Ai(t,e="main"){const n=bi[e],r=nt.bytes(t);return yt(r,32),r.toBech32(n,0)}const Oi={check:ki,encode:Ai,decode:function(t){if(!ki(t))throw new TypeError("Invalid segwit address!");return nt.bech32(t)},hash:di,scriptPubKey:function(t){const e=nt.bytes(t);return yt(e,32),["OP_0",e.hex]},fromScript:function(t,e){return Ai(di(t),e)}};function Ii(t){const e=nt.bytes(t);return e.length>32?e.slice(1,33):e}const Pi=["bc1p","tb1p","bcrt1p"];function Bi(t){for(const e of Pi)if(t.startsWith(e))return!0;return!1}function Ti(t,e="main"){const n=bi[e],r=nt.bytes(t);return yt(r,32),r.toBech32(n,1)}const Ui={check:Bi,encode:Ti,decode:function(t){if(!Bi(t))throw new TypeError("Invalid taproot address!");return nt.bech32(t)},scriptPubKey:function(t){const e=nt.bytes(t);return yt(e,32),["OP_1",e.hex]},fromPubKey:function(t,e){return Ti(Ii(t),e)}},Ci={version:2,vin:[],vout:[],locktime:0},Ni={scriptSig:[],sequence:4294967293,witness:[]},Li={value:0n,scriptPubKey:[]};function Zi(t){const e={...Ci,...t};return e.vin=e.vin.map((t=>({...Ni,...t}))),e.vout=e.vout.map((t=>({...Li,...t}))),e}function Ri(t,e){const{version:n,vin:r,vout:s,locktime:i}=Zi(t),o=!0!==e&&function(t){for(const e of t){const{witness:t}=e;if("string"==typeof t||t instanceof Uint8Array||Array.isArray(t)&&t.length>0)return!0}return!1}(r),a=[Hi(n)];o&&a.push(nt.hex("0001")),a.push(function(t){const e=[nt.varInt(t.length,"le")];for(const n of t){const{txid:t,vout:r,scriptSig:s,sequence:i}=n;e.push(ji(t)),e.push(Di(r)),e.push(At(s,!0)),e.push($i(i))}return nt.join(e)}(r)),a.push(function(t){const e=[nt.varInt(t.length,"le")];for(const n of t)e.push(Ki(n));return nt.join(e)}(s));for(const t of r)o&&a.push(Fi(t.witness));return a.push(Vi(i)),nt.join(a)}function Hi(t){return nt.num(t,4).reverse()}function ji(t){return nt.hex(t,32).reverse()}function Di(t){return nt.num(t,4).reverse()}function $i(t){if("string"==typeof t)return nt.hex(t,4).reverse();if("number"==typeof t)return nt.num(t,4).reverse();throw new Error("Unrecognized format: "+String(t))}function zi(t){if("number"==typeof t){if(t%1!=0)throw new Error("Value must be an integer:"+String(t));return nt.num(t,8).reverse()}return nt.big(t,8).reverse()}function Ki(t){const{value:e,scriptPubKey:n}=t,r=[];return r.push(zi(e)),r.push(At(n,!0)),nt.join(r)}function Fi(t=[]){const e=[];if(Array.isArray(t)){const n=nt.varInt(t.length);e.push(n);for(const n of t)e.push(qi(n));return nt.join(e)}return nt.bytes(t)}function qi(t){return function(t){if(Array.isArray(t))return 0===t.length;if("string"==typeof t&&""===t)return!0;const e=nt.bytes(t);return 1===e.length&&0===e[0]}(t)?new nt(0):At(t,!0)}function Vi(t){if("string"==typeof t)return nt.hex(t,4);if("number"==typeof t)return nt.num(t,4).reverse();throw new Error("Unrecognized format: "+String(t))}function Mi(t){"string"==typeof t&&(t=nt.hex(t).raw);const e=new gt(t),n=function(t){return t.read(4).reverse().toNum()}(e),r=function(t){const[e,n]=[...t.peek(2)];if(0===e){if(t.read(2),1===n)return!0;throw new Error(`Invalid witness flag: ${n}`)}return!1}(e),s=function(t){const e=[],n=t.readSize("le");for(let r=0;r<n;r++)e.push(Gi(t));return e}(e),i=function(t){const e=[],n=t.readSize("le");for(let r=0;r<n;r++)e.push(Wi(t));return e}(e);if(r)for(const t of s)t.witness=Yi(e);const o=function(t){return t.read(4).reverse().toNum()}(e);return{version:n,vin:s,vout:i,locktime:o}}function Gi(t){return{txid:t.read(32).reverse().toHex(),vout:t.read(4).reverse().toNum(),scriptSig:Qi(t,!0),sequence:t.read(4).reverse().toHex(),witness:[]}}function Wi(t){return{value:t.read(8).reverse().big,scriptPubKey:Qi(t,!0)}}function Yi(t){const e=[],n=t.readSize();for(let r=0;r<n;r++){const n=Ji(t,!0);e.push(n??"")}return e}function Ji(t,e){const n=!0===e?t.readSize("le"):t.size;return n>0?t.read(n).hex:null}function Qi(t,e){const n=Ji(t,e);return null!==n?n:[]}const Xi={toBytes:function(t){if(St(t))return Mi(t),nt.bytes(t);if("object"==typeof t)return Ri(t);throw new Error("Invalid format: "+String(typeof t))},toJson:function(t){if(St(t))return Mi(t);if("object"==typeof t&&!(t instanceof Uint8Array))return Ri(t),Zi(t);throw new Error("Invalid format: "+String(typeof t))}},to=[["p2pkh",/^76a914(?<hash>\w{40})88ac$/],["p2sh",/^a914(?<hash>\w{40})87$/],["p2w-pkh",/^0014(?<hash>\w{40})$/],["p2w-sh",/^0020(?<hash>\w{64})$/],["p2tr",/^5120(?<hash>\w{64})$/]],eo=[192,194,196,198,200,202,204,206,208,210,212,214,216,218,220,222,224,226,228,230,232,234,236,238,240,242,244,246,248,250,252,254,102,126,128,132,150,152,186,188,190];function no(t=[]){const e=[...t],n=function(t){let e=t.at(-1);return Et(e)&&(e=nt.hex(e)),t.length>1&&e instanceof Uint8Array&&80===e[0]?(t.pop(),nt.raw(e)):null}(e),r=function(t){let e=t.at(-1);return Et(e)&&(e=nt.hex(e)),t.length>1&&e instanceof Uint8Array&&e.length>32&&eo.includes(254&e[0])?(t.pop(),nt.raw(e)):null}(e),s=function(t){if(t.length>1){const e=t.at(-1);try{const n=Ut.fmt.toBytes(e);return t.pop(),n}catch(t){return null}}return null}(e),i=function(t){const e=[];for(const n of t){if(!(Et(n)||n instanceof Uint8Array||"number"==typeof n))throw new Error("unrecognized value: "+String(n));e.push(nt.bytes(n))}return e}(e);return{annex:n,cblock:r,script:s,params:i}}function ro(t){const e=Ut.fmt.toBytes(t,!1).hex;for(const[t,n]of to){const r=t,{groups:s}=n.exec(e)??{},{hash:i}=s??{};if(Et(i))return{type:r,data:nt.hex(i)}}return{type:"raw",data:nt.hex(e)}}const so={create:Zi,encode:Ri,decode:Mi,fmt:Xi,util:{getTxSize:function(t){const e=Xi.toJson(t),n=Ri(e,!0).length,r=Ri(e,!1).length,s=3*n+r,i=s%4>0?1:0;return{size:r,bsize:n,vsize:Math.floor(s/4)+i,weight:s}},getTxid:function(t){return ni(Ri(Xi.toJson(t),!0)).reverse().hex},readScriptPubKey:ro,readWitness:no}},io=[["1","p2pkh","main",20,"base58"],["3","p2sh","main",20,"base58"],["m","p2pkh","testnet",20,"base58"],["n","p2pkh","testnet",20,"base58"],["2","p2sh","testnet",20,"base58"],["bc1q","p2w-pkh","main",20,"bech32"],["tb1q","p2w-pkh","testnet",20,"bech32"],["bcrt1q","p2w-pkh","regtest",20,"bech32"],["bc1q","p2w-sh","main",32,"bech32"],["tb1q","p2w-sh","testnet",32,"bech32"],["bcrt1q","p2w-sh","regtest",32,"bech32"],["bc1p","p2tr","main",32,"bech32m"],["tb1p","p2tr","testnet",32,"bech32m"],["bcrt1p","p2tr","regtest",32,"bech32m"]];function oo(t,e){switch(e){case"base58":return nt.b58chk(t).slice(1);case"bech32":case"bech32m":return nt.bech32(t);default:throw new Error("Invalid address format: "+e)}}function ao(t){switch(t){case"p2pkh":return gi;case"p2sh":return wi;case"p2w-pkh":return Ei;case"p2w-sh":return Oi;case"p2tr":return Ui;default:throw new Error("Invalid address type: "+t)}}function uo(t){const[e,n,r]=function(t){for(const e of io){const[n,r,s,i,o]=e;if(t.startsWith(n)&&oo(t,o).length===i)return e}throw new Error("Invalid address: "+t)}(t),s=ao(n),i=s.decode(t,r);return{prefix:e,type:n,network:r,data:i,script:s.scriptPubKey(i)}}const co={p2pkh:gi,p2sh:wi,p2wpkh:Ei,p2wsh:Oi,p2tr:Ui,decode:uo,fromScriptPubKey:function(t,e){const{type:n,data:r}=so.util.readScriptPubKey(t);return ao(n).encode(r,e)},toScriptPubKey:function(t){const{script:e}=uo(t);return Ut.fmt.toAsm(e,!1)}},fo=[1,2,3];function ho(t,e,n={}){const{sigflag:r=1}=n,s=128==(128&r),i=r%128;if(!fo.includes(i))throw new Error("Invalid hash type: "+String(r));const o=so.fmt.toJson(t),{version:a,vin:u,vout:c,locktime:f}=o,{txid:h,vout:d,prevout:l,sequence:p}=u[e],{value:g}=l??{};if(void 0===g)throw new Error("Prevout value is empty!");let y=n.script;if(void 0===y&&void 0!==n.pubkey){y=`76a914${ri(n.pubkey).hex}88ac`}if(void 0===y)throw new Error("No pubkey / script has been set!");if(Ut.fmt.toAsm(y).includes("OP_CODESEPARATOR"))throw new Error("This library does not currently support the use of OP_CODESEPARATOR in segwit scripts.");const m=[Hi(a),lo(u,s),po(u,i,s),ji(h),Di(d),Ut.encode(y,!0),zi(g),$i(p),go(c,e,i),Vi(f),nt.num(r,4).reverse()];return ni(nt.join(m))}function lo(t,e){if(!0===e)return nt.num(0,32);const n=[];for(const{txid:e,vout:r}of t)n.push(ji(e)),n.push(Di(r));return ni(nt.join(n))}function po(t,e,n){if(n||1!==e)return nt.num(0,32);const r=[];for(const{sequence:e}of t)r.push($i(e));return ni(nt.join(r))}function go(t,e,n){const r=[];if(1===n){for(const{value:e,scriptPubKey:n}of t)r.push(zi(e)),r.push(Ut.encode(n,!0));return ni(nt.join(r))}if(3===n&&e<t.length){const{value:n,scriptPubKey:s}=t[e];return r.push(zi(n)),r.push(Ut.encode(s,!0)),ni(nt.join(r))}return nt.num(0,32)}const yo={hash:ho,sign:function(t,e,n,r={}){const{sigflag:s=1}=r,i=ho(e,n,r),o=ui.secp.sign(i,t).toDERRawBytes(!0);return nt.join([o,s])},verify:function(t,e,n={}){const r=so.fmt.toJson(t),{throws:s=!1}=n,{witness:i=[]}=r.vin[e],o=so.util.readWitness(i),{script:a,params:u}=o;let c=null;if(u.length<1)return mt("Invalid witness data: "+String(i),s);if(void 0===n.script&&null!==a&&(n.script=a),void 0!==n.pubkey)c=nt.bytes(n.pubkey);else{if(!(u.length>1&&33===u[1].length))return mt("No pubkey provided!",s);c=nt.bytes(u[1])}const f=Ut.fmt.toParam(u[0]),h=f.slice(0,-1),d=f.slice(-1)[0],l=ho(r,e,{...n,sigflag:d});return!!ui.secp.verify(h,l,c)||mt("Invalid signature!",s)}},mo=[0,1,2,3,129,130,131];function wo(t,e,n={}){const{extension:r,sigflag:s=0,extflag:i=0,key_version:o=0,separator_pos:a=4294967295}=n,u=so.fmt.toJson(t),{version:c,vin:f,vout:h,locktime:d}=u;if(e>=f.length)throw new Error("Index out of bounds: "+String(e));if(!mo.includes(s))throw new Error("Invalid hash type: "+String(s));if(i<0||i>127)throw new Error("Extention flag out of range: "+String(i));const{txid:l,vout:p,sequence:g,witness:y=[]}=f[e],m=128==(128&s),w=function(t){if(void 0===t)return;if(t.length<2)return;let e=t.at(-1);"string"==typeof e&&(e=nt.hex(e));if(e instanceof Uint8Array&&80===e[0])return nt.raw(e).prefixSize("be").digest;return}(y),b=2*(i+(void 0!==r?1:0))+(void 0!==w?1:0),v=nt.str("TapSighash").digest,_=[v,v,nt.num(0,1),nt.num(s,1),Hi(c),Vi(d)];if(!m){const t=f.map((t=>bo(t)));_.push(function(t){const e=[];for(const{txid:n,vout:r}of t)e.push(ji(n)),e.push(Di(r));return nt.join(e).digest}(f),function(t){const e=[];for(const{value:n}of t)e.push(zi(n));return nt.join(e).digest}(t),function(t){const e=[];for(const{scriptPubKey:n}of t)e.push(At(n,!0));return nt.join(e).digest}(t),function(t){const e=[];for(const{sequence:n}of t)e.push($i(n));return nt.join(e).digest}(f))}if(((3&s)<2||(3&s)>3)&&_.push(function(t){const e=[];for(const{value:n,scriptPubKey:r}of t)e.push(zi(n)),e.push(Ut.encode(r,!0));return nt.join(e).digest}(h)),_.push(nt.num(b,1)),m){const{value:t,scriptPubKey:n}=bo(f[e]);_.push(ji(l),Di(p),zi(t),Ut.encode(n,!0),$i(g))}else _.push(nt.num(e,4).reverse());return void 0!==w&&_.push(w),3==(3&s)&&_.push(function(t){return nt.join([zi(t.value),Ut.encode(t.scriptPubKey,!0)]).digest}(h[e])),void 0!==r&&_.push(nt.bytes(r),nt.num(o),nt.num(a,4)),nt.join(_).digest}function bo(t){if(void 0===t.prevout)throw new Error("Prevout data missing for input: "+String(t.txid));return t.prevout}const vo=0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2fn,_o=0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141n;const xo=192;function Eo(t){const e=nt.str(t).digest;return nt.join([e,e])}function So(t,e=xo){return nt.join([Eo("TapLeaf"),Oo(e),nt.bytes(t)]).digest.hex}function ko(t,e){return e<t&&([t,e]=[e,t]),nt.join([Eo("TapBranch"),nt.hex(t).raw,nt.hex(e).raw]).digest.hex}function Ao(t,e,n=[]){const r=[],s=[];if(t.length<1)throw new Error("Tree is empty!");for(let s=0;s<t.length;s++){const i=t[s];if(Array.isArray(i)){const[t,s,o]=Ao(i,e);e=s,r.push(t);for(const t of o)n.push(t)}else r.push(i)}if(1===r.length)return[r[0],e,n];r.sort(),r.length%2!=0&&r.push(r[r.length-1]);for(let t=0;t<r.length-1;t+=2){const i=ko(r[t],r[t+1]);s.push(i),"string"==typeof e&&(e===r[t]?(n.push(r[t+1]),e=i):e===r[t+1]&&(n.push(r[t]),e=i))}return Ao(s,e,n)}function Oo(t=192){return 254&t}function Io(t,e=new Uint8Array,n=!1){const r=n?new os(t).point.x.raw:Ii(t);return nt.join([Eo("TapTweak"),r,nt.bytes(e)]).digest}function Po(t,e,n=!1){void 0===e&&(e=new Uint8Array);const r=nt.bytes(t),s=Io(t,e,n);return n?Bo(r,s):To(r,s)}function Bo(t,e){let n=new os(t);return n.point.hasOddY&&(n=n.negate()),nt.raw(n.add(e).raw)}function To(t,e){t=Ii(t);const n=as.from_x(t).add(e);return nt.raw(n.raw)}const Uo=function(){const t=nt.hex("0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8");return as.from_x(t.digest).x}(),Co=192;function No(t,e={}){const{isPrivate:n=!1,tree:r=[],version:s=Co}=e,i=n?ci.getPublicKey(t,!0):Ii(t);let{target:o}=e;void 0!==o&&(o=nt.bytes(o).hex);let a,u=[];if(r.length>0){const[e,s,i]=Ao(r,o);u=i,a=Po(t,e,n)}else a=Po(t,void 0!==o?o:void 0,n);const c=n?ci.getPublicKey(a)[0]:a[0],f=[nt.num(s+Ro(c)),i];u.length>0&&u.forEach((t=>f.push(nt.hex(t))));const h=nt.join(f);if(void 0!==o&&!Lo(a,o,h,e))throw new Error("Path checking failed! Unable to generate path.");return[Ii(a).hex,h.hex]}function Lo(t,e,n,r={}){const{isPrivate:s=!1,throws:i=!1}=r,{parity:o,paths:a,intkey:u}=Zo(n),c=s?ci.getPublicKey(t,!0):Ii(t),f=nt.join([o,c]);if(33!==f.length)return mt("Invalid tapkey: "+f.hex,i);let h=nt.bytes(e).hex;for(const t of a)h=ko(h,t);const d=Po(u,h);return nt.raw(d).hex===nt.raw(f).hex}function Zo(t){const e=new gt(nt.bytes(t)),n=e.read(1).num,r=e.read(32),[s,i]=n%2==0?[n,2]:[n-1,3],o=[];for(;e.size>=32;)o.push(e.read(32).hex);if(0!==e.size)throw new Error("Non-empty buffer on control block: "+String(e));return{intkey:r,paths:o,parity:i,version:s}}function Ro(t=2){if(0===t||1===t)return t;if(2===t||"02"===t)return 0;if(3===t||"03"===t)return 1;throw new Error("Invalid parity bit: "+String(t))}const Ho={hash:wo,sign:function(t,e,n,r={}){const{sigflag:s=0}=r,i=function(t,e,n=nt.random(32)){const r=nt.bytes(e),s=new os(t),i=s.point,o=i.hasEvenY?s.big:s.negated.big,a=wt("BIP0340/aux",nt.bytes(n)),u=wt("BIP0340/nonce",o^a.big,i.x.raw,r),c=new os(u),f=c.point,h=f.hasEvenY?c.big:c.negated.big,d=new os(wt("BIP0340/challenge",f.x.raw,i.x.raw,r)),l=new os(h+d.big*o);return nt.join([f.x.raw,l.raw])}(t,wo(e,n,r));return 0===s?nt.raw(i):nt.join([i,s])},verify:function(t,e,n={}){const r=so.fmt.toJson(t),{throws:s=!1}=n,{prevout:i,witness:o=[]}=r.vin[e],a=so.util.readWitness(o),{cblock:u,script:c,params:f}=a;let h;if(f.length<1)return mt("Invalid witness data: "+String(o),s);const{scriptPubKey:d}=i??{};if(void 0===d)return mt("Prevout scriptPubKey is empty!",s);const{type:l,data:p}=so.util.readScriptPubKey(d);if("p2tr"!==l)return mt("Prevout script is not a valid taproot output:"+p.hex,s);if(32!==p.length)return mt("Invalid tapkey length: "+String(p.length),s);if(null!==u&&null!==c){const t=So(c,254&u[0]);if(n.extension=t,!Lo(p,t,u,{throws:s}))return mt("cblock verification failed!",s)}h=void 0!==n.pubkey?nt.bytes(n.pubkey):f.length>1&&32===f[1].length?nt.bytes(f[1]):nt.bytes(p);const g=Ut.fmt.toParam(f[0]),y=new gt(g),m=y.read(64).raw;return 1===y.size&&(n.sigflag=y.read(1).num,0===n.sigflag)?mt("0x00 is not a valid appended sigflag!",s):!!function(t,e,n,r=!1){const s=as.from_x(Ii(n)),i=nt.bytes(e),o=nt.bytes(t).stream;o.size<64&&mt("Signature length is too small: "+String(o.size),r);const a=o.read(32);a.big>vo&&mt("Signature r value greater than field size!",r);const u=o.read(32);u.big>_o&&mt("Signature s value greater than curve order!",r);const c=new os(wt("BIP0340/challenge",a.raw,s.x.raw,i)),f=new os(u).point,h=s.mul(c.big),d=f.sub(h);return d.hasOddY&&mt("Signature R value has odd Y coordinate!",r),0n===d.x.big&&mt("Signature R value is infinite!",r),d.x.big===a.big}(m,wo(r,e,n),h,s)||mt("Invalid signature!",s)}},jo={segwit:yo,taproot:Ho},Do={getTag:Eo,getLeaf:So,getBranch:ko,getRoot:function(t){return nt.hex(Ao(t)[0])}},$o={getPubKey:function(t,e={}){return No(t,{...e,isPrivate:!1})},getSecKey:function(t,e={}){return No(t,{...e,isPrivate:!0})},encodeScript:function(t,e){return So(Ut.fmt.toBytes(t),e)},checkPath:Lo,tree:Do,tweak:{getPubKey:function(t,e){return Po(t,e)},getSecKey:function(t,e){return Po(t,e,!0)},getTweak:Io,tweakSecKey:Bo,tweakPubKey:To},util:{readCtrlBlock:Zo,readParityBit:Ro},SCRIPT_PUBKEY:Uo};class zo{constructor(t){this._buff=nt.raw(At(t))}get raw(){return this._buff.raw}get hex(){return this._buff.hex}get asm(){return Bt(this._buff)}getHash(t,e){switch(t){case"p2w":return ni(this._buff).hex;case"p2sh":return ri(this._buff).hex;case"p2tr":return Do.getLeaf(this._buff,e);default:throw new Error("Unrecognized format: "+t)}}toJSON(){return this.asm??[]}}const Ko=4294967295,Fo=512;class qo{constructor(t){this.value="string"==typeof t?parseInt(t,16):t}get isReplaceable(){return this.value<Ko}get isLocked(){return!(this.value!==Ko||0!=(-2147483648&this.value))}get isTimelock(){return 0!=(4194304&this.value)}get timestamp(){return this.isLocked?this.isTimelock?this.value*Fo:this.value*Fo*600:0}set timestamp(t){this.value=Math.ceil(t/Fo)}get blockheight(){return this.isLocked?this.isTimelock?Math.ceil(this.value*Fo/600):this.value:0}set blockheight(t){this.value=t}get estDate(){return this.isTimelock?new Date(Date.now()+this.value*Fo*1e3):new Date(Date.now()+600*this.value*1e3)}set estDate(t){const e=t.getTime()-Date.now();this.value=e>512e3?Math.ceil(e/1e3/Fo):1}toJSON(){return this.value}}let Vo=class{constructor(t){this.value=BigInt(t.value),this.scriptPubKey=new zo(t.scriptPubKey)}get type(){const{type:t}=ro(this.scriptPubKey.raw);return t}};class Mo{constructor(t,e){this._data=t,this._meta=no(t),this.format=e}get length(){return this._data.length}get annex(){const t=this._meta.annex;return null!==t?nt.raw(t).hex:void 0}get cblock(){const t=this._meta.cblock;return null!==t?nt.raw(t).hex:void 0}get script(){const t=this._meta.script;return null!==t?Ut.decode(t):void 0}get params(){return this._meta.params}toJSON(){return this._data}}let Go=class{constructor(t,e){this._tx=t,this.idx=e}get data(){return this._tx.vin[this.idx]}get txid(){return this.data.txid}get vout(){return this.data.vout}get prevout(){return void 0!==this.data.prevout?new Vo(this.data.prevout):void 0}get scriptSig(){return new zo(this.data.scriptSig)}get sequence(){return new qo(this.data.sequence)}get witness(){return new Mo(this.data.witness)}get type(){if(void 0!==this.prevout){const t=this.prevout.scriptPubKey.raw,{type:e}=ro(t);if("p2sh"===e){const t=this.scriptSig.asm;if("OP_0"===t[0]){if(20===t[1].length)return"p2w-p2pkh";if(32===t[1].length)return"p2w-p2sh"}return"p2sh"}return e}return"raw"}sign(t,e){if(this.type.startsWith("p2w"))return jo.segwit.sign(t,this._tx,this.idx,e);if(this.type.startsWith("p2tr"))return jo.taproot.sign(t,this._tx,this.idx,e);if(this.type.startsWith("p2pkh")||this.type.startsWith("p2sh"))throw new Error("This library does not support signing legacy transactions.");throw new Error("Unable to sign this input type:"+String(this.type))}};class Wo{constructor(t=0){this.value=nt.bytes(t).num}get isTimelock(){return this.value>5e8}get timestamp(){return this.isTimelock?this.value:600*this.value}set timestamp(t){this.value=t}get blockheight(){return this.isTimelock?Math.floor(this.value/600):this.value}set blockheight(t){this.value=t}get estDate(){return this.isTimelock?new Date(Date.now()+1e3*this.value):new Date(Date.now()+600*this.value*1e3)}set estDate(t){this.value=Math.floor((t.getTime()-Date.now())/1e3)}toJSON(){return this.value}}var Yo,Jo;!function(t){t.assertEqual=t=>t,t.assertIs=function(t){},t.assertNever=function(t){throw new Error},t.arrayToEnum=t=>{const e={};for(const n of t)e[n]=n;return e},t.getValidEnumValues=e=>{const n=t.objectKeys(e).filter((t=>"number"!=typeof e[e[t]])),r={};for(const t of n)r[t]=e[t];return t.objectValues(r)},t.objectValues=e=>t.objectKeys(e).map((function(t){return e[t]})),t.objectKeys="function"==typeof Object.keys?t=>Object.keys(t):t=>{const e=[];for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.push(n);return e},t.find=(t,e)=>{for(const n of t)if(e(n))return n},t.isInteger="function"==typeof Number.isInteger?t=>Number.isInteger(t):t=>"number"==typeof t&&isFinite(t)&&Math.floor(t)===t,t.joinValues=function(t,e=" | "){return t.map((t=>"string"==typeof t?`'${t}'`:t)).join(e)},t.jsonStringifyReplacer=(t,e)=>"bigint"==typeof e?e.toString():e}(Yo||(Yo={})),function(t){t.mergeShapes=(t,e)=>({...t,...e})}(Jo||(Jo={}));const Qo=Yo.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Xo=t=>{switch(typeof t){case"undefined":return Qo.undefined;case"string":return Qo.string;case"number":return isNaN(t)?Qo.nan:Qo.number;case"boolean":return Qo.boolean;case"function":return Qo.function;case"bigint":return Qo.bigint;case"symbol":return Qo.symbol;case"object":return Array.isArray(t)?Qo.array:null===t?Qo.null:t.then&&"function"==typeof t.then&&t.catch&&"function"==typeof t.catch?Qo.promise:"undefined"!=typeof Map&&t instanceof Map?Qo.map:"undefined"!=typeof Set&&t instanceof Set?Qo.set:"undefined"!=typeof Date&&t instanceof Date?Qo.date:Qo.object;default:return Qo.unknown}},ta=Yo.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class ea extends Error{constructor(t){super(),this.issues=[],this.addIssue=t=>{this.issues=[...this.issues,t]},this.addIssues=(t=[])=>{this.issues=[...this.issues,...t]};const e=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,e):this.__proto__=e,this.name="ZodError",this.issues=t}get errors(){return this.issues}format(t){const e=t||function(t){return t.message},n={_errors:[]},r=t=>{for(const s of t.issues)if("invalid_union"===s.code)s.unionErrors.map(r);else if("invalid_return_type"===s.code)r(s.returnTypeError);else if("invalid_arguments"===s.code)r(s.argumentsError);else if(0===s.path.length)n._errors.push(e(s));else{let t=n,r=0;for(;r<s.path.length;){const n=s.path[r];r===s.path.length-1?(t[n]=t[n]||{_errors:[]},t[n]._errors.push(e(s))):t[n]=t[n]||{_errors:[]},t=t[n],r++}}};return r(this),n}toString(){return this.message}get message(){return JSON.stringify(this.issues,Yo.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(t=(t=>t.message)){const e={},n=[];for(const r of this.issues)r.path.length>0?(e[r.path[0]]=e[r.path[0]]||[],e[r.path[0]].push(t(r))):n.push(t(r));return{formErrors:n,fieldErrors:e}}get formErrors(){return this.flatten()}}ea.create=t=>new ea(t);const na=(t,e)=>{let n;switch(t.code){case ta.invalid_type:n=t.received===Qo.undefined?"Required":`Expected ${t.expected}, received ${t.received}`;break;case ta.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(t.expected,Yo.jsonStringifyReplacer)}`;break;case ta.unrecognized_keys:n=`Unrecognized key(s) in object: ${Yo.joinValues(t.keys,", ")}`;break;case ta.invalid_union:n="Invalid input";break;case ta.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${Yo.joinValues(t.options)}`;break;case ta.invalid_enum_value:n=`Invalid enum value. Expected ${Yo.joinValues(t.options)}, received '${t.received}'`;break;case ta.invalid_arguments:n="Invalid function arguments";break;case ta.invalid_return_type:n="Invalid function return type";break;case ta.invalid_date:n="Invalid date";break;case ta.invalid_string:"object"==typeof t.validation?"includes"in t.validation?(n=`Invalid input: must include "${t.validation.includes}"`,"number"==typeof t.validation.position&&(n=`${n} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?n=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?n=`Invalid input: must end with "${t.validation.endsWith}"`:Yo.assertNever(t.validation):n="regex"!==t.validation?`Invalid ${t.validation}`:"Invalid";break;case ta.too_small:n="array"===t.type?`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:"string"===t.type?`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:"number"===t.type?`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:"date"===t.type?`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:"Invalid input";break;case ta.too_big:n="array"===t.type?`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:"string"===t.type?`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:"number"===t.type?`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:"bigint"===t.type?`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:"date"===t.type?`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:"Invalid input";break;case ta.custom:n="Invalid input";break;case ta.invalid_intersection_types:n="Intersection results could not be merged";break;case ta.not_multiple_of:n=`Number must be a multiple of ${t.multipleOf}`;break;case ta.not_finite:n="Number must be finite";break;default:n=e.defaultError,Yo.assertNever(t)}return{message:n}};let ra=na;function sa(){return ra}const ia=t=>{const{data:e,path:n,errorMaps:r,issueData:s}=t,i=[...n,...s.path||[]],o={...s,path:i};let a="";const u=r.filter((t=>!!t)).slice().reverse();for(const t of u)a=t(o,{data:e,defaultError:a}).message;return{...s,path:i,message:s.message||a}};function oa(t,e){const n=ia({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,sa(),na].filter((t=>!!t))});t.common.issues.push(n)}class aa{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(t,e){const n=[];for(const r of e){if("aborted"===r.status)return ua;"dirty"===r.status&&t.dirty(),n.push(r.value)}return{status:t.value,value:n}}static async mergeObjectAsync(t,e){const n=[];for(const t of e)n.push({key:await t.key,value:await t.value});return aa.mergeObjectSync(t,n)}static mergeObjectSync(t,e){const n={};for(const r of e){const{key:e,value:s}=r;if("aborted"===e.status)return ua;if("aborted"===s.status)return ua;"dirty"===e.status&&t.dirty(),"dirty"===s.status&&t.dirty(),(void 0!==s.value||r.alwaysSet)&&(n[e.value]=s.value)}return{status:t.value,value:n}}}const ua=Object.freeze({status:"aborted"}),ca=t=>({status:"dirty",value:t}),fa=t=>({status:"valid",value:t}),ha=t=>"aborted"===t.status,da=t=>"dirty"===t.status,la=t=>"valid"===t.status,pa=t=>"undefined"!=typeof Promise&&t instanceof Promise;var ga;!function(t){t.errToObj=t=>"string"==typeof t?{message:t}:t||{},t.toString=t=>"string"==typeof t?t:null==t?void 0:t.message}(ga||(ga={}));class ya{constructor(t,e,n,r){this._cachedPath=[],this.parent=t,this.data=e,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ma=(t,e)=>{if(la(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const e=new ea(t.common.issues);return this._error=e,this._error}}};function wa(t){if(!t)return{};const{errorMap:e,invalid_type_error:n,required_error:r,description:s}=t;if(e&&(n||r))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(e)return{errorMap:e,description:s};return{errorMap:(t,e)=>"invalid_type"!==t.code?{message:e.defaultError}:void 0===e.data?{message:null!=r?r:e.defaultError}:{message:null!=n?n:e.defaultError},description:s}}class ba{constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(t){return Xo(t.data)}_getOrReturnCtx(t,e){return e||{common:t.parent.common,data:t.data,parsedType:Xo(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new aa,ctx:{common:t.parent.common,data:t.data,parsedType:Xo(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const e=this._parse(t);if(pa(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(t){const e=this._parse(t);return Promise.resolve(e)}parse(t,e){const n=this.safeParse(t,e);if(n.success)return n.data;throw n.error}safeParse(t,e){var n;const r={common:{issues:[],async:null!==(n=null==e?void 0:e.async)&&void 0!==n&&n,contextualErrorMap:null==e?void 0:e.errorMap},path:(null==e?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xo(t)},s=this._parseSync({data:t,path:r.path,parent:r});return ma(r,s)}async parseAsync(t,e){const n=await this.safeParseAsync(t,e);if(n.success)return n.data;throw n.error}async safeParseAsync(t,e){const n={common:{issues:[],contextualErrorMap:null==e?void 0:e.errorMap,async:!0},path:(null==e?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Xo(t)},r=this._parse({data:t,path:n.path,parent:n}),s=await(pa(r)?r:Promise.resolve(r));return ma(n,s)}refine(t,e){const n=t=>"string"==typeof e||void 0===e?{message:e}:"function"==typeof e?e(t):e;return this._refinement(((e,r)=>{const s=t(e),i=()=>r.addIssue({code:ta.custom,...n(e)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then((t=>!!t||(i(),!1))):!!s||(i(),!1)}))}refinement(t,e){return this._refinement(((n,r)=>!!t(n)||(r.addIssue("function"==typeof e?e(n,r):e),!1)))}_refinement(t){return new ou({schema:this,typeName:mu.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}optional(){return au.create(this,this._def)}nullable(){return uu.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return $a.create(this,this._def)}promise(){return iu.create(this,this._def)}or(t){return Fa.create([this,t],this._def)}and(t){return Ga.create(this,t,this._def)}transform(t){return new ou({...wa(this._def),schema:this,typeName:mu.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const e="function"==typeof t?t:()=>t;return new cu({...wa(this._def),innerType:this,defaultValue:e,typeName:mu.ZodDefault})}brand(){return new lu({typeName:mu.ZodBranded,type:this,...wa(this._def)})}catch(t){const e="function"==typeof t?t:()=>t;return new fu({...wa(this._def),innerType:this,catchValue:e,typeName:mu.ZodCatch})}describe(t){return new(0,this.constructor)({...this._def,description:t})}pipe(t){return pu.create(this,t)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const va=/^c[^\s-]{8,}$/i,_a=/^[a-z][a-z0-9]*$/,xa=/[0-9A-HJKMNP-TV-Z]{26}/,Ea=/^([a-f0-9]{8}-[a-f0-9]{4}-[1-5][a-f0-9]{3}-[a-f0-9]{4}-[a-f0-9]{12}|00000000-0000-0000-0000-000000000000)$/i,Sa=/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\])|(\[IPv6:(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))\])|([A-Za-z0-9]([A-Za-z0-9-]*[A-Za-z0-9])*(\.[A-Za-z]{2,})+))$/,ka=/^(\p{Extended_Pictographic}|\p{Emoji_Component})+$/u,Aa=/^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/,Oa=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;class Ia extends ba{constructor(){super(...arguments),this._regex=(t,e,n)=>this.refinement((e=>t.test(e)),{validation:e,code:ta.invalid_string,...ga.errToObj(n)}),this.nonempty=t=>this.min(1,ga.errToObj(t)),this.trim=()=>new Ia({...this._def,checks:[...this._def.checks,{kind:"trim"}]}),this.toLowerCase=()=>new Ia({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]}),this.toUpperCase=()=>new Ia({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}_parse(t){this._def.coerce&&(t.data=String(t.data));if(this._getType(t)!==Qo.string){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.string,received:e.parsedType}),ua}const e=new aa;let n;for(const o of this._def.checks)if("min"===o.kind)t.data.length<o.value&&(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),e.dirty());else if("max"===o.kind)t.data.length>o.value&&(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),e.dirty());else if("length"===o.kind){const r=t.data.length>o.value,s=t.data.length<o.value;(r||s)&&(n=this._getOrReturnCtx(t,n),r?oa(n,{code:ta.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}):s&&oa(n,{code:ta.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}),e.dirty())}else if("email"===o.kind)Sa.test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{validation:"email",code:ta.invalid_string,message:o.message}),e.dirty());else if("emoji"===o.kind)ka.test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{validation:"emoji",code:ta.invalid_string,message:o.message}),e.dirty());else if("uuid"===o.kind)Ea.test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{validation:"uuid",code:ta.invalid_string,message:o.message}),e.dirty());else if("cuid"===o.kind)va.test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{validation:"cuid",code:ta.invalid_string,message:o.message}),e.dirty());else if("cuid2"===o.kind)_a.test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{validation:"cuid2",code:ta.invalid_string,message:o.message}),e.dirty());else if("ulid"===o.kind)xa.test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{validation:"ulid",code:ta.invalid_string,message:o.message}),e.dirty());else if("url"===o.kind)try{new URL(t.data)}catch(r){n=this._getOrReturnCtx(t,n),oa(n,{validation:"url",code:ta.invalid_string,message:o.message}),e.dirty()}else if("regex"===o.kind){o.regex.lastIndex=0;o.regex.test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{validation:"regex",code:ta.invalid_string,message:o.message}),e.dirty())}else if("trim"===o.kind)t.data=t.data.trim();else if("includes"===o.kind)t.data.includes(o.value,o.position)||(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.invalid_string,validation:{includes:o.value,position:o.position},message:o.message}),e.dirty());else if("toLowerCase"===o.kind)t.data=t.data.toLowerCase();else if("toUpperCase"===o.kind)t.data=t.data.toUpperCase();else if("startsWith"===o.kind)t.data.startsWith(o.value)||(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.invalid_string,validation:{startsWith:o.value},message:o.message}),e.dirty());else if("endsWith"===o.kind)t.data.endsWith(o.value)||(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.invalid_string,validation:{endsWith:o.value},message:o.message}),e.dirty());else if("datetime"===o.kind){((i=o).precision?i.offset?new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${i.precision}}(([+-]\\d{2}(:?\\d{2})?)|Z)$`):new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${i.precision}}Z$`):0===i.precision?i.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$"):i.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$")).test(t.data)||(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.invalid_string,validation:"datetime",message:o.message}),e.dirty())}else"ip"===o.kind?(r=t.data,("v4"!==(s=o.version)&&s||!Aa.test(r))&&("v6"!==s&&s||!Oa.test(r))&&(n=this._getOrReturnCtx(t,n),oa(n,{validation:"ip",code:ta.invalid_string,message:o.message}),e.dirty())):Yo.assertNever(o);var r,s,i;return{status:e.value,value:t.data}}_addCheck(t){return new Ia({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...ga.errToObj(t)})}url(t){return this._addCheck({kind:"url",...ga.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...ga.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...ga.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...ga.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...ga.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...ga.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...ga.errToObj(t)})}datetime(t){var e;return"string"==typeof t?this._addCheck({kind:"datetime",precision:null,offset:!1,message:t}):this._addCheck({kind:"datetime",precision:void 0===(null==t?void 0:t.precision)?null:null==t?void 0:t.precision,offset:null!==(e=null==t?void 0:t.offset)&&void 0!==e&&e,...ga.errToObj(null==t?void 0:t.message)})}regex(t,e){return this._addCheck({kind:"regex",regex:t,...ga.errToObj(e)})}includes(t,e){return this._addCheck({kind:"includes",value:t,position:null==e?void 0:e.position,...ga.errToObj(null==e?void 0:e.message)})}startsWith(t,e){return this._addCheck({kind:"startsWith",value:t,...ga.errToObj(e)})}endsWith(t,e){return this._addCheck({kind:"endsWith",value:t,...ga.errToObj(e)})}min(t,e){return this._addCheck({kind:"min",value:t,...ga.errToObj(e)})}max(t,e){return this._addCheck({kind:"max",value:t,...ga.errToObj(e)})}length(t,e){return this._addCheck({kind:"length",value:t,...ga.errToObj(e)})}get isDatetime(){return!!this._def.checks.find((t=>"datetime"===t.kind))}get isEmail(){return!!this._def.checks.find((t=>"email"===t.kind))}get isURL(){return!!this._def.checks.find((t=>"url"===t.kind))}get isEmoji(){return!!this._def.checks.find((t=>"emoji"===t.kind))}get isUUID(){return!!this._def.checks.find((t=>"uuid"===t.kind))}get isCUID(){return!!this._def.checks.find((t=>"cuid"===t.kind))}get isCUID2(){return!!this._def.checks.find((t=>"cuid2"===t.kind))}get isULID(){return!!this._def.checks.find((t=>"ulid"===t.kind))}get isIP(){return!!this._def.checks.find((t=>"ip"===t.kind))}get minLength(){let t=null;for(const e of this._def.checks)"min"===e.kind&&(null===t||e.value>t)&&(t=e.value);return t}get maxLength(){let t=null;for(const e of this._def.checks)"max"===e.kind&&(null===t||e.value<t)&&(t=e.value);return t}}function Pa(t,e){const n=(t.toString().split(".")[1]||"").length,r=(e.toString().split(".")[1]||"").length,s=n>r?n:r;return parseInt(t.toFixed(s).replace(".",""))%parseInt(e.toFixed(s).replace(".",""))/Math.pow(10,s)}Ia.create=t=>{var e;return new Ia({checks:[],typeName:mu.ZodString,coerce:null!==(e=null==t?void 0:t.coerce)&&void 0!==e&&e,...wa(t)})};class Ba extends ba{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){this._def.coerce&&(t.data=Number(t.data));if(this._getType(t)!==Qo.number){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.number,received:e.parsedType}),ua}let e;const n=new aa;for(const r of this._def.checks)if("int"===r.kind)Yo.isInteger(t.data)||(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty());else if("min"===r.kind){(r.inclusive?t.data<r.value:t.data<=r.value)&&(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty())}else if("max"===r.kind){(r.inclusive?t.data>r.value:t.data>=r.value)&&(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty())}else"multipleOf"===r.kind?0!==Pa(t.data,r.value)&&(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):"finite"===r.kind?Number.isFinite(t.data)||(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.not_finite,message:r.message}),n.dirty()):Yo.assertNever(r);return{status:n.value,value:t.data}}gte(t,e){return this.setLimit("min",t,!0,ga.toString(e))}gt(t,e){return this.setLimit("min",t,!1,ga.toString(e))}lte(t,e){return this.setLimit("max",t,!0,ga.toString(e))}lt(t,e){return this.setLimit("max",t,!1,ga.toString(e))}setLimit(t,e,n,r){return new Ba({...this._def,checks:[...this._def.checks,{kind:t,value:e,inclusive:n,message:ga.toString(r)}]})}_addCheck(t){return new Ba({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:ga.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:ga.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:ga.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:ga.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:ga.toString(t)})}multipleOf(t,e){return this._addCheck({kind:"multipleOf",value:t,message:ga.toString(e)})}finite(t){return this._addCheck({kind:"finite",message:ga.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:ga.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:ga.toString(t)})}get minValue(){let t=null;for(const e of this._def.checks)"min"===e.kind&&(null===t||e.value>t)&&(t=e.value);return t}get maxValue(){let t=null;for(const e of this._def.checks)"max"===e.kind&&(null===t||e.value<t)&&(t=e.value);return t}get isInt(){return!!this._def.checks.find((t=>"int"===t.kind||"multipleOf"===t.kind&&Yo.isInteger(t.value)))}get isFinite(){let t=null,e=null;for(const n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===e||n.value>e)&&(e=n.value):"max"===n.kind&&(null===t||n.value<t)&&(t=n.value)}return Number.isFinite(e)&&Number.isFinite(t)}}Ba.create=t=>new Ba({checks:[],typeName:mu.ZodNumber,coerce:(null==t?void 0:t.coerce)||!1,...wa(t)});class Ta extends ba{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){this._def.coerce&&(t.data=BigInt(t.data));if(this._getType(t)!==Qo.bigint){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.bigint,received:e.parsedType}),ua}let e;const n=new aa;for(const r of this._def.checks)if("min"===r.kind){(r.inclusive?t.data<r.value:t.data<=r.value)&&(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty())}else if("max"===r.kind){(r.inclusive?t.data>r.value:t.data>=r.value)&&(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty())}else"multipleOf"===r.kind?t.data%r.value!==BigInt(0)&&(e=this._getOrReturnCtx(t,e),oa(e,{code:ta.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):Yo.assertNever(r);return{status:n.value,value:t.data}}gte(t,e){return this.setLimit("min",t,!0,ga.toString(e))}gt(t,e){return this.setLimit("min",t,!1,ga.toString(e))}lte(t,e){return this.setLimit("max",t,!0,ga.toString(e))}lt(t,e){return this.setLimit("max",t,!1,ga.toString(e))}setLimit(t,e,n,r){return new Ta({...this._def,checks:[...this._def.checks,{kind:t,value:e,inclusive:n,message:ga.toString(r)}]})}_addCheck(t){return new Ta({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:ga.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:ga.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:ga.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:ga.toString(t)})}multipleOf(t,e){return this._addCheck({kind:"multipleOf",value:t,message:ga.toString(e)})}get minValue(){let t=null;for(const e of this._def.checks)"min"===e.kind&&(null===t||e.value>t)&&(t=e.value);return t}get maxValue(){let t=null;for(const e of this._def.checks)"max"===e.kind&&(null===t||e.value<t)&&(t=e.value);return t}}Ta.create=t=>{var e;return new Ta({checks:[],typeName:mu.ZodBigInt,coerce:null!==(e=null==t?void 0:t.coerce)&&void 0!==e&&e,...wa(t)})};class Ua extends ba{_parse(t){this._def.coerce&&(t.data=Boolean(t.data));if(this._getType(t)!==Qo.boolean){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.boolean,received:e.parsedType}),ua}return fa(t.data)}}Ua.create=t=>new Ua({typeName:mu.ZodBoolean,coerce:(null==t?void 0:t.coerce)||!1,...wa(t)});class Ca extends ba{_parse(t){this._def.coerce&&(t.data=new Date(t.data));if(this._getType(t)!==Qo.date){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.date,received:e.parsedType}),ua}if(isNaN(t.data.getTime())){return oa(this._getOrReturnCtx(t),{code:ta.invalid_date}),ua}const e=new aa;let n;for(const r of this._def.checks)"min"===r.kind?t.data.getTime()<r.value&&(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),e.dirty()):"max"===r.kind?t.data.getTime()>r.value&&(n=this._getOrReturnCtx(t,n),oa(n,{code:ta.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),e.dirty()):Yo.assertNever(r);return{status:e.value,value:new Date(t.data.getTime())}}_addCheck(t){return new Ca({...this._def,checks:[...this._def.checks,t]})}min(t,e){return this._addCheck({kind:"min",value:t.getTime(),message:ga.toString(e)})}max(t,e){return this._addCheck({kind:"max",value:t.getTime(),message:ga.toString(e)})}get minDate(){let t=null;for(const e of this._def.checks)"min"===e.kind&&(null===t||e.value>t)&&(t=e.value);return null!=t?new Date(t):null}get maxDate(){let t=null;for(const e of this._def.checks)"max"===e.kind&&(null===t||e.value<t)&&(t=e.value);return null!=t?new Date(t):null}}Ca.create=t=>new Ca({checks:[],coerce:(null==t?void 0:t.coerce)||!1,typeName:mu.ZodDate,...wa(t)});class Na extends ba{_parse(t){if(this._getType(t)!==Qo.symbol){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.symbol,received:e.parsedType}),ua}return fa(t.data)}}Na.create=t=>new Na({typeName:mu.ZodSymbol,...wa(t)});class La extends ba{_parse(t){if(this._getType(t)!==Qo.undefined){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.undefined,received:e.parsedType}),ua}return fa(t.data)}}La.create=t=>new La({typeName:mu.ZodUndefined,...wa(t)});class Za extends ba{_parse(t){if(this._getType(t)!==Qo.null){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.null,received:e.parsedType}),ua}return fa(t.data)}}Za.create=t=>new Za({typeName:mu.ZodNull,...wa(t)});class Ra extends ba{constructor(){super(...arguments),this._any=!0}_parse(t){return fa(t.data)}}Ra.create=t=>new Ra({typeName:mu.ZodAny,...wa(t)});class Ha extends ba{constructor(){super(...arguments),this._unknown=!0}_parse(t){return fa(t.data)}}Ha.create=t=>new Ha({typeName:mu.ZodUnknown,...wa(t)});class ja extends ba{_parse(t){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.never,received:e.parsedType}),ua}}ja.create=t=>new ja({typeName:mu.ZodNever,...wa(t)});class Da extends ba{_parse(t){if(this._getType(t)!==Qo.undefined){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.void,received:e.parsedType}),ua}return fa(t.data)}}Da.create=t=>new Da({typeName:mu.ZodVoid,...wa(t)});class $a extends ba{_parse(t){const{ctx:e,status:n}=this._processInputParams(t),r=this._def;if(e.parsedType!==Qo.array)return oa(e,{code:ta.invalid_type,expected:Qo.array,received:e.parsedType}),ua;if(null!==r.exactLength){const t=e.data.length>r.exactLength.value,s=e.data.length<r.exactLength.value;(t||s)&&(oa(e,{code:t?ta.too_big:ta.too_small,minimum:s?r.exactLength.value:void 0,maximum:t?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(null!==r.minLength&&e.data.length<r.minLength.value&&(oa(e,{code:ta.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),null!==r.maxLength&&e.data.length>r.maxLength.value&&(oa(e,{code:ta.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),e.common.async)return Promise.all([...e.data].map(((t,n)=>r.type._parseAsync(new ya(e,t,e.path,n))))).then((t=>aa.mergeArray(n,t)));const s=[...e.data].map(((t,n)=>r.type._parseSync(new ya(e,t,e.path,n))));return aa.mergeArray(n,s)}get element(){return this._def.type}min(t,e){return new $a({...this._def,minLength:{value:t,message:ga.toString(e)}})}max(t,e){return new $a({...this._def,maxLength:{value:t,message:ga.toString(e)}})}length(t,e){return new $a({...this._def,exactLength:{value:t,message:ga.toString(e)}})}nonempty(t){return this.min(1,t)}}function za(t){if(t instanceof Ka){const e={};for(const n in t.shape){const r=t.shape[n];e[n]=au.create(za(r))}return new Ka({...t._def,shape:()=>e})}return t instanceof $a?new $a({...t._def,type:za(t.element)}):t instanceof au?au.create(za(t.unwrap())):t instanceof uu?uu.create(za(t.unwrap())):t instanceof Wa?Wa.create(t.items.map((t=>za(t)))):t}$a.create=(t,e)=>new $a({type:t,minLength:null,maxLength:null,exactLength:null,typeName:mu.ZodArray,...wa(e)});class Ka extends ba{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const t=this._def.shape(),e=Yo.objectKeys(t);return this._cached={shape:t,keys:e}}_parse(t){if(this._getType(t)!==Qo.object){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.object,received:e.parsedType}),ua}const{status:e,ctx:n}=this._processInputParams(t),{shape:r,keys:s}=this._getCached(),i=[];if(!(this._def.catchall instanceof ja&&"strip"===this._def.unknownKeys))for(const t in n.data)s.includes(t)||i.push(t);const o=[];for(const t of s){const e=r[t],s=n.data[t];o.push({key:{status:"valid",value:t},value:e._parse(new ya(n,s,n.path,t)),alwaysSet:t in n.data})}if(this._def.catchall instanceof ja){const t=this._def.unknownKeys;if("passthrough"===t)for(const t of i)o.push({key:{status:"valid",value:t},value:{status:"valid",value:n.data[t]}});else if("strict"===t)i.length>0&&(oa(n,{code:ta.unrecognized_keys,keys:i}),e.dirty());else if("strip"!==t)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const t=this._def.catchall;for(const e of i){const r=n.data[e];o.push({key:{status:"valid",value:e},value:t._parse(new ya(n,r,n.path,e)),alwaysSet:e in n.data})}}return n.common.async?Promise.resolve().then((async()=>{const t=[];for(const e of o){const n=await e.key;t.push({key:n,value:await e.value,alwaysSet:e.alwaysSet})}return t})).then((t=>aa.mergeObjectSync(e,t))):aa.mergeObjectSync(e,o)}get shape(){return this._def.shape()}strict(t){return new Ka({...this._def,unknownKeys:"strict",...void 0!==t?{errorMap:(e,n)=>{var r,s,i,o;const a=null!==(i=null===(s=(r=this._def).errorMap)||void 0===s?void 0:s.call(r,e,n).message)&&void 0!==i?i:n.defaultError;return"unrecognized_keys"===e.code?{message:null!==(o=ga.errToObj(t).message)&&void 0!==o?o:a}:{message:a}}}:{}})}strip(){return new Ka({...this._def,unknownKeys:"strip"})}passthrough(){return new Ka({...this._def,unknownKeys:"passthrough"})}extend(t){return new Ka({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new Ka({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:mu.ZodObject})}setKey(t,e){return this.augment({[t]:e})}catchall(t){return new Ka({...this._def,catchall:t})}pick(t){const e={};return Yo.objectKeys(t).forEach((n=>{t[n]&&this.shape[n]&&(e[n]=this.shape[n])})),new Ka({...this._def,shape:()=>e})}omit(t){const e={};return Yo.objectKeys(this.shape).forEach((n=>{t[n]||(e[n]=this.shape[n])})),new Ka({...this._def,shape:()=>e})}deepPartial(){return za(this)}partial(t){const e={};return Yo.objectKeys(this.shape).forEach((n=>{const r=this.shape[n];t&&!t[n]?e[n]=r:e[n]=r.optional()})),new Ka({...this._def,shape:()=>e})}required(t){const e={};return Yo.objectKeys(this.shape).forEach((n=>{if(t&&!t[n])e[n]=this.shape[n];else{let t=this.shape[n];for(;t instanceof au;)t=t._def.innerType;e[n]=t}})),new Ka({...this._def,shape:()=>e})}keyof(){return nu(Yo.objectKeys(this.shape))}}Ka.create=(t,e)=>new Ka({shape:()=>t,unknownKeys:"strip",catchall:ja.create(),typeName:mu.ZodObject,...wa(e)}),Ka.strictCreate=(t,e)=>new Ka({shape:()=>t,unknownKeys:"strict",catchall:ja.create(),typeName:mu.ZodObject,...wa(e)}),Ka.lazycreate=(t,e)=>new Ka({shape:t,unknownKeys:"strip",catchall:ja.create(),typeName:mu.ZodObject,...wa(e)});class Fa extends ba{_parse(t){const{ctx:e}=this._processInputParams(t),n=this._def.options;if(e.common.async)return Promise.all(n.map((async t=>{const n={...e,common:{...e.common,issues:[]},parent:null};return{result:await t._parseAsync({data:e.data,path:e.path,parent:n}),ctx:n}}))).then((function(t){for(const e of t)if("valid"===e.result.status)return e.result;for(const n of t)if("dirty"===n.result.status)return e.common.issues.push(...n.ctx.common.issues),n.result;const n=t.map((t=>new ea(t.ctx.common.issues)));return oa(e,{code:ta.invalid_union,unionErrors:n}),ua}));{let t;const r=[];for(const s of n){const n={...e,common:{...e.common,issues:[]},parent:null},i=s._parseSync({data:e.data,path:e.path,parent:n});if("valid"===i.status)return i;"dirty"!==i.status||t||(t={result:i,ctx:n}),n.common.issues.length&&r.push(n.common.issues)}if(t)return e.common.issues.push(...t.ctx.common.issues),t.result;const s=r.map((t=>new ea(t)));return oa(e,{code:ta.invalid_union,unionErrors:s}),ua}}get options(){return this._def.options}}Fa.create=(t,e)=>new Fa({options:t,typeName:mu.ZodUnion,...wa(e)});const qa=t=>t instanceof tu?qa(t.schema):t instanceof ou?qa(t.innerType()):t instanceof eu?[t.value]:t instanceof ru?t.options:t instanceof su?Object.keys(t.enum):t instanceof cu?qa(t._def.innerType):t instanceof La?[void 0]:t instanceof Za?[null]:null;class Va extends ba{_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==Qo.object)return oa(e,{code:ta.invalid_type,expected:Qo.object,received:e.parsedType}),ua;const n=this.discriminator,r=e.data[n],s=this.optionsMap.get(r);return s?e.common.async?s._parseAsync({data:e.data,path:e.path,parent:e}):s._parseSync({data:e.data,path:e.path,parent:e}):(oa(e,{code:ta.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),ua)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(t,e,n){const r=new Map;for(const n of e){const e=qa(n.shape[t]);if(!e)throw new Error(`A discriminator value for key \`${t}\` could not be extracted from all schema options`);for(const s of e){if(r.has(s))throw new Error(`Discriminator property ${String(t)} has duplicate value ${String(s)}`);r.set(s,n)}}return new Va({typeName:mu.ZodDiscriminatedUnion,discriminator:t,options:e,optionsMap:r,...wa(n)})}}function Ma(t,e){const n=Xo(t),r=Xo(e);if(t===e)return{valid:!0,data:t};if(n===Qo.object&&r===Qo.object){const n=Yo.objectKeys(e),r=Yo.objectKeys(t).filter((t=>-1!==n.indexOf(t))),s={...t,...e};for(const n of r){const r=Ma(t[n],e[n]);if(!r.valid)return{valid:!1};s[n]=r.data}return{valid:!0,data:s}}if(n===Qo.array&&r===Qo.array){if(t.length!==e.length)return{valid:!1};const n=[];for(let r=0;r<t.length;r++){const s=Ma(t[r],e[r]);if(!s.valid)return{valid:!1};n.push(s.data)}return{valid:!0,data:n}}return n===Qo.date&&r===Qo.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class Ga extends ba{_parse(t){const{status:e,ctx:n}=this._processInputParams(t),r=(t,r)=>{if(ha(t)||ha(r))return ua;const s=Ma(t.value,r.value);return s.valid?((da(t)||da(r))&&e.dirty(),{status:e.value,value:s.data}):(oa(n,{code:ta.invalid_intersection_types}),ua)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then((([t,e])=>r(t,e))):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Ga.create=(t,e,n)=>new Ga({left:t,right:e,typeName:mu.ZodIntersection,...wa(n)});class Wa extends ba{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==Qo.array)return oa(n,{code:ta.invalid_type,expected:Qo.array,received:n.parsedType}),ua;if(n.data.length<this._def.items.length)return oa(n,{code:ta.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),ua;!this._def.rest&&n.data.length>this._def.items.length&&(oa(n,{code:ta.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),e.dirty());const r=[...n.data].map(((t,e)=>{const r=this._def.items[e]||this._def.rest;return r?r._parse(new ya(n,t,n.path,e)):null})).filter((t=>!!t));return n.common.async?Promise.all(r).then((t=>aa.mergeArray(e,t))):aa.mergeArray(e,r)}get items(){return this._def.items}rest(t){return new Wa({...this._def,rest:t})}}Wa.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Wa({items:t,typeName:mu.ZodTuple,rest:null,...wa(e)})};class Ya extends ba{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==Qo.object)return oa(n,{code:ta.invalid_type,expected:Qo.object,received:n.parsedType}),ua;const r=[],s=this._def.keyType,i=this._def.valueType;for(const t in n.data)r.push({key:s._parse(new ya(n,t,n.path,t)),value:i._parse(new ya(n,n.data[t],n.path,t))});return n.common.async?aa.mergeObjectAsync(e,r):aa.mergeObjectSync(e,r)}get element(){return this._def.valueType}static create(t,e,n){return new Ya(e instanceof ba?{keyType:t,valueType:e,typeName:mu.ZodRecord,...wa(n)}:{keyType:Ia.create(),valueType:t,typeName:mu.ZodRecord,...wa(e)})}}class Ja extends ba{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==Qo.map)return oa(n,{code:ta.invalid_type,expected:Qo.map,received:n.parsedType}),ua;const r=this._def.keyType,s=this._def.valueType,i=[...n.data.entries()].map((([t,e],i)=>({key:r._parse(new ya(n,t,n.path,[i,"key"])),value:s._parse(new ya(n,e,n.path,[i,"value"]))})));if(n.common.async){const t=new Map;return Promise.resolve().then((async()=>{for(const n of i){const r=await n.key,s=await n.value;if("aborted"===r.status||"aborted"===s.status)return ua;"dirty"!==r.status&&"dirty"!==s.status||e.dirty(),t.set(r.value,s.value)}return{status:e.value,value:t}}))}{const t=new Map;for(const n of i){const r=n.key,s=n.value;if("aborted"===r.status||"aborted"===s.status)return ua;"dirty"!==r.status&&"dirty"!==s.status||e.dirty(),t.set(r.value,s.value)}return{status:e.value,value:t}}}}Ja.create=(t,e,n)=>new Ja({valueType:e,keyType:t,typeName:mu.ZodMap,...wa(n)});class Qa extends ba{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.parsedType!==Qo.set)return oa(n,{code:ta.invalid_type,expected:Qo.set,received:n.parsedType}),ua;const r=this._def;null!==r.minSize&&n.data.size<r.minSize.value&&(oa(n,{code:ta.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),e.dirty()),null!==r.maxSize&&n.data.size>r.maxSize.value&&(oa(n,{code:ta.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),e.dirty());const s=this._def.valueType;function i(t){const n=new Set;for(const r of t){if("aborted"===r.status)return ua;"dirty"===r.status&&e.dirty(),n.add(r.value)}return{status:e.value,value:n}}const o=[...n.data.values()].map(((t,e)=>s._parse(new ya(n,t,n.path,e))));return n.common.async?Promise.all(o).then((t=>i(t))):i(o)}min(t,e){return new Qa({...this._def,minSize:{value:t,message:ga.toString(e)}})}max(t,e){return new Qa({...this._def,maxSize:{value:t,message:ga.toString(e)}})}size(t,e){return this.min(t,e).max(t,e)}nonempty(t){return this.min(1,t)}}Qa.create=(t,e)=>new Qa({valueType:t,minSize:null,maxSize:null,typeName:mu.ZodSet,...wa(e)});class Xa extends ba{constructor(){super(...arguments),this.validate=this.implement}_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==Qo.function)return oa(e,{code:ta.invalid_type,expected:Qo.function,received:e.parsedType}),ua;function n(t,n){return ia({data:t,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,sa(),na].filter((t=>!!t)),issueData:{code:ta.invalid_arguments,argumentsError:n}})}function r(t,n){return ia({data:t,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,sa(),na].filter((t=>!!t)),issueData:{code:ta.invalid_return_type,returnTypeError:n}})}const s={errorMap:e.common.contextualErrorMap},i=e.data;return this._def.returns instanceof iu?fa((async(...t)=>{const e=new ea([]),o=await this._def.args.parseAsync(t,s).catch((r=>{throw e.addIssue(n(t,r)),e})),a=await i(...o);return await this._def.returns._def.type.parseAsync(a,s).catch((t=>{throw e.addIssue(r(a,t)),e}))})):fa(((...t)=>{const e=this._def.args.safeParse(t,s);if(!e.success)throw new ea([n(t,e.error)]);const o=i(...e.data),a=this._def.returns.safeParse(o,s);if(!a.success)throw new ea([r(o,a.error)]);return a.data}))}parameters(){return this._def.args}returnType(){return this._def.returns}args(...t){return new Xa({...this._def,args:Wa.create(t).rest(Ha.create())})}returns(t){return new Xa({...this._def,returns:t})}implement(t){return this.parse(t)}strictImplement(t){return this.parse(t)}static create(t,e,n){return new Xa({args:t||Wa.create([]).rest(Ha.create()),returns:e||Ha.create(),typeName:mu.ZodFunction,...wa(n)})}}class tu extends ba{get schema(){return this._def.getter()}_parse(t){const{ctx:e}=this._processInputParams(t);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}}tu.create=(t,e)=>new tu({getter:t,typeName:mu.ZodLazy,...wa(e)});class eu extends ba{_parse(t){if(t.data!==this._def.value){const e=this._getOrReturnCtx(t);return oa(e,{received:e.data,code:ta.invalid_literal,expected:this._def.value}),ua}return{status:"valid",value:t.data}}get value(){return this._def.value}}function nu(t,e){return new ru({values:t,typeName:mu.ZodEnum,...wa(e)})}eu.create=(t,e)=>new eu({value:t,typeName:mu.ZodLiteral,...wa(e)});class ru extends ba{_parse(t){if("string"!=typeof t.data){const e=this._getOrReturnCtx(t),n=this._def.values;return oa(e,{expected:Yo.joinValues(n),received:e.parsedType,code:ta.invalid_type}),ua}if(-1===this._def.values.indexOf(t.data)){const e=this._getOrReturnCtx(t),n=this._def.values;return oa(e,{received:e.data,code:ta.invalid_enum_value,options:n}),ua}return fa(t.data)}get options(){return this._def.values}get enum(){const t={};for(const e of this._def.values)t[e]=e;return t}get Values(){const t={};for(const e of this._def.values)t[e]=e;return t}get Enum(){const t={};for(const e of this._def.values)t[e]=e;return t}extract(t){return ru.create(t)}exclude(t){return ru.create(this.options.filter((e=>!t.includes(e))))}}ru.create=nu;class su extends ba{_parse(t){const e=Yo.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(t);if(n.parsedType!==Qo.string&&n.parsedType!==Qo.number){const t=Yo.objectValues(e);return oa(n,{expected:Yo.joinValues(t),received:n.parsedType,code:ta.invalid_type}),ua}if(-1===e.indexOf(t.data)){const t=Yo.objectValues(e);return oa(n,{received:n.data,code:ta.invalid_enum_value,options:t}),ua}return fa(t.data)}get enum(){return this._def.values}}su.create=(t,e)=>new su({values:t,typeName:mu.ZodNativeEnum,...wa(e)});class iu extends ba{unwrap(){return this._def.type}_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==Qo.promise&&!1===e.common.async)return oa(e,{code:ta.invalid_type,expected:Qo.promise,received:e.parsedType}),ua;const n=e.parsedType===Qo.promise?e.data:Promise.resolve(e.data);return fa(n.then((t=>this._def.type.parseAsync(t,{path:e.path,errorMap:e.common.contextualErrorMap}))))}}iu.create=(t,e)=>new iu({type:t,typeName:mu.ZodPromise,...wa(e)});class ou extends ba{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===mu.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:e,ctx:n}=this._processInputParams(t),r=this._def.effect||null;if("preprocess"===r.type){const t=r.transform(n.data);return n.common.async?Promise.resolve(t).then((t=>this._def.schema._parseAsync({data:t,path:n.path,parent:n}))):this._def.schema._parseSync({data:t,path:n.path,parent:n})}const s={addIssue:t=>{oa(n,t),t.fatal?e.abort():e.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),"refinement"===r.type){const t=t=>{const e=r.refinement(t,s);if(n.common.async)return Promise.resolve(e);if(e instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return t};if(!1===n.common.async){const r=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===r.status?ua:("dirty"===r.status&&e.dirty(),t(r.value),{status:e.value,value:r.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((n=>"aborted"===n.status?ua:("dirty"===n.status&&e.dirty(),t(n.value).then((()=>({status:e.value,value:n.value}))))))}if("transform"===r.type){if(!1===n.common.async){const t=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!la(t))return t;const i=r.transform(t.value,s);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:i}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((t=>la(t)?Promise.resolve(r.transform(t.value,s)).then((t=>({status:e.value,value:t}))):t))}Yo.assertNever(r)}}ou.create=(t,e,n)=>new ou({schema:t,typeName:mu.ZodEffects,effect:e,...wa(n)}),ou.createWithPreprocess=(t,e,n)=>new ou({schema:e,effect:{type:"preprocess",transform:t},typeName:mu.ZodEffects,...wa(n)});class au extends ba{_parse(t){return this._getType(t)===Qo.undefined?fa(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}au.create=(t,e)=>new au({innerType:t,typeName:mu.ZodOptional,...wa(e)});class uu extends ba{_parse(t){return this._getType(t)===Qo.null?fa(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}uu.create=(t,e)=>new uu({innerType:t,typeName:mu.ZodNullable,...wa(e)});class cu extends ba{_parse(t){const{ctx:e}=this._processInputParams(t);let n=e.data;return e.parsedType===Qo.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:e.path,parent:e})}removeDefault(){return this._def.innerType}}cu.create=(t,e)=>new cu({innerType:t,typeName:mu.ZodDefault,defaultValue:"function"==typeof e.default?e.default:()=>e.default,...wa(e)});class fu extends ba{_parse(t){const{ctx:e}=this._processInputParams(t),n={...e,common:{...e.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return pa(r)?r.then((t=>({status:"valid",value:"valid"===t.status?t.value:this._def.catchValue({get error(){return new ea(n.common.issues)},input:n.data})}))):{status:"valid",value:"valid"===r.status?r.value:this._def.catchValue({get error(){return new ea(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}fu.create=(t,e)=>new fu({innerType:t,typeName:mu.ZodCatch,catchValue:"function"==typeof e.catch?e.catch:()=>e.catch,...wa(e)});class hu extends ba{_parse(t){if(this._getType(t)!==Qo.nan){const e=this._getOrReturnCtx(t);return oa(e,{code:ta.invalid_type,expected:Qo.nan,received:e.parsedType}),ua}return{status:"valid",value:t.data}}}hu.create=t=>new hu({typeName:mu.ZodNaN,...wa(t)});const du=Symbol("zod_brand");class lu extends ba{_parse(t){const{ctx:e}=this._processInputParams(t),n=e.data;return this._def.type._parse({data:n,path:e.path,parent:e})}unwrap(){return this._def.type}}class pu extends ba{_parse(t){const{status:e,ctx:n}=this._processInputParams(t);if(n.common.async){return(async()=>{const t=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===t.status?ua:"dirty"===t.status?(e.dirty(),ca(t.value)):this._def.out._parseAsync({data:t.value,path:n.path,parent:n})})()}{const t=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===t.status?ua:"dirty"===t.status?(e.dirty(),{status:"dirty",value:t.value}):this._def.out._parseSync({data:t.value,path:n.path,parent:n})}}static create(t,e){return new pu({in:t,out:e,typeName:mu.ZodPipeline})}}const gu=(t,e={},n)=>t?Ra.create().superRefine(((r,s)=>{var i,o;if(!t(r)){const t="function"==typeof e?e(r):"string"==typeof e?{message:e}:e,a=null===(o=null!==(i=t.fatal)&&void 0!==i?i:n)||void 0===o||o,u="string"==typeof t?{message:t}:t;s.addIssue({code:"custom",...u,fatal:a})}})):Ra.create(),yu={object:Ka.lazycreate};var mu;!function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline"}(mu||(mu={}));const wu=Ia.create,bu=Ba.create,vu=hu.create,_u=Ta.create,xu=Ua.create,Eu=Ca.create,Su=Na.create,ku=La.create,Au=Za.create,Ou=Ra.create,Iu=Ha.create,Pu=ja.create,Bu=Da.create,Tu=$a.create,Uu=Ka.create,Cu=Ka.strictCreate,Nu=Fa.create,Lu=Va.create,Zu=Ga.create,Ru=Wa.create,Hu=Ya.create,ju=Ja.create,Du=Qa.create,$u=Xa.create,zu=tu.create,Ku=eu.create,Fu=ru.create,qu=su.create,Vu=iu.create,Mu=ou.create,Gu=au.create,Wu=uu.create,Yu=ou.createWithPreprocess,Ju=pu.create,Qu={string:t=>Ia.create({...t,coerce:!0}),number:t=>Ba.create({...t,coerce:!0}),boolean:t=>Ua.create({...t,coerce:!0}),bigint:t=>Ta.create({...t,coerce:!0}),date:t=>Ca.create({...t,coerce:!0})},Xu=ua;var tc=Object.freeze({__proto__:null,defaultErrorMap:na,setErrorMap:function(t){ra=t},getErrorMap:sa,makeIssue:ia,EMPTY_PATH:[],addIssueToContext:oa,ParseStatus:aa,INVALID:ua,DIRTY:ca,OK:fa,isAborted:ha,isDirty:da,isValid:la,isAsync:pa,get util(){return Yo},get objectUtil(){return Jo},ZodParsedType:Qo,getParsedType:Xo,ZodType:ba,ZodString:Ia,ZodNumber:Ba,ZodBigInt:Ta,ZodBoolean:Ua,ZodDate:Ca,ZodSymbol:Na,ZodUndefined:La,ZodNull:Za,ZodAny:Ra,ZodUnknown:Ha,ZodNever:ja,ZodVoid:Da,ZodArray:$a,ZodObject:Ka,ZodUnion:Fa,ZodDiscriminatedUnion:Va,ZodIntersection:Ga,ZodTuple:Wa,ZodRecord:Ya,ZodMap:Ja,ZodSet:Qa,ZodFunction:Xa,ZodLazy:tu,ZodLiteral:eu,ZodEnum:ru,ZodNativeEnum:su,ZodPromise:iu,ZodEffects:ou,ZodTransformer:ou,ZodOptional:au,ZodNullable:uu,ZodDefault:cu,ZodCatch:fu,ZodNaN:hu,BRAND:du,ZodBranded:lu,ZodPipeline:pu,custom:gu,Schema:ba,ZodSchema:ba,late:yu,get ZodFirstPartyTypeKind(){return mu},coerce:Qu,any:Ou,array:Tu,bigint:_u,boolean:xu,date:Eu,discriminatedUnion:Lu,effect:Mu,enum:Fu,function:$u,instanceof:(t,e={message:`Input not instance of ${t.name}`})=>gu((e=>e instanceof t),e),intersection:Zu,lazy:zu,literal:Ku,map:ju,nan:vu,nativeEnum:qu,never:Pu,null:Au,nullable:Wu,number:bu,object:Uu,oboolean:()=>xu().optional(),onumber:()=>bu().optional(),optional:Gu,ostring:()=>wu().optional(),pipeline:Ju,preprocess:Yu,promise:Vu,record:Hu,set:Du,strictObject:Cu,string:wu,symbol:Su,transformer:Mu,tuple:Ru,undefined:ku,union:Nu,unknown:Iu,void:Bu,NEVER:Xu,ZodIssueCode:ta,quotelessJson:t=>JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:ea});const ec=tc.string().regex(/^[a-fA-F0-9]$/),nc=tc.string().regex(/^[a-fA-F0-9]{64}$/),rc=tc.number().min(0).max(4294967295),sc=tc.bigint(),ic=tc.instanceof(Uint8Array),oc=tc.union([ec,rc,tc.string(),ic]).array(),ac=tc.union([oc,ec,ic]),uc=tc.array(ac),cc=tc.object({value:tc.union([rc,sc]),scriptPubKey:ac}),fc=tc.object({txid:nc,vout:rc,scriptSig:ac,sequence:rc,prevout:cc.optional(),witness:uc}),hc={TxData:tc.object({version:rc,vin:tc.array(fc),vout:tc.array(cc),locktime:rc}),TxInput:fc,TxOutput:cc,witness:uc,script:ac,hexstr:ec,hash:nc,uint32:rc,uint64:sc};return t.Address=co,t.Input=Go,t.Output=Vo,t.Script=Ut,t.Sequence=qo,t.Signer=jo,t.Tap=$o,t.Transaction=class{constructor(t){"string"==typeof t&&(t=nt.hex(t)),t instanceof Uint8Array&&(t=so.decode(t));const e=hc.TxData;this._data=e.parse(so.create(t))}get data(){return this._data}get version(){return this.data.version}get vin(){return this.data.vin.map(((t,e)=>new Go(this.data,e)))}get vout(){return this.data.vout.map((t=>new Vo(t)))}get locktime(){return new Wo(this.data.locktime)}get base(){return so.encode(this.data,!0)}get buff(){return so.encode(this.data)}get raw(){return this.buff.raw}get hex(){return this.buff.hex}get size(){return this.raw.length}get bsize(){return this.base.length}get weight(){return 3*this.bsize+this.size}get vsize(){const t=this.weight%4>0?1:0;return Math.floor(this.weight/4)+t}get hash(){return ni(this.buff).reverse().hex}get txid(){return ni(this.base).reverse().hex}async export(){const{size:t,weight:e,vsize:n,hex:r}=this;return{txid:this.txid,hash:this.hash,...this.data,size:t,weight:e,vsize:n,hex:r}}},t.Tx=so,t.Witness=Mo,t}({});
//# sourceMappingURL=bundle.min.js.map
