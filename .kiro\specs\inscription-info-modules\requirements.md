# Requirements Document

## Introduction

本功能旨在为BRC20铭文铸造工具添加两个新的信息展示模块：铭文信息模块和持有人列表模块。这些模块将在现有的"内存池透视"容器上方显示，为用户提供更全面的铭文相关数据和持有人统计信息。

## Requirements

### Requirement 1

**User Story:** 作为一个BRC20铭文用户，我希望能够查看详细的铭文信息，以便更好地了解铭文的基本属性和状态。

#### Acceptance Criteria

1. WHEN 用户访问页面 THEN 系统 SHALL 在内存池透视容器上方显示铭文信息模块
2. WHEN 铭文信息模块加载 THEN 系统 SHALL 显示铭文名称、总量、已铸造数量、进度百分比、单次铸造限制、部署者地址和部署哈希等基本信息
3. WHEN 用户输入有效的Tick名称 THEN 系统 SHALL 自动更新铭文信息模块中的相关数据
4. IF 铭文数据加载失败 THEN 系统 SHALL 显示适当的错误提示信息

### Requirement 2

**User Story:** 作为一个BRC20铭文用户，我希望能够查看持有人列表和统计信息，以便了解铭文的分布情况和持有人数量。

#### Acceptance Criteria

1. WHEN 用户访问页面 THEN 系统 SHALL 在铭文信息模块旁边显示持有人列表模块
2. WHEN 持有人列表模块加载 THEN 系统 SHALL 显示持有人总数、前几名持有人地址、持有数量、占比等信息
3. WHEN 用户输入有效的Tick名称 THEN 系统 SHALL 自动更新持有人列表中的相关数据
4. WHEN 持有人列表数据较多 THEN 系统 SHALL 提供分页或滚动查看功能

### Requirement 3

**User Story:** 作为一个移动端用户，我希望这些新模块在手机上也能正常显示和使用，以便在任何设备上都能获得良好的用户体验。

#### Acceptance Criteria

1. WHEN 用户在移动设备上访问页面 THEN 系统 SHALL 确保新模块在小屏幕上正确显示
2. WHEN 屏幕宽度小于768px THEN 系统 SHALL 将两个模块垂直堆叠显示
3. WHEN 用户在触摸设备上操作 THEN 系统 SHALL 确保所有交互元素都有适当的触摸目标大小

### Requirement 4

**User Story:** 作为一个用户，我希望新模块的样式与现有界面保持一致，以便获得统一的视觉体验。

#### Acceptance Criteria

1. WHEN 新模块显示 THEN 系统 SHALL 使用与现有容器相同的样式风格（白色背景、圆角、阴影等）
2. WHEN 用户切换暗黑模式 THEN 系统 SHALL 确保新模块也相应切换到暗黑主题
3. WHEN 新模块中有链接或按钮 THEN 系统 SHALL 使用与现有界面一致的颜色和交互效果