{"version": 3, "file": "point.js", "sourceRoot": "", "sources": ["../src/point.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAS,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,SAAS,EAAE,MAAQ,yBAAyB,CAAA;AACrD,OAAO,EAAE,GAAG,EAAE,MAAc,YAAY,CAAA;AAExC,OAAO,EAAE,UAAU,EAAE,MAAO,WAAW,CAAA;AAEvC,MAAM,OAAO,GAAG,SAAS,CAAC,eAAe,CAAA;AAEzC,MAAM,CAAC,MAAM,KAAK,GAAG,OAAO,CAAA;AAE5B,MAAM,UAAU,OAAO,CAAE,CAAa;IACpC,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACrC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAA;AACtB,CAAC;AAED,MAAM,UAAU,QAAQ,CAAE,KAAgB;IACxC,MAAM,CAAC,GAAG,KAAgC,CAAA;IAC1C,OAAO,CACL,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;QACrC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CACrD,CAAA;AACH,CAAC;AAED,MAAM,UAAU,QAAQ,CAAE,KAAgB;IACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAA;IAClC,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IAC7C,IAAI;QACF,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,OAAO,IAAI,CAAA;KACZ;IAAC,MAAM;QAAE,OAAO,KAAK,CAAA;KAAE;AAC1B,CAAC;AAED,MAAM,UAAU,YAAY,CAAE,CAAW;IACvC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;KACtD;AACH,CAAC;AAED,MAAM,UAAU,MAAM,CACpB,CAAa;IAEb,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACrC,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAA;QACtB,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;KAC5B;IAAC,MAAM;QAAE,OAAO,IAAI,CAAA;KAAE;AACzB,CAAC;AAED,MAAM,UAAU,GAAG,CACjB,CAAoB,EACpB,CAAoB;IAEpB,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,CAAC,CAAA;IACxB,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,CAAC,CAAA;IACxB,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACrC,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACrC,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACrB,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;KAC5B;IAAC,MAAM;QAAE,OAAO,IAAI,CAAA;KAAE;AACzB,CAAC;AAED,MAAM,UAAU,GAAG,CACjB,CAAoB,EACpB,CAAoB;IAEpB,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,CAAC,CAAA;IACxB,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,CAAC,CAAA;IACxB,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACrC,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;IACrC,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA;QAC1B,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;KAC5B;IAAC,MAAM;QAAE,OAAO,IAAI,CAAA;KAAE;AACzB,CAAC;AAED,MAAM,UAAU,EAAE,CAChB,CAAoB,EACpB,CAAoB;IAEpB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;QAC5B,OAAO,IAAI,CAAA;KACZ;IACD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;QAC5B,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;KACpC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,MAAM,UAAU,GAAG,CACjB,CAAoB,EACpB,CAAS;IAET,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IAC3B,IAAI;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAC1B,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;QACrC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAChC,EAAE,CAAC,cAAc,EAAE,CAAA;QACnB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;KAC5B;IAAC,MAAM;QAAE,OAAO,IAAI,CAAA;KAAE;AACzB,CAAC;AAED,MAAM,UAAU,GAAG,CACjB,CAAS;IAET,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAC1B,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;IACzB,MAAM,EAAE,GAAK,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACpC,EAAE,CAAC,cAAc,EAAE,CAAA;IACnB,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAA;AAC7B,CAAC;AAED,MAAM,UAAU,MAAM,CACpB,KAAa,EACb,KAAK,GAAG,KAAK;IAEb,MAAM,IAAI,GAAI,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;IACtC,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACvC,KAAK,CAAC,cAAc,EAAE,CAAA;IACtB,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAA;AACnC,CAAC;AAED,MAAM,UAAU,QAAQ,CAAE,CAAa;IACrC,MAAM,KAAK,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAChC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;IACvC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,MAAM,EAAE,KAAK,CAAE,CAAC,CAAA;AACrC,CAAC"}