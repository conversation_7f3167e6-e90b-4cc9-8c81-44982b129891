{"name": "@cmdcode/buff-utils", "type": "module", "version": "2.0.0", "description": "Cross-platform library for working with buffers and moving between formats.", "author": "<PERSON>", "license": "CC-BY-1.0", "keywords": ["bytes", "buffer", "bech32", "base58", "base64", "convert"], "repository": {"type": "git", "url": "https://github.com/cmdruid/buff-utils.git"}, "publishConfig": {"registry": "https://registry.npmjs.org", "access": "public"}, "files": ["README.md", "LICENSE", "dist", "src"], "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/module.mjs"}}}, "types": "./dist/index.d.ts", "main": "./dist/main.cjs", "unpkg": "./dist/browser.js", "scripts": {"build": "yarn clean && rollup -c rollup.config.ts --configPlugin typescript", "clean": "rm -rf dist/* coverage .nyc_output", "scratch": "tsx test/scratch.ts", "test": "nyc --reporter=lcov tsx test/tape.ts | tap-spec; nyc report ---reporter=text", "release": "yarn test && yarn build"}, "devDependencies": {"@noble/curves": "^1.1.0", "@noble/hashes": "^1.3.0", "@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^11.1.3", "@scure/base": "^1.1.1", "@types/node": "^20.5.7", "@types/tape": "^5.6.0", "nyc": "^15.1.0", "rollup": "^3.27.0", "tap-spec": "^5.0.0", "tape": "^5.6.6", "tslib": "^2.6.2", "tsx": "^3.12.7", "typescript": "^5.1.6"}}