"use strict";exports.decode=function(e,r=!1){{const t=Buffer.from(e,"base64");return r?t.toString("utf8"):new Uint8Array(t.buffer,t.byteOffset,t.length)}},exports.encode=function(e,r=!1,t=!0){let f="";return f=("string"==typeof e?Buffer.from(e,"utf8"):Buffer.from(e)).toString("base64"),r&&(f=function(e){return e.replace(/\+/g,"-").replace(/\//g,"_")}(f)),t||(f=f.replace(/=/g,"")),f};
//# sourceMappingURL=index.node.js.map
