import { Buff } from '@cmdcode/buff-utils';
export function random(size) {
    return Buff.random(size);
}
export function increment_buffer(buffer) {
    let i = buffer.length - 1;
    for (i; i >= 0; i--) {
        if (buffer[i] < 255) {
            buffer.set([buffer[i] + 1], i);
            return buffer;
        }
    }
    throw TypeError('Unable to increment buffer: ' + buffer.toString());
}
export function stringify(content) {
    switch (typeof content) {
        case 'object':
            return JSON.stringify(content);
        case 'string':
            return content;
        case 'bigint':
            return content.toString();
        case 'number':
            return content.toString();
        case 'boolean':
            return String(content);
        default:
            throw new TypeError('Content type not supported: ' + typeof content);
    }
}
//# sourceMappingURL=util.js.map