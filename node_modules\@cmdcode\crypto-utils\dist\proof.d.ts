import { Bytes } from '@cmdcode/buff-utils';
import { SignOptions } from './config.js';
import { Literal, ProofData, SignedEvent } from './types.js';
declare const PROOF_DEFAULTS: {
    kind: number;
    stamp: number;
    tags: Literal[][];
};
export declare function create_proof<T>(seckey: Bytes, data: T, params?: Literal[][], options?: SignOptions): string;
export declare function parse_proof(proof: string): ProofData;
export declare function parse_proofs(proofs: string[]): ProofData[];
export declare function validate_proof(proof: string): boolean;
export declare function verify_proof<T>(proof: string, data: T, options?: SignOptions): boolean;
export declare function create_event<T>(proof: string, data: T): SignedEvent;
export declare function encode_params(params?: Literal[][]): string;
export declare function decode_params(str?: string): string[][];
export declare function parse_config(params?: Literal[][]): typeof PROOF_DEFAULTS;
export {};
//# sourceMappingURL=proof.d.ts.map