export declare const OPCODE_MAP: {
    OP_0: number;
    OP_PUSHDATA1: number;
    OP_PUSHDATA2: number;
    OP_PUSHDATA4: number;
    OP_1NEGATE: number;
    OP_SUCCESS80: number;
    OP_1: number;
    OP_2: number;
    OP_3: number;
    OP_4: number;
    OP_5: number;
    OP_6: number;
    OP_7: number;
    OP_8: number;
    OP_9: number;
    OP_10: number;
    OP_11: number;
    OP_12: number;
    OP_13: number;
    OP_14: number;
    OP_15: number;
    OP_16: number;
    OP_NOP: number;
    OP_SUCCESS98: number;
    OP_IF: number;
    OP_NOTIF: number;
    OP_ELSE: number;
    OP_ENDIF: number;
    OP_VERIFY: number;
    OP_RETURN: number;
    OP_TOALTSTACK: number;
    OP_FROMALTSTACK: number;
    OP_2DROP: number;
    OP_2DUP: number;
    OP_3DUP: number;
    OP_2OVER: number;
    OP_2ROT: number;
    OP_2SWAP: number;
    OP_IFDUP: number;
    OP_DEPTH: number;
    OP_DROP: number;
    OP_DUP: number;
    OP_NIP: number;
    OP_OVER: number;
    OP_PICK: number;
    OP_ROLL: number;
    OP_ROT: number;
    OP_SWAP: number;
    OP_TUCK: number;
    OP_SUCCESS126: number;
    OP_SUCCESS127: number;
    OP_SUCCESS128: number;
    OP_SUCCESS129: number;
    OP_SIZE: number;
    OP_SUCCESS131: number;
    OP_SUCCESS132: number;
    OP_SUCCESS133: number;
    OP_SUCCESS134: number;
    OP_EQUAL: number;
    OP_EQUALVERIFY: number;
    OP_SUCCESS137: number;
    OP_SUCCESS138: number;
    OP_1ADD: number;
    OP_1SUB: number;
    OP_SUCCESS141: number;
    OP_SUCCESS142: number;
    OP_NEGATE: number;
    OP_ABS: number;
    OP_NOT: number;
    OP_0NOTEQUAL: number;
    OP_ADD: number;
    OP_SUB: number;
    OP_SUCCESS149: number;
    OP_SUCCESS150: number;
    OP_SUCCESS151: number;
    OP_SUCCESS152: number;
    OP_SUCCESS153: number;
    OP_BOOLAND: number;
    OP_BOOLOR: number;
    OP_NUMEQUAL: number;
    OP_NUMEQUALVERIFY: number;
    OP_NUMNOTEQUAL: number;
    OP_LESSTHAN: number;
    OP_GREATERTHAN: number;
    OP_LESSTHANOREQUAL: number;
    OP_GREATERTHANOREQUAL: number;
    OP_MIN: number;
    OP_MAX: number;
    OP_WITHIN: number;
    OP_RIPEMD160: number;
    OP_SHA1: number;
    OP_SHA256: number;
    OP_HASH160: number;
    OP_HASH256: number;
    OP_CODESEPARATOR: number;
    OP_CHECKSIG: number;
    OP_CHECKSIGVERIFY: number;
    OP_CHECKMULTISIG: number;
    OP_CHECKMULTISIGVERIFY: number;
    OP_NOP1: number;
    OP_CHECKLOCKTIMEVERIFY: number;
    OP_CHECKSEQUENCEVERIFY: number;
    OP_NOP4: number;
    OP_NOP5: number;
    OP_NOP6: number;
    OP_NOP7: number;
    OP_NOP8: number;
    OP_NOP9: number;
    OP_NOP10: number;
    OP_CHECKSIGADD: number;
};
export declare function getOpLabel(num: number): string;
export declare function getOpCode(string: string): number;
export declare function getWordType(word: number): string;
export declare function isValidWord(word: number): boolean;
//# sourceMappingURL=words.d.ts.map