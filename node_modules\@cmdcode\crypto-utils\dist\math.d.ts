import { Buff, Bytes } from '@cmdcode/buff-utils';
export { mod, pow, pow2, invert } from '@noble/curves/abstract/modular';
export declare const modN: (x: bigint) => bigint;
export declare const modP: (x: bigint) => bigint;
export declare const powN: (x: bigint, exp: bigint) => bigint;
export declare const on_curve: (x: bigint) => boolean;
export declare const in_field: (x: bigint) => boolean;
export declare function mod_bytes(bytes: Bytes): Buff;
//# sourceMappingURL=math.d.ts.map