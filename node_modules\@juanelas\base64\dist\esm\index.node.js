function e(e,r=!1,t=!0){let f="";f=("string"==typeof e?Buffer.from(e,"utf8"):Buffer.from(e)).toString("base64");return r&&(f=function(e){return e.replace(/\+/g,"-").replace(/\//g,"_")}(f)),t||(f=f.replace(/=/g,"")),f}function r(e,r=!1){{const t=Buffer.from(e,"base64");return r?t.toString("utf8"):new Uint8Array(t.buffer,t.byteOffset,t.length)}}export{r as decode,e as encode};
//# sourceMappingURL=index.node.js.map
