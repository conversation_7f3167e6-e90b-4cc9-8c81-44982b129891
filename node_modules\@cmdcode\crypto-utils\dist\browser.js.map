{"version": 3, "file": "browser.js", "sources": ["../node_modules/@noble/hashes/esm/_assert.js", "../node_modules/@noble/hashes/esm/crypto.js", "../node_modules/@noble/hashes/esm/utils.js", "../node_modules/@noble/hashes/esm/_sha2.js", "../node_modules/@noble/hashes/esm/sha256.js", "../node_modules/@noble/curves/esm/abstract/utils.js", "../node_modules/@noble/curves/esm/abstract/modular.js", "../node_modules/@noble/curves/esm/abstract/curve.js", "../node_modules/@noble/curves/esm/abstract/weierstrass.js", "../node_modules/@noble/hashes/esm/hmac.js", "../node_modules/@noble/curves/esm/_shortw_utils.js", "../node_modules/@noble/curves/esm/secp256k1.js", "../src/config.ts", "../node_modules/@cmdcode/buff-utils/dist/module.mjs", "../src/const.ts", "../src/math.ts", "../src/assert.ts", "../src/ecc.ts", "../node_modules/@noble/hashes/esm/_u64.js", "../node_modules/@noble/hashes/esm/sha512.js", "../node_modules/@noble/hashes/esm/ripemd160.js", "../src/hash.ts", "../src/util.ts", "../src/keys.ts", "../src/hd.ts", "../src/proof.ts", "../src/point.ts"], "sourcesContent": ["function number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`Wrong positive integer: ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`Expected boolean, not ${b}`);\n}\nfunction bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new Error('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n    if (typeof hash !== 'function' || typeof hash.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\nexport { number, bool, bytes, hash, exists, output };\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n//# sourceMappingURL=_assert.js.map", "export const crypto = typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n//# sourceMappingURL=crypto.js.map", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nconst u8a = (a) => a instanceof Uint8Array;\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!u8a(data))\n        throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\n// For runtime check if class implements interface\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "import { exists, output } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nexport class SHA2 extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        exists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        exists(this);\n        output(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_sha2.js.map", "import { SHA2 } from './_sha2.js';\nimport { rotr, wrapConstructor } from './utils.js';\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n//# sourceMappingURL=sha256.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst u8a = (a) => a instanceof Uint8Array;\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\nexport function numberToHexUnpadded(num) {\n    const hex = num.toString(16);\n    return hex.length & 1 ? `0${hex}` : hex;\n}\nexport function hexToNumber(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // Big Endian\n    return BigInt(hex === '' ? '0' : `0x${hex}`);\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n    return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nexport function numberToBytesBE(n, len) {\n    return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n, len) {\n    return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n) {\n    return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title, hex, expectedLength) {\n    let res;\n    if (typeof hex === 'string') {\n        try {\n            res = hexToBytes(hex);\n        }\n        catch (e) {\n            throw new Error(`${title} must be valid hex string, got \"${hex}\". Cause: ${e}`);\n        }\n    }\n    else if (u8a(hex)) {\n        // Uint8Array.from() instead of hash.slice() because node.js Buffer\n        // is instance of Uint8Array, and its slice() creates **mutable** copy\n        res = Uint8Array.from(hex);\n    }\n    else {\n        throw new Error(`${title} must be hex string or Uint8Array`);\n    }\n    const len = res.length;\n    if (typeof expectedLength === 'number' && len !== expectedLength)\n        throw new Error(`${title} expected ${expectedLength} bytes, got ${len}`);\n    return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\nexport function equalBytes(b1, b2) {\n    // We don't care about timing attacks here\n    if (b1.length !== b2.length)\n        return false;\n    for (let i = 0; i < b1.length; i++)\n        if (b1[i] !== b2[i])\n            return false;\n    return true;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n) {\n    let len;\n    for (len = 0; n > _0n; n >>= _1n, len += 1)\n        ;\n    return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n, pos) {\n    return (n >> BigInt(pos)) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nexport const bitSet = (n, pos, value) => {\n    return n | ((value ? _1n : _0n) << BigInt(pos));\n};\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n) => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = (data) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr) => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n    if (typeof hashLen !== 'number' || hashLen < 2)\n        throw new Error('hashLen must be a number');\n    if (typeof qByteLen !== 'number' || qByteLen < 2)\n        throw new Error('qByteLen must be a number');\n    if (typeof hmacFn !== 'function')\n        throw new Error('hmacFn must be a function');\n    // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n    let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n    let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n    let i = 0; // Iterations counter, will throw when over 1000\n    const reset = () => {\n        v.fill(1);\n        k.fill(0);\n        i = 0;\n    };\n    const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n    const reseed = (seed = u8n()) => {\n        // HMAC-DRBG reseed() function. Steps D-G\n        k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n        v = h(); // v = hmac(k || v)\n        if (seed.length === 0)\n            return;\n        k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n        v = h(); // v = hmac(k || v)\n    };\n    const gen = () => {\n        // HMAC-DRBG generate() function\n        if (i++ >= 1000)\n            throw new Error('drbg: tried 1000 values');\n        let len = 0;\n        const out = [];\n        while (len < qByteLen) {\n            v = h();\n            const sl = v.slice();\n            out.push(sl);\n            len += v.length;\n        }\n        return concatBytes(...out);\n    };\n    const genUntil = (seed, pred) => {\n        reset();\n        reseed(seed); // Steps D-G\n        let res = undefined; // Step H: grind until k is in [1..n-1]\n        while (!(res = pred(gen())))\n            reseed();\n        reset();\n        return res;\n    };\n    return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n    bigint: (val) => typeof val === 'bigint',\n    function: (val) => typeof val === 'function',\n    boolean: (val) => typeof val === 'boolean',\n    string: (val) => typeof val === 'string',\n    stringOrUint8Array: (val) => typeof val === 'string' || val instanceof Uint8Array,\n    isSafeInteger: (val) => Number.isSafeInteger(val),\n    array: (val) => Array.isArray(val),\n    field: (val, object) => object.Fp.isValid(val),\n    hash: (val) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nexport function validateObject(object, validators, optValidators = {}) {\n    const checkField = (fieldName, type, isOptional) => {\n        const checkVal = validatorFns[type];\n        if (typeof checkVal !== 'function')\n            throw new Error(`Invalid validator \"${type}\", expected function`);\n        const val = object[fieldName];\n        if (isOptional && val === undefined)\n            return;\n        if (!checkVal(val, object)) {\n            throw new Error(`Invalid param ${String(fieldName)}=${val} (${typeof val}), expected ${type}`);\n        }\n    };\n    for (const [fieldName, type] of Object.entries(validators))\n        checkField(fieldName, type, false);\n    for (const [fieldName, type] of Object.entries(optValidators))\n        checkField(fieldName, type, true);\n    return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n//# sourceMappingURL=utils.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Utilities for modular arithmetics and finite fields\nimport { bitMask, numberToBytesBE, numberToBytesLE, bytesToNumberBE, bytesToNumberLE, ensureBytes, validateObject, } from './utils.js';\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _4n = BigInt(4), _5n = BigInt(5), _8n = BigInt(8);\n// prettier-ignore\nconst _9n = BigInt(9), _16n = BigInt(16);\n// Calculates a modulo b\nexport function mod(a, b) {\n    const result = a % b;\n    return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\n// TODO: use field version && remove\nexport function pow(num, power, modulo) {\n    if (modulo <= _0n || power < _0n)\n        throw new Error('Expected power/modulo > 0');\n    if (modulo === _1n)\n        return _0n;\n    let res = _1n;\n    while (power > _0n) {\n        if (power & _1n)\n            res = (res * num) % modulo;\n        num = (num * num) % modulo;\n        power >>= _1n;\n    }\n    return res;\n}\n// Does x ^ (2 ^ power) mod p. pow2(30, 4) == 30 ^ (2 ^ 4)\nexport function pow2(x, power, modulo) {\n    let res = x;\n    while (power-- > _0n) {\n        res *= res;\n        res %= modulo;\n    }\n    return res;\n}\n// Inverses number over modulo\nexport function invert(number, modulo) {\n    if (number === _0n || modulo <= _0n) {\n        throw new Error(`invert: expected positive integers, got n=${number} mod=${modulo}`);\n    }\n    // Euclidean GCD https://brilliant.org/wiki/extended-euclidean-algorithm/\n    // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n    let a = mod(number, modulo);\n    let b = modulo;\n    // prettier-ignore\n    let x = _0n, y = _1n, u = _1n, v = _0n;\n    while (a !== _0n) {\n        // JIT applies optimization if those two lines follow each other\n        const q = b / a;\n        const r = b % a;\n        const m = x - u * q;\n        const n = y - v * q;\n        // prettier-ignore\n        b = a, a = r, x = u, y = v, u = m, v = n;\n    }\n    const gcd = b;\n    if (gcd !== _1n)\n        throw new Error('invert: does not exist');\n    return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P) {\n    // Legendre constant: used to calculate Legendre symbol (a | p),\n    // which denotes the value of a^((p-1)/2) (mod p).\n    // (a | p) ≡ 1    if a is a square (mod p)\n    // (a | p) ≡ -1   if a is not a square (mod p)\n    // (a | p) ≡ 0    if a ≡ 0 (mod p)\n    const legendreC = (P - _1n) / _2n;\n    let Q, S, Z;\n    // Step 1: By factoring out powers of 2 from p - 1,\n    // find q and s such that p - 1 = q*(2^s) with q odd\n    for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++)\n        ;\n    // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n    for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++)\n        ;\n    // Fast-path\n    if (S === 1) {\n        const p1div4 = (P + _1n) / _4n;\n        return function tonelliFast(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Slow-path\n    const Q1div2 = (Q + _1n) / _2n;\n    return function tonelliSlow(Fp, n) {\n        // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n        if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE))\n            throw new Error('Cannot find square root');\n        let r = S;\n        // TODO: will fail at Fp2/etc\n        let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n        let x = Fp.pow(n, Q1div2); // first guess at the square root\n        let b = Fp.pow(n, Q); // first guess at the fudge factor\n        while (!Fp.eql(b, Fp.ONE)) {\n            if (Fp.eql(b, Fp.ZERO))\n                return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n            // Find m such b^(2^m)==1\n            let m = 1;\n            for (let t2 = Fp.sqr(b); m < r; m++) {\n                if (Fp.eql(t2, Fp.ONE))\n                    break;\n                t2 = Fp.sqr(t2); // t2 *= t2\n            }\n            // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n            const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n            g = Fp.sqr(ge); // g = ge * ge\n            x = Fp.mul(x, ge); // x *= ge\n            b = Fp.mul(b, g); // b *= g\n            r = m;\n        }\n        return x;\n    };\n}\nexport function FpSqrt(P) {\n    // NOTE: different algorithms can give different roots, it is up to user to decide which one they want.\n    // For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n    // P ≡ 3 (mod 4)\n    // √n = n^((P+1)/4)\n    if (P % _4n === _3n) {\n        // Not all roots possible!\n        // const ORDER =\n        //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n        // const NUM = 72057594037927816n;\n        const p1div4 = (P + _1n) / _4n;\n        return function sqrt3mod4(Fp, n) {\n            const root = Fp.pow(n, p1div4);\n            // Throw if root**2 != n\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n    if (P % _8n === _5n) {\n        const c1 = (P - _5n) / _8n;\n        return function sqrt5mod8(Fp, n) {\n            const n2 = Fp.mul(n, _2n);\n            const v = Fp.pow(n2, c1);\n            const nv = Fp.mul(n, v);\n            const i = Fp.mul(Fp.mul(nv, _2n), v);\n            const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n            if (!Fp.eql(Fp.sqr(root), n))\n                throw new Error('Cannot find square root');\n            return root;\n        };\n    }\n    // P ≡ 9 (mod 16)\n    if (P % _16n === _9n) {\n        // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n        // Means we cannot use sqrt for constants at all!\n        //\n        // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n        // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n        // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n        // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n        // sqrt = (x) => {\n        //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n        //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n        //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n        //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n        //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n        //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n        //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n        //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n        //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n        //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n        // }\n    }\n    // Other cases: Tonelli-Shanks algorithm\n    return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = [\n    'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n    'eql', 'add', 'sub', 'mul', 'pow', 'div',\n    'addN', 'subN', 'mulN', 'sqrN'\n];\nexport function validateField(field) {\n    const initial = {\n        ORDER: 'bigint',\n        MASK: 'bigint',\n        BYTES: 'isSafeInteger',\n        BITS: 'isSafeInteger',\n    };\n    const opts = FIELD_FIELDS.reduce((map, val) => {\n        map[val] = 'function';\n        return map;\n    }, initial);\n    return validateObject(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow(f, num, power) {\n    // Should have same speed as pow for bigints\n    // TODO: benchmark!\n    if (power < _0n)\n        throw new Error('Expected power > 0');\n    if (power === _0n)\n        return f.ONE;\n    if (power === _1n)\n        return num;\n    let p = f.ONE;\n    let d = num;\n    while (power > _0n) {\n        if (power & _1n)\n            p = f.mul(p, d);\n        d = f.sqr(d);\n        power >>= _1n;\n    }\n    return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch(f, nums) {\n    const tmp = new Array(nums.length);\n    // Walk from first to last, multiply them by each other MOD p\n    const lastMultiplied = nums.reduce((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = acc;\n        return f.mul(acc, num);\n    }, f.ONE);\n    // Invert last element\n    const inverted = f.inv(lastMultiplied);\n    // Walk from last to first, multiply them by inverted each other MOD p\n    nums.reduceRight((acc, num, i) => {\n        if (f.is0(num))\n            return acc;\n        tmp[i] = f.mul(acc, tmp[i]);\n        return f.mul(acc, num);\n    }, inverted);\n    return tmp;\n}\nexport function FpDiv(f, lhs, rhs) {\n    return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare(f) {\n    const legendreConst = (f.ORDER - _1n) / _2n; // Integer arithmetic\n    return (x) => {\n        const p = f.pow(x, legendreConst);\n        return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n    };\n}\n// CURVE.n lengths\nexport function nLength(n, nBitLength) {\n    // Bit size, byte size of CURVE.n\n    const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n    const nByteLength = Math.ceil(_nBitLength / 8);\n    return { nBitLength: _nBitLength, nByteLength };\n}\n/**\n * Initializes a finite field over prime. **Non-primes are not supported.**\n * Do not init in loop: slow. Very fragile: always run a benchmark on a change.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(ORDER, bitLen, isLE = false, redef = {}) {\n    if (ORDER <= _0n)\n        throw new Error(`Expected Field ORDER > 0, got ${ORDER}`);\n    const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n    if (BYTES > 2048)\n        throw new Error('Field lengths over 2048 bytes are not supported');\n    const sqrtP = FpSqrt(ORDER);\n    const f = Object.freeze({\n        ORDER,\n        BITS,\n        BYTES,\n        MASK: bitMask(BITS),\n        ZERO: _0n,\n        ONE: _1n,\n        create: (num) => mod(num, ORDER),\n        isValid: (num) => {\n            if (typeof num !== 'bigint')\n                throw new Error(`Invalid field element: expected bigint, got ${typeof num}`);\n            return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n        },\n        is0: (num) => num === _0n,\n        isOdd: (num) => (num & _1n) === _1n,\n        neg: (num) => mod(-num, ORDER),\n        eql: (lhs, rhs) => lhs === rhs,\n        sqr: (num) => mod(num * num, ORDER),\n        add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n        sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n        mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n        pow: (num, power) => FpPow(f, num, power),\n        div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n        // Same as above, but doesn't normalize\n        sqrN: (num) => num * num,\n        addN: (lhs, rhs) => lhs + rhs,\n        subN: (lhs, rhs) => lhs - rhs,\n        mulN: (lhs, rhs) => lhs * rhs,\n        inv: (num) => invert(num, ORDER),\n        sqrt: redef.sqrt || ((n) => sqrtP(f, n)),\n        invertBatch: (lst) => FpInvertBatch(f, lst),\n        // TODO: do we really need constant cmov?\n        // We don't have const-time bigints anyway, so probably will be not very useful\n        cmov: (a, b, c) => (c ? b : a),\n        toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n        fromBytes: (bytes) => {\n            if (bytes.length !== BYTES)\n                throw new Error(`Fp.fromBytes: expected ${BYTES}, got ${bytes.length}`);\n            return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n        },\n    });\n    return Object.freeze(f);\n}\nexport function FpSqrtOdd(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nexport function FpSqrtEven(Fp, elm) {\n    if (!Fp.isOdd)\n        throw new Error(`Field doesn't have isOdd`);\n    const root = Fp.sqrt(elm);\n    return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use mapKeyToField instead\n */\nexport function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n    hash = ensureBytes('privateHash', hash);\n    const hashLen = hash.length;\n    const minLen = nLength(groupOrder).nByteLength + 8;\n    if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n        throw new Error(`hashToPrivateScalar: expected ${minLen}-1024 bytes of input, got ${hashLen}`);\n    const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n    return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder) {\n    if (typeof fieldOrder !== 'bigint')\n        throw new Error('field order must be bigint');\n    const bitLength = fieldOrder.toString(2).length;\n    return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder) {\n    const length = getFieldBytesLength(fieldOrder);\n    return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key, fieldOrder, isLE = false) {\n    const len = key.length;\n    const fieldLen = getFieldBytesLength(fieldOrder);\n    const minLen = getMinHashLength(fieldOrder);\n    // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n    if (len < 16 || len < minLen || len > 1024)\n        throw new Error(`expected ${minLen}-1024 bytes of input, got ${len}`);\n    const num = isLE ? bytesToNumberBE(key) : bytesToNumberLE(key);\n    // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n    const reduced = mod(num, fieldOrder - _1n) + _1n;\n    return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n//# sourceMappingURL=modular.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Abelian group utilities\nimport { validateField, nLength } from './modular.js';\nimport { validateObject } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n// Elliptic curve multiplication of Point by scalar. Fragile.\n// Scalars should always be less than curve order: this should be checked inside of a curve itself.\n// Creates precomputation tables for fast multiplication:\n// - private scalar is split by fixed size windows of W bits\n// - every window point is collected from window's table & added to accumulator\n// - since windows are different, same point inside tables won't be accessed more than once per calc\n// - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n// - +1 window is neccessary for wNAF\n// - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n// TODO: Research returning 2d JS array of windows, instead of a single window. This would allow\n// windows to be in different memory locations\nexport function wNAF(c, bits) {\n    const constTimeNegate = (condition, item) => {\n        const neg = item.negate();\n        return condition ? neg : item;\n    };\n    const opts = (W) => {\n        const windows = Math.ceil(bits / W) + 1; // +1, because\n        const windowSize = 2 ** (W - 1); // -1 because we skip zero\n        return { windows, windowSize };\n    };\n    return {\n        constTimeNegate,\n        // non-const time multiplication ladder\n        unsafeLadder(elm, n) {\n            let p = c.ZERO;\n            let d = elm;\n            while (n > _0n) {\n                if (n & _1n)\n                    p = p.add(d);\n                d = d.double();\n                n >>= _1n;\n            }\n            return p;\n        },\n        /**\n         * Creates a wNAF precomputation window. Used for caching.\n         * Default window size is set by `utils.precompute()` and is equal to 8.\n         * Number of precomputed points depends on the curve size:\n         * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n         * - 𝑊 is the window size\n         * - 𝑛 is the bitlength of the curve order.\n         * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n         * @returns precomputed point tables flattened to a single array\n         */\n        precomputeWindow(elm, W) {\n            const { windows, windowSize } = opts(W);\n            const points = [];\n            let p = elm;\n            let base = p;\n            for (let window = 0; window < windows; window++) {\n                base = p;\n                points.push(base);\n                // =1, because we skip zero\n                for (let i = 1; i < windowSize; i++) {\n                    base = base.add(p);\n                    points.push(base);\n                }\n                p = base.double();\n            }\n            return points;\n        },\n        /**\n         * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n         * @param W window size\n         * @param precomputes precomputed tables\n         * @param n scalar (we don't check here, but should be less than curve order)\n         * @returns real and fake (for const-time) points\n         */\n        wNAF(W, precomputes, n) {\n            // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n            // But need to carefully remove other checks before wNAF. ORDER == bits here\n            const { windows, windowSize } = opts(W);\n            let p = c.ZERO;\n            let f = c.BASE;\n            const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n            const maxNumber = 2 ** W;\n            const shiftBy = BigInt(W);\n            for (let window = 0; window < windows; window++) {\n                const offset = window * windowSize;\n                // Extract W bits.\n                let wbits = Number(n & mask);\n                // Shift number by W bits.\n                n >>= shiftBy;\n                // If the bits are bigger than max size, we'll split those.\n                // +224 => 256 - 32\n                if (wbits > windowSize) {\n                    wbits -= maxNumber;\n                    n += _1n;\n                }\n                // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n                // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n                // there is negate now: it is possible that negated element from low value\n                // would be the same as high element, which will create carry into next window.\n                // It's not obvious how this can fail, but still worth investigating later.\n                // Check if we're onto Zero point.\n                // Add random point inside current window to f.\n                const offset1 = offset;\n                const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n                const cond1 = window % 2 !== 0;\n                const cond2 = wbits < 0;\n                if (wbits === 0) {\n                    // The most important part for const-time getPublicKey\n                    f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n                }\n                else {\n                    p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n                }\n            }\n            // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n            // Even if the variable is still unused, there are some checks which will\n            // throw an exception, so compiler needs to prove they won't happen, which is hard.\n            // At this point there is a way to F be infinity-point even if p is not,\n            // which makes it less const-time: around 1 bigint multiply.\n            return { p, f };\n        },\n        wNAFCached(P, precomputesMap, n, transform) {\n            // @ts-ignore\n            const W = P._WINDOW_SIZE || 1;\n            // Calculate precomputes on a first run, reuse them after\n            let comp = precomputesMap.get(P);\n            if (!comp) {\n                comp = this.precomputeWindow(P, W);\n                if (W !== 1) {\n                    precomputesMap.set(P, transform(comp));\n                }\n            }\n            return this.wNAF(W, comp, n);\n        },\n    };\n}\nexport function validateBasic(curve) {\n    validateField(curve.Fp);\n    validateObject(curve, {\n        n: 'bigint',\n        h: 'bigint',\n        Gx: 'field',\n        Gy: 'field',\n    }, {\n        nBitLength: 'isSafeInteger',\n        nByteLength: 'isSafeInteger',\n    });\n    // Set defaults\n    return Object.freeze({\n        ...nLength(curve.n, curve.nBitLength),\n        ...curve,\n        ...{ p: curve.Fp.ORDER },\n    });\n}\n//# sourceMappingURL=curve.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Short <PERSON> curve. The formula is: y² = x³ + ax + b\nimport * as mod from './modular.js';\nimport * as ut from './utils.js';\nimport { ensureBytes } from './utils.js';\nimport { wNAF, validateBasic } from './curve.js';\nfunction validatePointOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        a: 'field',\n        b: 'field',\n    }, {\n        allowedPrivateKeyLengths: 'array',\n        wrapPrivateKey: 'boolean',\n        isTorsionFree: 'function',\n        clearCofactor: 'function',\n        allowInfinityPoint: 'boolean',\n        fromBytes: 'function',\n        toBytes: 'function',\n    });\n    const { endo, Fp, a } = opts;\n    if (endo) {\n        if (!Fp.eql(a, Fp.ZERO)) {\n            throw new Error('Endomorphism can only be defined for Koblitz curves that have a=0');\n        }\n        if (typeof endo !== 'object' ||\n            typeof endo.beta !== 'bigint' ||\n            typeof endo.splitScalar !== 'function') {\n            throw new Error('Expected endomorphism with beta: bigint and splitScalar: function');\n        }\n    }\n    return Object.freeze({ ...opts });\n}\n// ASN.1 DER encoding utilities\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = ut;\nexport const DER = {\n    // asn.1 DER encoding utils\n    Err: class DERErr extends Error {\n        constructor(m = '') {\n            super(m);\n        }\n    },\n    _parseInt(data) {\n        const { Err: E } = DER;\n        if (data.length < 2 || data[0] !== 0x02)\n            throw new E('Invalid signature integer tag');\n        const len = data[1];\n        const res = data.subarray(2, len + 2);\n        if (!len || res.length !== len)\n            throw new E('Invalid signature integer: wrong length');\n        // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n        // since we always use positive integers here. It must always be empty:\n        // - add zero byte if exists\n        // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n        if (res[0] & 0b10000000)\n            throw new E('Invalid signature integer: negative');\n        if (res[0] === 0x00 && !(res[1] & 0b10000000))\n            throw new E('Invalid signature integer: unnecessary leading zero');\n        return { d: b2n(res), l: data.subarray(len + 2) }; // d is data, l is left\n    },\n    toSig(hex) {\n        // parse DER signature\n        const { Err: E } = DER;\n        const data = typeof hex === 'string' ? h2b(hex) : hex;\n        if (!(data instanceof Uint8Array))\n            throw new Error('ui8a expected');\n        let l = data.length;\n        if (l < 2 || data[0] != 0x30)\n            throw new E('Invalid signature tag');\n        if (data[1] !== l - 2)\n            throw new E('Invalid signature: incorrect length');\n        const { d: r, l: sBytes } = DER._parseInt(data.subarray(2));\n        const { d: s, l: rBytesLeft } = DER._parseInt(sBytes);\n        if (rBytesLeft.length)\n            throw new E('Invalid signature: left bytes after parsing');\n        return { r, s };\n    },\n    hexFromSig(sig) {\n        // Add leading zero if first byte has negative bit enabled. More details in '_parseInt'\n        const slice = (s) => (Number.parseInt(s[0], 16) & 0b1000 ? '00' + s : s);\n        const h = (num) => {\n            const hex = num.toString(16);\n            return hex.length & 1 ? `0${hex}` : hex;\n        };\n        const s = slice(h(sig.s));\n        const r = slice(h(sig.r));\n        const shl = s.length / 2;\n        const rhl = r.length / 2;\n        const sl = h(shl);\n        const rl = h(rhl);\n        return `30${h(rhl + shl + 4)}02${rl}${r}02${sl}${s}`;\n    },\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\nexport function weierstrassPoints(opts) {\n    const CURVE = validatePointOpts(opts);\n    const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n    const toBytes = CURVE.toBytes ||\n        ((_c, point, _isCompressed) => {\n            const a = point.toAffine();\n            return ut.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n        });\n    const fromBytes = CURVE.fromBytes ||\n        ((bytes) => {\n            // const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n            const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n            const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n            return { x, y };\n        });\n    /**\n     * y² = x³ + ax + b: Short weierstrass curve formula\n     * @returns y²\n     */\n    function weierstrassEquation(x) {\n        const { a, b } = CURVE;\n        const x2 = Fp.sqr(x); // x * x\n        const x3 = Fp.mul(x2, x); // x2 * x\n        return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n    }\n    // Validate whether the passed curve params are valid.\n    // We check if curve equation works for generator point.\n    // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n    // ProjectivePoint class has not been initialized yet.\n    if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n        throw new Error('bad generator point: equation left != right');\n    // Valid group elements reside in range 1..n-1\n    function isWithinCurveOrder(num) {\n        return typeof num === 'bigint' && _0n < num && num < CURVE.n;\n    }\n    function assertGE(num) {\n        if (!isWithinCurveOrder(num))\n            throw new Error('Expected valid bigint: 0 < bigint < curve.n');\n    }\n    // Validates if priv key is valid and converts it to bigint.\n    // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n    function normPrivateKeyToScalar(key) {\n        const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n } = CURVE;\n        if (lengths && typeof key !== 'bigint') {\n            if (key instanceof Uint8Array)\n                key = ut.bytesToHex(key);\n            // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n            if (typeof key !== 'string' || !lengths.includes(key.length))\n                throw new Error('Invalid key');\n            key = key.padStart(nByteLength * 2, '0');\n        }\n        let num;\n        try {\n            num =\n                typeof key === 'bigint'\n                    ? key\n                    : ut.bytesToNumberBE(ensureBytes('private key', key, nByteLength));\n        }\n        catch (error) {\n            throw new Error(`private key must be ${nByteLength} bytes, hex or bigint, not ${typeof key}`);\n        }\n        if (wrapPrivateKey)\n            num = mod.mod(num, n); // disabled by default, enabled for BLS\n        assertGE(num); // num in range [1..N-1]\n        return num;\n    }\n    const pointPrecomputes = new Map();\n    function assertPrjPoint(other) {\n        if (!(other instanceof Point))\n            throw new Error('ProjectivePoint expected');\n    }\n    /**\n     * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n     * Default Point works in 2d / affine coordinates: (x, y)\n     * We're doing calculations in projective, because its operations don't require costly inversion.\n     */\n    class Point {\n        constructor(px, py, pz) {\n            this.px = px;\n            this.py = py;\n            this.pz = pz;\n            if (px == null || !Fp.isValid(px))\n                throw new Error('x required');\n            if (py == null || !Fp.isValid(py))\n                throw new Error('y required');\n            if (pz == null || !Fp.isValid(pz))\n                throw new Error('z required');\n        }\n        // Does not validate if the point is on-curve.\n        // Use fromHex instead, or call assertValidity() later.\n        static fromAffine(p) {\n            const { x, y } = p || {};\n            if (!p || !Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('invalid affine point');\n            if (p instanceof Point)\n                throw new Error('projective point not allowed');\n            const is0 = (i) => Fp.eql(i, Fp.ZERO);\n            // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n            if (is0(x) && is0(y))\n                return Point.ZERO;\n            return new Point(x, y, Fp.ONE);\n        }\n        get x() {\n            return this.toAffine().x;\n        }\n        get y() {\n            return this.toAffine().y;\n        }\n        /**\n         * Takes a bunch of Projective Points but executes only one\n         * inversion on all of them. Inversion is very slow operation,\n         * so this improves performance massively.\n         * Optimization: converts a list of projective points to a list of identical points with Z=1.\n         */\n        static normalizeZ(points) {\n            const toInv = Fp.invertBatch(points.map((p) => p.pz));\n            return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n        }\n        /**\n         * Converts hash string or Uint8Array to Point.\n         * @param hex short/long ECDSA hex\n         */\n        static fromHex(hex) {\n            const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n            P.assertValidity();\n            return P;\n        }\n        // Multiplies generator point by privateKey.\n        static fromPrivateKey(privateKey) {\n            return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n        }\n        // \"Private method\", don't use it directly\n        _setWindowSize(windowSize) {\n            this._WINDOW_SIZE = windowSize;\n            pointPrecomputes.delete(this);\n        }\n        // A point on curve is valid if it conforms to equation.\n        assertValidity() {\n            if (this.is0()) {\n                // (0, 1, 0) aka ZERO is invalid in most contexts.\n                // In BLS, ZERO can be serialized, so we allow it.\n                // (0, 0, 0) is wrong representation of ZERO and is always invalid.\n                if (CURVE.allowInfinityPoint && !Fp.is0(this.py))\n                    return;\n                throw new Error('bad point: ZERO');\n            }\n            // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n            const { x, y } = this.toAffine();\n            // Check if x, y are valid field elements\n            if (!Fp.isValid(x) || !Fp.isValid(y))\n                throw new Error('bad point: x or y not FE');\n            const left = Fp.sqr(y); // y²\n            const right = weierstrassEquation(x); // x³ + ax + b\n            if (!Fp.eql(left, right))\n                throw new Error('bad point: equation left != right');\n            if (!this.isTorsionFree())\n                throw new Error('bad point: not in prime-order subgroup');\n        }\n        hasEvenY() {\n            const { y } = this.toAffine();\n            if (Fp.isOdd)\n                return !Fp.isOdd(y);\n            throw new Error(\"Field doesn't support isOdd\");\n        }\n        /**\n         * Compare one point to another.\n         */\n        equals(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n            const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n            return U1 && U2;\n        }\n        /**\n         * Flips point to one corresponding to (x, -y) in Affine coordinates.\n         */\n        negate() {\n            return new Point(this.px, Fp.neg(this.py), this.pz);\n        }\n        // Renes-Costello-Batina exception-free doubling formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 3\n        // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n        double() {\n            const { a, b } = CURVE;\n            const b3 = Fp.mul(b, _3n);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            let t0 = Fp.mul(X1, X1); // step 1\n            let t1 = Fp.mul(Y1, Y1);\n            let t2 = Fp.mul(Z1, Z1);\n            let t3 = Fp.mul(X1, Y1);\n            t3 = Fp.add(t3, t3); // step 5\n            Z3 = Fp.mul(X1, Z1);\n            Z3 = Fp.add(Z3, Z3);\n            X3 = Fp.mul(a, Z3);\n            Y3 = Fp.mul(b3, t2);\n            Y3 = Fp.add(X3, Y3); // step 10\n            X3 = Fp.sub(t1, Y3);\n            Y3 = Fp.add(t1, Y3);\n            Y3 = Fp.mul(X3, Y3);\n            X3 = Fp.mul(t3, X3);\n            Z3 = Fp.mul(b3, Z3); // step 15\n            t2 = Fp.mul(a, t2);\n            t3 = Fp.sub(t0, t2);\n            t3 = Fp.mul(a, t3);\n            t3 = Fp.add(t3, Z3);\n            Z3 = Fp.add(t0, t0); // step 20\n            t0 = Fp.add(Z3, t0);\n            t0 = Fp.add(t0, t2);\n            t0 = Fp.mul(t0, t3);\n            Y3 = Fp.add(Y3, t0);\n            t2 = Fp.mul(Y1, Z1); // step 25\n            t2 = Fp.add(t2, t2);\n            t0 = Fp.mul(t2, t3);\n            X3 = Fp.sub(X3, t0);\n            Z3 = Fp.mul(t2, t1);\n            Z3 = Fp.add(Z3, Z3); // step 30\n            Z3 = Fp.add(Z3, Z3);\n            return new Point(X3, Y3, Z3);\n        }\n        // Renes-Costello-Batina exception-free addition formula.\n        // There is 30% faster Jacobian formula, but it is not complete.\n        // https://eprint.iacr.org/2015/1060, algorithm 1\n        // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n        add(other) {\n            assertPrjPoint(other);\n            const { px: X1, py: Y1, pz: Z1 } = this;\n            const { px: X2, py: Y2, pz: Z2 } = other;\n            let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n            const a = CURVE.a;\n            const b3 = Fp.mul(CURVE.b, _3n);\n            let t0 = Fp.mul(X1, X2); // step 1\n            let t1 = Fp.mul(Y1, Y2);\n            let t2 = Fp.mul(Z1, Z2);\n            let t3 = Fp.add(X1, Y1);\n            let t4 = Fp.add(X2, Y2); // step 5\n            t3 = Fp.mul(t3, t4);\n            t4 = Fp.add(t0, t1);\n            t3 = Fp.sub(t3, t4);\n            t4 = Fp.add(X1, Z1);\n            let t5 = Fp.add(X2, Z2); // step 10\n            t4 = Fp.mul(t4, t5);\n            t5 = Fp.add(t0, t2);\n            t4 = Fp.sub(t4, t5);\n            t5 = Fp.add(Y1, Z1);\n            X3 = Fp.add(Y2, Z2); // step 15\n            t5 = Fp.mul(t5, X3);\n            X3 = Fp.add(t1, t2);\n            t5 = Fp.sub(t5, X3);\n            Z3 = Fp.mul(a, t4);\n            X3 = Fp.mul(b3, t2); // step 20\n            Z3 = Fp.add(X3, Z3);\n            X3 = Fp.sub(t1, Z3);\n            Z3 = Fp.add(t1, Z3);\n            Y3 = Fp.mul(X3, Z3);\n            t1 = Fp.add(t0, t0); // step 25\n            t1 = Fp.add(t1, t0);\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.mul(b3, t4);\n            t1 = Fp.add(t1, t2);\n            t2 = Fp.sub(t0, t2); // step 30\n            t2 = Fp.mul(a, t2);\n            t4 = Fp.add(t4, t2);\n            t0 = Fp.mul(t1, t4);\n            Y3 = Fp.add(Y3, t0);\n            t0 = Fp.mul(t5, t4); // step 35\n            X3 = Fp.mul(t3, X3);\n            X3 = Fp.sub(X3, t0);\n            t0 = Fp.mul(t3, t1);\n            Z3 = Fp.mul(t5, Z3);\n            Z3 = Fp.add(Z3, t0); // step 40\n            return new Point(X3, Y3, Z3);\n        }\n        subtract(other) {\n            return this.add(other.negate());\n        }\n        is0() {\n            return this.equals(Point.ZERO);\n        }\n        wNAF(n) {\n            return wnaf.wNAFCached(this, pointPrecomputes, n, (comp) => {\n                const toInv = Fp.invertBatch(comp.map((p) => p.pz));\n                return comp.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n            });\n        }\n        /**\n         * Non-constant-time multiplication. Uses double-and-add algorithm.\n         * It's faster, but should only be used when you don't care about\n         * an exposed private key e.g. sig verification, which works over *public* keys.\n         */\n        multiplyUnsafe(n) {\n            const I = Point.ZERO;\n            if (n === _0n)\n                return I;\n            assertGE(n); // Will throw on 0\n            if (n === _1n)\n                return this;\n            const { endo } = CURVE;\n            if (!endo)\n                return wnaf.unsafeLadder(this, n);\n            // Apply endomorphism\n            let { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n            let k1p = I;\n            let k2p = I;\n            let d = this;\n            while (k1 > _0n || k2 > _0n) {\n                if (k1 & _1n)\n                    k1p = k1p.add(d);\n                if (k2 & _1n)\n                    k2p = k2p.add(d);\n                d = d.double();\n                k1 >>= _1n;\n                k2 >>= _1n;\n            }\n            if (k1neg)\n                k1p = k1p.negate();\n            if (k2neg)\n                k2p = k2p.negate();\n            k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n            return k1p.add(k2p);\n        }\n        /**\n         * Constant time multiplication.\n         * Uses wNAF method. Windowed method may be 10% faster,\n         * but takes 2x longer to generate and consumes 2x memory.\n         * Uses precomputes when available.\n         * Uses endomorphism for Koblitz curves.\n         * @param scalar by which the point would be multiplied\n         * @returns New point\n         */\n        multiply(scalar) {\n            assertGE(scalar);\n            let n = scalar;\n            let point, fake; // Fake point is used to const-time mult\n            const { endo } = CURVE;\n            if (endo) {\n                const { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n                let { p: k1p, f: f1p } = this.wNAF(k1);\n                let { p: k2p, f: f2p } = this.wNAF(k2);\n                k1p = wnaf.constTimeNegate(k1neg, k1p);\n                k2p = wnaf.constTimeNegate(k2neg, k2p);\n                k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n                point = k1p.add(k2p);\n                fake = f1p.add(f2p);\n            }\n            else {\n                const { p, f } = this.wNAF(n);\n                point = p;\n                fake = f;\n            }\n            // Normalize `z` for both points, but return only real one\n            return Point.normalizeZ([point, fake])[0];\n        }\n        /**\n         * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n         * Not using Strauss-Shamir trick: precomputation tables are faster.\n         * The trick could be useful if both P and Q are not G (not in our case).\n         * @returns non-zero affine point\n         */\n        multiplyAndAddUnsafe(Q, a, b) {\n            const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n            const mul = (P, a // Select faster multiply() method\n            ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n            const sum = mul(this, a).add(mul(Q, b));\n            return sum.is0() ? undefined : sum;\n        }\n        // Converts Projective point to affine (x, y) coordinates.\n        // Can accept precomputed Z^-1 - for example, from invertBatch.\n        // (x, y, z) ∋ (x=x/z, y=y/z)\n        toAffine(iz) {\n            const { px: x, py: y, pz: z } = this;\n            const is0 = this.is0();\n            // If invZ was 0, we return zero point. However we still want to execute\n            // all operations, so we replace invZ with a random number, 1.\n            if (iz == null)\n                iz = is0 ? Fp.ONE : Fp.inv(z);\n            const ax = Fp.mul(x, iz);\n            const ay = Fp.mul(y, iz);\n            const zz = Fp.mul(z, iz);\n            if (is0)\n                return { x: Fp.ZERO, y: Fp.ZERO };\n            if (!Fp.eql(zz, Fp.ONE))\n                throw new Error('invZ was invalid');\n            return { x: ax, y: ay };\n        }\n        isTorsionFree() {\n            const { h: cofactor, isTorsionFree } = CURVE;\n            if (cofactor === _1n)\n                return true; // No subgroups, always torsion-free\n            if (isTorsionFree)\n                return isTorsionFree(Point, this);\n            throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n        }\n        clearCofactor() {\n            const { h: cofactor, clearCofactor } = CURVE;\n            if (cofactor === _1n)\n                return this; // Fast-path\n            if (clearCofactor)\n                return clearCofactor(Point, this);\n            return this.multiplyUnsafe(CURVE.h);\n        }\n        toRawBytes(isCompressed = true) {\n            this.assertValidity();\n            return toBytes(Point, this, isCompressed);\n        }\n        toHex(isCompressed = true) {\n            return ut.bytesToHex(this.toRawBytes(isCompressed));\n        }\n    }\n    Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n    const _bits = CURVE.nBitLength;\n    const wnaf = wNAF(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n    // Validate if generator point is on curve\n    return {\n        CURVE,\n        ProjectivePoint: Point,\n        normPrivateKeyToScalar,\n        weierstrassEquation,\n        isWithinCurveOrder,\n    };\n}\nfunction validateOpts(curve) {\n    const opts = validateBasic(curve);\n    ut.validateObject(opts, {\n        hash: 'hash',\n        hmac: 'function',\n        randomBytes: 'function',\n    }, {\n        bits2int: 'function',\n        bits2int_modN: 'function',\n        lowS: 'boolean',\n    });\n    return Object.freeze({ lowS: true, ...opts });\n}\nexport function weierstrass(curveDef) {\n    const CURVE = validateOpts(curveDef);\n    const { Fp, n: CURVE_ORDER } = CURVE;\n    const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n    const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n    function isValidFieldElement(num) {\n        return _0n < num && num < Fp.ORDER; // 0 is banned since it's not invertible FE\n    }\n    function modN(a) {\n        return mod.mod(a, CURVE_ORDER);\n    }\n    function invN(a) {\n        return mod.invert(a, CURVE_ORDER);\n    }\n    const { ProjectivePoint: Point, normPrivateKeyToScalar, weierstrassEquation, isWithinCurveOrder, } = weierstrassPoints({\n        ...CURVE,\n        toBytes(_c, point, isCompressed) {\n            const a = point.toAffine();\n            const x = Fp.toBytes(a.x);\n            const cat = ut.concatBytes;\n            if (isCompressed) {\n                return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n            }\n            else {\n                return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n            }\n        },\n        fromBytes(bytes) {\n            const len = bytes.length;\n            const head = bytes[0];\n            const tail = bytes.subarray(1);\n            // this.assertValidity() is done inside of fromHex\n            if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n                const x = ut.bytesToNumberBE(tail);\n                if (!isValidFieldElement(x))\n                    throw new Error('Point is not on curve');\n                const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n                let y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n                const isYOdd = (y & _1n) === _1n;\n                // ECDSA\n                const isHeadOdd = (head & 1) === 1;\n                if (isHeadOdd !== isYOdd)\n                    y = Fp.neg(y);\n                return { x, y };\n            }\n            else if (len === uncompressedLen && head === 0x04) {\n                const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n                const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n                return { x, y };\n            }\n            else {\n                throw new Error(`Point of length ${len} was invalid. Expected ${compressedLen} compressed bytes or ${uncompressedLen} uncompressed bytes`);\n            }\n        },\n    });\n    const numToNByteStr = (num) => ut.bytesToHex(ut.numberToBytesBE(num, CURVE.nByteLength));\n    function isBiggerThanHalfOrder(number) {\n        const HALF = CURVE_ORDER >> _1n;\n        return number > HALF;\n    }\n    function normalizeS(s) {\n        return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n    }\n    // slice bytes num\n    const slcNum = (b, from, to) => ut.bytesToNumberBE(b.slice(from, to));\n    /**\n     * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n     */\n    class Signature {\n        constructor(r, s, recovery) {\n            this.r = r;\n            this.s = s;\n            this.recovery = recovery;\n            this.assertValidity();\n        }\n        // pair (bytes of r, bytes of s)\n        static fromCompact(hex) {\n            const l = CURVE.nByteLength;\n            hex = ensureBytes('compactSignature', hex, l * 2);\n            return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n        }\n        // DER encoded ECDSA signature\n        // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n        static fromDER(hex) {\n            const { r, s } = DER.toSig(ensureBytes('DER', hex));\n            return new Signature(r, s);\n        }\n        assertValidity() {\n            // can use assertGE here\n            if (!isWithinCurveOrder(this.r))\n                throw new Error('r must be 0 < r < CURVE.n');\n            if (!isWithinCurveOrder(this.s))\n                throw new Error('s must be 0 < s < CURVE.n');\n        }\n        addRecoveryBit(recovery) {\n            return new Signature(this.r, this.s, recovery);\n        }\n        recoverPublicKey(msgHash) {\n            const { r, s, recovery: rec } = this;\n            const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n            if (rec == null || ![0, 1, 2, 3].includes(rec))\n                throw new Error('recovery id invalid');\n            const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n            if (radj >= Fp.ORDER)\n                throw new Error('recovery id 2 or 3 invalid');\n            const prefix = (rec & 1) === 0 ? '02' : '03';\n            const R = Point.fromHex(prefix + numToNByteStr(radj));\n            const ir = invN(radj); // r^-1\n            const u1 = modN(-h * ir); // -hr^-1\n            const u2 = modN(s * ir); // sr^-1\n            const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n            if (!Q)\n                throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n            Q.assertValidity();\n            return Q;\n        }\n        // Signatures should be low-s, to prevent malleability.\n        hasHighS() {\n            return isBiggerThanHalfOrder(this.s);\n        }\n        normalizeS() {\n            return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n        }\n        // DER-encoded\n        toDERRawBytes() {\n            return ut.hexToBytes(this.toDERHex());\n        }\n        toDERHex() {\n            return DER.hexFromSig({ r: this.r, s: this.s });\n        }\n        // padded bytes of r, then padded bytes of s\n        toCompactRawBytes() {\n            return ut.hexToBytes(this.toCompactHex());\n        }\n        toCompactHex() {\n            return numToNByteStr(this.r) + numToNByteStr(this.s);\n        }\n    }\n    const utils = {\n        isValidPrivateKey(privateKey) {\n            try {\n                normPrivateKeyToScalar(privateKey);\n                return true;\n            }\n            catch (error) {\n                return false;\n            }\n        },\n        normPrivateKeyToScalar: normPrivateKeyToScalar,\n        /**\n         * Produces cryptographically secure private key from random of size\n         * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n         */\n        randomPrivateKey: () => {\n            const length = mod.getMinHashLength(CURVE.n);\n            return mod.mapHashToField(CURVE.randomBytes(length), CURVE.n);\n        },\n        /**\n         * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n         * Allows to massively speed-up `point.multiply(scalar)`.\n         * @returns cached point\n         * @example\n         * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n         * fast.multiply(privKey); // much faster ECDH now\n         */\n        precompute(windowSize = 8, point = Point.BASE) {\n            point._setWindowSize(windowSize);\n            point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n            return point;\n        },\n    };\n    /**\n     * Computes public key for a private key. Checks for validity of the private key.\n     * @param privateKey private key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns Public key, full when isCompressed=false; short when isCompressed=true\n     */\n    function getPublicKey(privateKey, isCompressed = true) {\n        return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n    }\n    /**\n     * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n     */\n    function isProbPub(item) {\n        const arr = item instanceof Uint8Array;\n        const str = typeof item === 'string';\n        const len = (arr || str) && item.length;\n        if (arr)\n            return len === compressedLen || len === uncompressedLen;\n        if (str)\n            return len === 2 * compressedLen || len === 2 * uncompressedLen;\n        if (item instanceof Point)\n            return true;\n        return false;\n    }\n    /**\n     * ECDH (Elliptic Curve Diffie Hellman).\n     * Computes shared public key from private key and public key.\n     * Checks: 1) private key validity 2) shared key is on-curve.\n     * Does NOT hash the result.\n     * @param privateA private key\n     * @param publicB different public key\n     * @param isCompressed whether to return compact (default), or full key\n     * @returns shared public key\n     */\n    function getSharedSecret(privateA, publicB, isCompressed = true) {\n        if (isProbPub(privateA))\n            throw new Error('first arg must be private key');\n        if (!isProbPub(publicB))\n            throw new Error('second arg must be public key');\n        const b = Point.fromHex(publicB); // check for being on-curve\n        return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n    }\n    // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n    // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n    // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n    // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n    const bits2int = CURVE.bits2int ||\n        function (bytes) {\n            // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n            // for some cases, since bytes.length * 8 is not actual bitLength.\n            const num = ut.bytesToNumberBE(bytes); // check for == u8 done here\n            const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n            return delta > 0 ? num >> BigInt(delta) : num;\n        };\n    const bits2int_modN = CURVE.bits2int_modN ||\n        function (bytes) {\n            return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n        };\n    // NOTE: pads output with zero as per spec\n    const ORDER_MASK = ut.bitMask(CURVE.nBitLength);\n    /**\n     * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n     */\n    function int2octets(num) {\n        if (typeof num !== 'bigint')\n            throw new Error('bigint expected');\n        if (!(_0n <= num && num < ORDER_MASK))\n            throw new Error(`bigint expected < 2^${CURVE.nBitLength}`);\n        // works with order, can have different size than numToField!\n        return ut.numberToBytesBE(num, CURVE.nByteLength);\n    }\n    // Steps A, D of RFC6979 3.2\n    // Creates RFC6979 seed; converts msg/privKey to numbers.\n    // Used only in sign, not in verify.\n    // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order, this will be wrong at least for P521.\n    // Also it can be bigger for P224 + SHA256\n    function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n        if (['recovered', 'canonical'].some((k) => k in opts))\n            throw new Error('sign() legacy options not supported');\n        const { hash, randomBytes } = CURVE;\n        let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n        if (lowS == null)\n            lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n        msgHash = ensureBytes('msgHash', msgHash);\n        if (prehash)\n            msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n        // We can't later call bits2octets, since nested bits2int is broken for curves\n        // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n        // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n        const h1int = bits2int_modN(msgHash);\n        const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n        const seedArgs = [int2octets(d), int2octets(h1int)];\n        // extraEntropy. RFC6979 3.6: additional k' (optional).\n        if (ent != null) {\n            // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n            const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n            seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n        }\n        const seed = ut.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n        const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n        // Converts signature params into point w r/s, checks result for validity.\n        function k2sig(kBytes) {\n            // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n            const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n            if (!isWithinCurveOrder(k))\n                return; // Important: all mod() calls here must be done over N\n            const ik = invN(k); // k^-1 mod n\n            const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n            const r = modN(q.x); // r = q.x mod n\n            if (r === _0n)\n                return;\n            // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n            // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n            // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n            const s = modN(ik * modN(m + r * d)); // Not using blinding here\n            if (s === _0n)\n                return;\n            let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n            let normS = s;\n            if (lowS && isBiggerThanHalfOrder(s)) {\n                normS = normalizeS(s); // if lowS was passed, ensure s is always\n                recovery ^= 1; // // in the bottom half of N\n            }\n            return new Signature(r, normS, recovery); // use normS, not s\n        }\n        return { seed, k2sig };\n    }\n    const defaultSigOpts = { lowS: CURVE.lowS, prehash: false };\n    const defaultVerOpts = { lowS: CURVE.lowS, prehash: false };\n    /**\n     * Signs message hash with a private key.\n     * ```\n     * sign(m, d, k) where\n     *   (x, y) = G × k\n     *   r = x mod n\n     *   s = (m + dr)/k mod n\n     * ```\n     * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n     * @param privKey private key\n     * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n     * @returns signature with recovery param\n     */\n    function sign(msgHash, privKey, opts = defaultSigOpts) {\n        const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n        const C = CURVE;\n        const drbg = ut.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n        return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n    }\n    // Enable precomputes. Slows down first publicKey computation by 20ms.\n    Point.BASE._setWindowSize(8);\n    // utils.precompute(8, ProjectivePoint.BASE)\n    /**\n     * Verifies a signature against message hash and public key.\n     * Rejects lowS signatures by default: to override,\n     * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n     *\n     * ```\n     * verify(r, s, h, P) where\n     *   U1 = hs^-1 mod n\n     *   U2 = rs^-1 mod n\n     *   R = U1⋅G - U2⋅P\n     *   mod(R.x, n) == r\n     * ```\n     */\n    function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n        const sg = signature;\n        msgHash = ensureBytes('msgHash', msgHash);\n        publicKey = ensureBytes('publicKey', publicKey);\n        if ('strict' in opts)\n            throw new Error('options.strict was renamed to lowS');\n        const { lowS, prehash } = opts;\n        let _sig = undefined;\n        let P;\n        try {\n            if (typeof sg === 'string' || sg instanceof Uint8Array) {\n                // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n                // Since DER can also be 2*nByteLength bytes, we check for it first.\n                try {\n                    _sig = Signature.fromDER(sg);\n                }\n                catch (derError) {\n                    if (!(derError instanceof DER.Err))\n                        throw derError;\n                    _sig = Signature.fromCompact(sg);\n                }\n            }\n            else if (typeof sg === 'object' && typeof sg.r === 'bigint' && typeof sg.s === 'bigint') {\n                const { r, s } = sg;\n                _sig = new Signature(r, s);\n            }\n            else {\n                throw new Error('PARSE');\n            }\n            P = Point.fromHex(publicKey);\n        }\n        catch (error) {\n            if (error.message === 'PARSE')\n                throw new Error(`signature must be Signature instance, Uint8Array or hex string`);\n            return false;\n        }\n        if (lowS && _sig.hasHighS())\n            return false;\n        if (prehash)\n            msgHash = CURVE.hash(msgHash);\n        const { r, s } = _sig;\n        const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n        const is = invN(s); // s^-1\n        const u1 = modN(h * is); // u1 = hs^-1 mod n\n        const u2 = modN(r * is); // u2 = rs^-1 mod n\n        const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n        if (!R)\n            return false;\n        const v = modN(R.x);\n        return v === r;\n    }\n    return {\n        CURVE,\n        getPublicKey,\n        getSharedSecret,\n        sign,\n        verify,\n        ProjectivePoint: Point,\n        Signature,\n        utils,\n    };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio(Fp, Z) {\n    // Generic implementation\n    const q = Fp.ORDER;\n    let l = _0n;\n    for (let o = q - _1n; o % _2n === _0n; o /= _2n)\n        l += _1n;\n    const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n    // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n    // 2n ** c1 == 2n << (c1-1)\n    const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n    const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n    const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n    const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n    const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n    const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n    const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n    const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n    let sqrtRatio = (u, v) => {\n        let tv1 = c6; // 1. tv1 = c6\n        let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n        let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n        tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n        let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n        tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n        tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n        tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n        tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n        let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n        tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n        let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n        tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n        tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n        tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n        tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n        // 17. for i in (c1, c1 - 1, ..., 2):\n        for (let i = c1; i > _1n; i--) {\n            let tv5 = i - _2n; // 18.    tv5 = i - 2\n            tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n            let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n            const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n            tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n            tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n            tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n            tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n            tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n        }\n        return { isValid: isQR, value: tv3 };\n    };\n    if (Fp.ORDER % _4n === _3n) {\n        // sqrt_ratio_3mod4(u, v)\n        const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n        const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n        sqrtRatio = (u, v) => {\n            let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n            const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n            tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n            let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n            y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n            const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n            const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n            const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n            let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n            return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n        };\n    }\n    // No curves uses that\n    // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n    return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU(Fp, opts) {\n    mod.validateField(Fp);\n    if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n        throw new Error('mapToCurveSimpleSWU: invalid opts');\n    const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n    if (!Fp.isOdd)\n        throw new Error('Fp.isOdd is not implemented!');\n    // Input: u, an element of F.\n    // Output: (x, y), a point on E.\n    return (u) => {\n        // prettier-ignore\n        let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n        tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n        tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n        tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n        tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n        tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n        tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n        tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n        tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n        tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n        tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n        tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n        tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n        tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n        tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n        tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n        tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n        x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n        const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n        y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n        y = Fp.mul(y, value); // 20.   y = y * y1\n        x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n        y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n        const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n        y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n        x = Fp.div(x, tv4); // 25.   x = x / tv4\n        return { x, y };\n    };\n}\n//# sourceMappingURL=weierstrass.js.map", "import { hash as assertHash, bytes as assertBytes, exists as assertExists } from './_assert.js';\nimport { Hash, toBytes } from './utils.js';\n// HMAC (RFC 2104)\nexport class HMAC extends Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        assertHash(hash);\n        const key = toBytes(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        pad.fill(0);\n    }\n    update(buf) {\n        assertExists(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        assertExists(this);\n        assertBytes(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n */\nexport const hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac';\nimport { concatBytes, randomBytes } from '@noble/hashes/utils';\nimport { weierstrass } from './abstract/weierstrass.js';\n// connects noble-curves to noble-hashes\nexport function getHash(hash) {\n    return {\n        hash,\n        hmac: (key, ...msgs) => hmac(hash, key, concatBytes(...msgs)),\n        randomBytes,\n    };\n}\nexport function createCurve(curveDef, defHash) {\n    const create = (hash) => weierstrass({ ...curveDef, ...getHash(hash) });\n    return Object.freeze({ ...create(defHash), create });\n}\n//# sourceMappingURL=_shortw_utils.js.map", "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha256';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { Field, mod, pow2 } from './abstract/modular.js';\nimport { mapToCurveSimpleSWU } from './abstract/weierstrass.js';\nimport { bytesToNumberBE, concatBytes, ensureBytes, numberToBytesBE } from './abstract/utils.js';\nimport { createHasher, isogenyMap } from './abstract/hash-to-curve.js';\nimport { createCurve } from './_shortw_utils.js';\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n    const P = secp256k1P;\n    // prettier-ignore\n    const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n    // prettier-ignore\n    const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n    const b2 = (y * y * y) % P; // x^3, 11\n    const b3 = (b2 * b2 * y) % P; // x^7\n    const b6 = (pow2(b3, _3n, P) * b3) % P;\n    const b9 = (pow2(b6, _3n, P) * b3) % P;\n    const b11 = (pow2(b9, _2n, P) * b2) % P;\n    const b22 = (pow2(b11, _11n, P) * b11) % P;\n    const b44 = (pow2(b22, _22n, P) * b22) % P;\n    const b88 = (pow2(b44, _44n, P) * b44) % P;\n    const b176 = (pow2(b88, _88n, P) * b88) % P;\n    const b220 = (pow2(b176, _44n, P) * b44) % P;\n    const b223 = (pow2(b220, _3n, P) * b3) % P;\n    const t1 = (pow2(b223, _23n, P) * b22) % P;\n    const t2 = (pow2(t1, _6n, P) * b2) % P;\n    const root = pow2(t2, _2n, P);\n    if (!Fp.eql(Fp.sqr(root), y))\n        throw new Error('Cannot find square root');\n    return root;\n}\nconst Fp = Field(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\nexport const secp256k1 = createCurve({\n    a: BigInt(0),\n    b: BigInt(7),\n    Fp,\n    n: secp256k1N,\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1),\n    lowS: true,\n    /**\n     * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n     * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n     * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n     * Explanation: https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066\n     */\n    endo: {\n        beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n        splitScalar: (k) => {\n            const n = secp256k1N;\n            const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n            const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n            const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n            const b2 = a1;\n            const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n            const c1 = divNearest(b2 * k, n);\n            const c2 = divNearest(-b1 * k, n);\n            let k1 = mod(k - c1 * a1 - c2 * a2, n);\n            let k2 = mod(-c1 * b1 - c2 * b2, n);\n            const k1neg = k1 > POW_2_128;\n            const k2neg = k2 > POW_2_128;\n            if (k1neg)\n                k1 = n - k1;\n            if (k2neg)\n                k2 = n - k2;\n            if (k1 > POW_2_128 || k2 > POW_2_128) {\n                throw new Error('splitScalar: Endomorphism failed, k=' + k);\n            }\n            return { k1neg, k1, k2neg, k2 };\n        },\n    },\n}, sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\nconst fe = (x) => typeof x === 'bigint' && _0n < x && x < secp256k1P;\nconst ge = (x) => typeof x === 'bigint' && _0n < x && x < secp256k1N;\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n    let tagP = TAGGED_HASH_PREFIXES[tag];\n    if (tagP === undefined) {\n        const tagH = sha256(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n        tagP = concatBytes(tagH, tagH);\n        TAGGED_HASH_PREFIXES[tag] = tagP;\n    }\n    return sha256(concatBytes(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n) => numberToBytesBE(n, 32);\nconst modP = (x) => mod(x, secp256k1P);\nconst modN = (x) => mod(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n    let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n    let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n    const scalar = p.hasEvenY() ? d_ : modN(-d_);\n    return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n    if (!fe(x))\n        throw new Error('bad x: need 0 < x < p'); // Fail if x ≥ p.\n    const xx = modP(x * x);\n    const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n    let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n    if (y % _2n !== _0n)\n        y = modP(-y); // Return the unique point P such that x(P) = x and\n    const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n    p.assertValidity();\n    return p;\n}\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n    return modN(bytesToNumberBE(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n    return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = randomBytes(32)) {\n    const m = ensureBytes('message', message);\n    const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n    const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n    const t = numTo32b(d ^ bytesToNumberBE(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n    const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n    const k_ = modN(bytesToNumberBE(rand)); // Let k' = int(rand) mod n\n    if (k_ === _0n)\n        throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n    const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n    const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n    const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n    sig.set(rx, 0);\n    sig.set(numTo32b(modN(k + e * d)), 32);\n    // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n    if (!schnorrVerify(sig, m, px))\n        throw new Error('sign: Invalid signature produced');\n    return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n    const sig = ensureBytes('signature', signature, 64);\n    const m = ensureBytes('message', message);\n    const pub = ensureBytes('publicKey', publicKey, 32);\n    try {\n        const P = lift_x(bytesToNumberBE(pub)); // P = lift_x(int(pk)); fail if that fails\n        const r = bytesToNumberBE(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n        if (!fe(r))\n            return false;\n        const s = bytesToNumberBE(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n        if (!ge(s))\n            return false;\n        const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n        const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n        if (!R || !R.hasEvenY() || R.toAffine().x !== r)\n            return false; // -eP == (n-e)P\n        return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n    }\n    catch (error) {\n        return false;\n    }\n}\nexport const schnorr = /* @__PURE__ */ (() => ({\n    getPublicKey: schnorrGetPublicKey,\n    sign: schnorrSign,\n    verify: schnorrVerify,\n    utils: {\n        randomPrivateKey: secp256k1.utils.randomPrivateKey,\n        lift_x,\n        pointToBytes,\n        numberToBytesBE,\n        bytesToNumberBE,\n        taggedHash,\n        mod,\n    },\n}))();\nconst isoMap = /* @__PURE__ */ (() => isogenyMap(Fp, [\n    // xNum\n    [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n    ],\n    // xDen\n    [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n    // yNum\n    [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n    ],\n    // yDen\n    [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n    ],\n].map((i) => i.map((j) => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */ (() => mapToCurveSimpleSWU(Fp, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fp.create(BigInt('-11')),\n}))();\nconst htf = /* @__PURE__ */ (() => createHasher(secp256k1.ProjectivePoint, (scalars) => {\n    const { x, y } = mapSWU(Fp.create(scalars[0]));\n    return isoMap(x, y);\n}, {\n    DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n    encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n    p: Fp.ORDER,\n    m: 1,\n    k: 128,\n    expand: 'xmd',\n    hash: sha256,\n}))();\nexport const hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\nexport const encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n//# sourceMappingURL=secp256k1.js.map", null, "function bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new Error('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\n\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nconst u8a = (a) => a instanceof Uint8Array;\n// Cast array to view\nconst createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nconst rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nconst isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!u8a(data))\n        throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n// For runtime check if class implements interface\nclass Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nfunction wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\n\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nclass SHA2 extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        exists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        exists(this);\n        output(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nconst sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\n\n/*! scure-base - MIT License (c) 2022 Paul Miller (paulmillr.com) */\n// Utilities\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n    // Wrap call in closure so JIT can inline calls\n    const wrap = (a, b) => (c) => a(b(c));\n    // Construct chain of args[-1].encode(args[-2].encode([...]))\n    const encode = Array.from(args)\n        .reverse()\n        .reduce((acc, i) => (acc ? wrap(acc, i.encode) : i.encode), undefined);\n    // Construct chain of args[0].decode(args[1].decode(...))\n    const decode = args.reduce((acc, i) => (acc ? wrap(acc, i.decode) : i.decode), undefined);\n    return { encode, decode };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(alphabet) {\n    return {\n        encode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('alphabet.encode input should be an array of numbers');\n            return digits.map((i) => {\n                if (i < 0 || i >= alphabet.length)\n                    throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n                return alphabet[i];\n            });\n        },\n        decode: (input) => {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('alphabet.decode input should be array of strings');\n            return input.map((letter) => {\n                if (typeof letter !== 'string')\n                    throw new Error(`alphabet.decode: not string element=${letter}`);\n                const index = alphabet.indexOf(letter);\n                if (index === -1)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n                return index;\n            });\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n    if (typeof separator !== 'string')\n        throw new Error('join separator should be string');\n    return {\n        encode: (from) => {\n            if (!Array.isArray(from) || (from.length && typeof from[0] !== 'string'))\n                throw new Error('join.encode input should be array of strings');\n            for (let i of from)\n                if (typeof i !== 'string')\n                    throw new Error(`join.encode: non-string input=${i}`);\n            return from.join(separator);\n        },\n        decode: (to) => {\n            if (typeof to !== 'string')\n                throw new Error('join.decode input should be string');\n            return to.split(separator);\n        },\n    };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n    if (typeof chr !== 'string')\n        throw new Error('padding chr should be string');\n    return {\n        encode(data) {\n            if (!Array.isArray(data) || (data.length && typeof data[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of data)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.encode: non-string input=${i}`);\n            while ((data.length * bits) % 8)\n                data.push(chr);\n            return data;\n        },\n        decode(input) {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of input)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.decode: non-string input=${i}`);\n            let end = input.length;\n            if ((end * bits) % 8)\n                throw new Error('Invalid padding: string should have whole number of bytes');\n            for (; end > 0 && input[end - 1] === chr; end--) {\n                if (!(((end - 1) * bits) % 8))\n                    throw new Error('Invalid padding: string has too much padding');\n            }\n            return input.slice(0, end);\n        },\n    };\n}\n/**\n * Slow: O(n^2) time complexity\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix(data, from, to) {\n    // base 1 is impossible\n    if (from < 2)\n        throw new Error(`convertRadix: wrong from=${from}, base cannot be less than 2`);\n    if (to < 2)\n        throw new Error(`convertRadix: wrong to=${to}, base cannot be less than 2`);\n    if (!Array.isArray(data))\n        throw new Error('convertRadix: data should be array');\n    if (!data.length)\n        return [];\n    let pos = 0;\n    const res = [];\n    const digits = Array.from(data);\n    digits.forEach((d) => {\n        if (d < 0 || d >= from)\n            throw new Error(`Wrong integer: ${d}`);\n    });\n    while (true) {\n        let carry = 0;\n        let done = true;\n        for (let i = pos; i < digits.length; i++) {\n            const digit = digits[i];\n            const digitBase = from * carry + digit;\n            if (!Number.isSafeInteger(digitBase) ||\n                (from * carry) / from !== carry ||\n                digitBase - digit !== from * carry) {\n                throw new Error('convertRadix: carry overflow');\n            }\n            carry = digitBase % to;\n            const rounded = Math.floor(digitBase / to);\n            digits[i] = rounded;\n            if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n                throw new Error('convertRadix: carry overflow');\n            if (!done)\n                continue;\n            else if (!rounded)\n                pos = i;\n            else\n                done = false;\n        }\n        res.push(carry);\n        if (done)\n            break;\n    }\n    for (let i = 0; i < data.length - 1 && data[i] === 0; i++)\n        res.push(0);\n    return res.reverse();\n}\nconst gcd = /* @__NO_SIDE_EFFECTS__ */ (a, b) => (!b ? a : gcd(b, a % b));\nconst radix2carry = /*@__NO_SIDE_EFFECTS__ */ (from, to) => from + (to - gcd(from, to));\n/**\n * Implemented with numbers, because BigInt is 5x slower\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix2(data, from, to, padding) {\n    if (!Array.isArray(data))\n        throw new Error('convertRadix2: data should be array');\n    if (from <= 0 || from > 32)\n        throw new Error(`convertRadix2: wrong from=${from}`);\n    if (to <= 0 || to > 32)\n        throw new Error(`convertRadix2: wrong to=${to}`);\n    if (radix2carry(from, to) > 32) {\n        throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n    }\n    let carry = 0;\n    let pos = 0; // bitwise position in current element\n    const mask = 2 ** to - 1;\n    const res = [];\n    for (const n of data) {\n        if (n >= 2 ** from)\n            throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n        carry = (carry << from) | n;\n        if (pos + from > 32)\n            throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n        pos += from;\n        for (; pos >= to; pos -= to)\n            res.push(((carry >> (pos - to)) & mask) >>> 0);\n        carry &= 2 ** pos - 1; // clean carry, otherwise it will cause overflow\n    }\n    carry = (carry << (to - pos)) & mask;\n    if (!padding && pos >= from)\n        throw new Error('Excess padding');\n    if (!padding && carry)\n        throw new Error(`Non-zero padding: ${carry}`);\n    if (padding && pos > 0)\n        res.push(carry >>> 0);\n    return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix.encode input should be Uint8Array');\n            return convertRadix(Array.from(bytes), 2 ** 8, num);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix.decode input should be array of strings');\n            return Uint8Array.from(convertRadix(digits, num, 2 ** 8));\n        },\n    };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n    if (bits <= 0 || bits > 32)\n        throw new Error('radix2: bits should be in (0..32]');\n    if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n        throw new Error('radix2: carry overflow');\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix2.encode input should be Uint8Array');\n            return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix2.decode input should be array of strings');\n            return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction unsafeWrapper(fn) {\n    if (typeof fn !== 'function')\n        throw new Error('unsafeWrapper fn should be function');\n    return function (...args) {\n        try {\n            return fn.apply(null, args);\n        }\n        catch (e) { }\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction checksum(len, fn) {\n    if (typeof fn !== 'function')\n        throw new Error('checksum fn should be function');\n    return {\n        encode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.encode: input should be Uint8Array');\n            const checksum = fn(data).slice(0, len);\n            const res = new Uint8Array(data.length + len);\n            res.set(data);\n            res.set(checksum, data.length);\n            return res;\n        },\n        decode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.decode: input should be Uint8Array');\n            const payload = data.slice(0, -len);\n            const newChecksum = fn(payload).slice(0, len);\n            const oldChecksum = data.slice(-len);\n            for (let i = 0; i < len; i++)\n                if (newChecksum[i] !== oldChecksum[i])\n                    throw new Error('Invalid checksum');\n            return payload;\n        },\n    };\n}\nconst base64 = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\nconst base64url = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\n// base58 code\n// -----------\nconst genBase58 = (abc) => chain(radix(58), alphabet(abc), join(''));\nconst base58 = /* @__PURE__ */ genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\nconst base58check = /* @__PURE__ */ (sha256) => chain(checksum(4, (data) => sha256(sha256(data))), base58);\nconst BECH_ALPHABET = /* @__PURE__ */ chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bech32Polymod(pre) {\n    const b = pre >> 25;\n    let chk = (pre & 0x1ffffff) << 5;\n    for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n        if (((b >> i) & 1) === 1)\n            chk ^= POLYMOD_GENERATORS[i];\n    }\n    return chk;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n    const len = prefix.length;\n    let chk = 1;\n    for (let i = 0; i < len; i++) {\n        const c = prefix.charCodeAt(i);\n        if (c < 33 || c > 126)\n            throw new Error(`Invalid prefix (${prefix})`);\n        chk = bech32Polymod(chk) ^ (c >> 5);\n    }\n    chk = bech32Polymod(chk);\n    for (let i = 0; i < len; i++)\n        chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n    for (let v of words)\n        chk = bech32Polymod(chk) ^ v;\n    for (let i = 0; i < 6; i++)\n        chk = bech32Polymod(chk);\n    chk ^= encodingConst;\n    return BECH_ALPHABET.encode(convertRadix2([chk % 2 ** 30], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n    const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n    const _words = radix2(5);\n    const fromWords = _words.decode;\n    const toWords = _words.encode;\n    const fromWordsUnsafe = unsafeWrapper(fromWords);\n    function encode(prefix, words, limit = 90) {\n        if (typeof prefix !== 'string')\n            throw new Error(`bech32.encode prefix should be string, not ${typeof prefix}`);\n        if (!Array.isArray(words) || (words.length && typeof words[0] !== 'number'))\n            throw new Error(`bech32.encode words should be array of numbers, not ${typeof words}`);\n        const actualLength = prefix.length + 7 + words.length;\n        if (limit !== false && actualLength > limit)\n            throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n        const lowered = prefix.toLowerCase();\n        const sum = bechChecksum(lowered, words, ENCODING_CONST);\n        return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n    }\n    function decode(str, limit = 90) {\n        if (typeof str !== 'string')\n            throw new Error(`bech32.decode input should be string, not ${typeof str}`);\n        if (str.length < 8 || (limit !== false && str.length > limit))\n            throw new TypeError(`Wrong string length: ${str.length} (${str}). Expected (8..${limit})`);\n        // don't allow mixed case\n        const lowered = str.toLowerCase();\n        if (str !== lowered && str !== str.toUpperCase())\n            throw new Error(`String must be lowercase or uppercase`);\n        str = lowered;\n        const sepIndex = str.lastIndexOf('1');\n        if (sepIndex === 0 || sepIndex === -1)\n            throw new Error(`Letter \"1\" must be present between prefix and data only`);\n        const prefix = str.slice(0, sepIndex);\n        const _words = str.slice(sepIndex + 1);\n        if (_words.length < 6)\n            throw new Error('Data must be at least 6 characters long');\n        const words = BECH_ALPHABET.decode(_words).slice(0, -6);\n        const sum = bechChecksum(prefix, words, ENCODING_CONST);\n        if (!_words.endsWith(sum))\n            throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n        return { prefix, words };\n    }\n    const decodeUnsafe = unsafeWrapper(decode);\n    function decodeToBytes(str) {\n        const { prefix, words } = decode(str, false);\n        return { prefix, words, bytes: fromWords(words) };\n    }\n    return { encode, decode, decodeToBytes, decodeUnsafe, fromWords, fromWordsUnsafe, toWords };\n}\nconst bech32 = /* @__PURE__ */ genBech32('bech32');\nconst bech32m = /* @__PURE__ */ genBech32('bech32m');\n\nconst Encoder = {\n    b58chk: {\n        encode: (data) => base58check(sha256).encode(data),\n        decode: (data) => base58check(sha256).decode(data)\n    },\n    base64: {\n        encode: (data) => base64.encode(data),\n        decode: (data) => base64.decode(data)\n    },\n    b64url: {\n        encode: (data) => base64url.encode(data),\n        decode: (data) => base64url.decode(data)\n    },\n    bech32: {\n        to_words: bech32.toWords,\n        to_bytes: bech32.fromWords,\n        encode: (prefix, words, limit = false) => {\n            return bech32.encode(prefix, words, limit);\n        },\n        decode: (data, limit = false) => {\n            const { prefix, words } = bech32.decode(data, limit);\n            return { prefix, words };\n        }\n    },\n    bech32m: {\n        to_words: bech32m.toWords,\n        to_bytes: bech32m.fromWords,\n        encode: (prefix, words, limit = false) => {\n            return bech32m.encode(prefix, words, limit);\n        },\n        decode: (data, limit = false) => {\n            const { prefix, words } = bech32m.decode(data, limit);\n            return { prefix, words };\n        }\n    }\n};\n\nfunction within_size(data, size) {\n    if (data.length > size) {\n        throw new TypeError(`Data is larger than array size: ${data.length} > ${size}`);\n    }\n}\nfunction is_hex$1(hex) {\n    if (hex.match(/[^a-fA-f0-9]/) !== null) {\n        throw new TypeError('Invalid characters in hex string: ' + hex);\n    }\n    if (hex.length % 2 !== 0) {\n        throw new Error(`Length of hex string is invalid: ${hex.length}`);\n    }\n}\nfunction is_json(str) {\n    try {\n        JSON.parse(str);\n    }\n    catch {\n        throw new TypeError('JSON string is invalid!');\n    }\n}\nfunction is_safe_num(num) {\n    if (num > Number.MAX_SAFE_INTEGER) {\n        throw new TypeError('Number exceeds safe bounds!');\n    }\n}\nfunction is_prefix(actual, target) {\n    if (actual !== target) {\n        throw new TypeError(`Bech32 prefix does not match: ${actual} !== ${target}`);\n    }\n}\n\nvar assert = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    is_hex: is_hex$1,\n    is_json: is_json,\n    is_prefix: is_prefix,\n    is_safe_num: is_safe_num,\n    within_size: within_size\n});\n\nconst _0n = BigInt(0);\nconst _255n = BigInt(255);\nconst _256n = BigInt(256);\nfunction big_size(big) {\n    if (big <= 0xffn)\n        return 1;\n    if (big <= 0xffffn)\n        return 2;\n    if (big <= 0xffffffffn)\n        return 4;\n    if (big <= 0xffffffffffffffffn)\n        return 8;\n    if (big <= 0xffffffffffffffffffffffffffffffffn)\n        return 16;\n    if (big <= 0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn) {\n        return 32;\n    }\n    throw new TypeError('Must specify a fixed buffer size for bigints greater than 32 bytes.');\n}\nfunction bigToBytes(big, size, endian = 'be') {\n    if (size === undefined)\n        size = big_size(big);\n    const use_le = (endian === 'le');\n    const buffer = new ArrayBuffer(size);\n    const dataView = new DataView(buffer);\n    let offset = (use_le) ? 0 : size - 1;\n    while (big > _0n) {\n        const byte = big & _255n;\n        const num = Number(byte);\n        if (use_le) {\n            dataView.setUint8(offset++, num);\n        }\n        else {\n            dataView.setUint8(offset--, num);\n        }\n        big = (big - byte) / _256n;\n    }\n    return new Uint8Array(buffer);\n}\nfunction bytesToBig(bytes) {\n    let num = BigInt(0);\n    for (let i = bytes.length - 1; i >= 0; i--) {\n        num = (num * _256n) + BigInt(bytes[i]);\n    }\n    return BigInt(num);\n}\n\nfunction binToBytes(binary) {\n    const bins = binary.split('').map(Number);\n    if (bins.length % 8 !== 0) {\n        throw new Error(`Binary array is invalid length: ${binary.length}`);\n    }\n    const bytes = new Uint8Array(bins.length / 8);\n    for (let i = 0, ct = 0; i < bins.length; i += 8, ct++) {\n        let byte = 0;\n        for (let j = 0; j < 8; j++) {\n            byte |= (bins[i + j] << (7 - j));\n        }\n        bytes[ct] = byte;\n    }\n    return bytes;\n}\nfunction bytesToBin(bytes) {\n    const bin = new Array(bytes.length * 8);\n    let count = 0;\n    for (const num of bytes) {\n        if (num > 255) {\n            throw new Error(`Invalid byte value: ${num}. Byte values must be between 0 and 255.`);\n        }\n        for (let i = 7; i >= 0; i--, count++) {\n            bin[count] = (num >> i) & 1;\n        }\n    }\n    return bin.join('');\n}\n\nfunction num_size(num) {\n    if (num <= 0xFF)\n        return 1;\n    if (num <= 0xFFFF)\n        return 2;\n    if (num <= 0xFFFFFFFF)\n        return 4;\n    throw new TypeError('Numbers larger than 4 bytes must specify a fixed size!');\n}\nfunction numToBytes(num, size, endian = 'be') {\n    if (size === undefined)\n        size = num_size(num);\n    const use_le = (endian === 'le');\n    const buffer = new ArrayBuffer(size);\n    const dataView = new DataView(buffer);\n    let offset = (use_le) ? 0 : size - 1;\n    while (num > 0) {\n        const byte = num & 255;\n        if (use_le) {\n            dataView.setUint8(offset++, num);\n        }\n        else {\n            dataView.setUint8(offset--, num);\n        }\n        num = (num - byte) / 256;\n    }\n    return new Uint8Array(buffer);\n}\nfunction bytesToNum(bytes) {\n    let num = 0;\n    for (let i = bytes.length - 1; i >= 0; i--) {\n        num = (num * 256) + bytes[i];\n        is_safe_num(num);\n    }\n    return num;\n}\n\nconst ec = new TextEncoder();\nconst dc = new TextDecoder();\nfunction strToBytes(str) {\n    return ec.encode(str);\n}\nfunction bytesToStr(bytes) {\n    return dc.decode(bytes);\n}\nfunction hex_size(hexstr, size) {\n    is_hex$1(hexstr);\n    const len = hexstr.length / 2;\n    if (size === undefined)\n        size = len;\n    if (len > size) {\n        throw new TypeError(`Hex string is larger than array size: ${len} > ${size}`);\n    }\n    return size;\n}\nfunction hexToBytes(hexstr, size, endian = 'le') {\n    size = hex_size(hexstr, size);\n    const use_le = (endian === 'le');\n    const buffer = new ArrayBuffer(size);\n    const dataView = new DataView(buffer);\n    let offset = (use_le) ? 0 : size - 1;\n    for (let i = 0; i < hexstr.length; i += 2) {\n        const char = hexstr.substring(i, i + 2);\n        const num = parseInt(char, 16);\n        if (use_le) {\n            dataView.setUint8(offset++, num);\n        }\n        else {\n            dataView.setUint8(offset--, num);\n        }\n    }\n    return new Uint8Array(buffer);\n}\nfunction bytesToHex(bytes) {\n    let chars = '';\n    for (let i = 0; i < bytes.length; i++) {\n        chars += bytes[i].toString(16).padStart(2, '0');\n    }\n    return chars;\n}\nconst Hex = {\n    encode: bytesToHex,\n    decode: hexToBytes\n};\nconst Txt = {\n    encode: strToBytes,\n    decode: bytesToStr\n};\n\nconst { getRandomValues } = crypto ?? globalThis.crypto ?? window.crypto;\nfunction random(size = 32) {\n    if (typeof getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(size));\n    }\n    throw new Error('Crypto module missing getRandomValues!');\n}\nfunction is_hex(input) {\n    if (input.match(/[^a-fA-f0-9]/) === null &&\n        input.length % 2 === 0) {\n        return true;\n    }\n    return false;\n}\nfunction is_bytes(input) {\n    if (typeof input === 'string' &&\n        is_hex(input)) {\n        return true;\n    }\n    else if (typeof input === 'number' ||\n        typeof input === 'bigint' ||\n        input instanceof Uint8Array) {\n        return true;\n    }\n    else if (Array.isArray(input) &&\n        input.every(e => typeof e === 'number')) {\n        return true;\n    }\n    else {\n        return false;\n    }\n}\nfunction set_buffer(data, size, endian = 'be') {\n    if (size === undefined)\n        size = data.length;\n    within_size(data, size);\n    const buffer = new Uint8Array(size).fill(0);\n    const offset = (endian === 'be') ? 0 : size - data.length;\n    buffer.set(data, offset);\n    return buffer;\n}\nfunction join_array(arr) {\n    let i, offset = 0;\n    const size = arr.reduce((len, arr) => len + arr.length, 0);\n    const buff = new Uint8Array(size);\n    for (i = 0; i < arr.length; i++) {\n        const a = arr[i];\n        buff.set(a, offset);\n        offset += a.length;\n    }\n    return buff;\n}\nfunction bigint_replacer(_, v) {\n    return typeof v === 'bigint'\n        ? `${v}n`\n        : v;\n}\nfunction bigint_reviver(_, v) {\n    return typeof v === 'string' && /n$/.test(v)\n        ? BigInt(v.slice(0, -1))\n        : v;\n}\nfunction parse_data$1(data_blob, chunk_size, total_size) {\n    const len = data_blob.length, count = total_size / chunk_size;\n    if (total_size % chunk_size !== 0) {\n        throw new TypeError(`Invalid parameters: ${total_size} % ${chunk_size} !== 0`);\n    }\n    if (len !== total_size) {\n        throw new TypeError(`Invalid data stream: ${len} !== ${total_size}`);\n    }\n    if (len % chunk_size !== 0) {\n        throw new TypeError(`Invalid data stream: ${len} % ${chunk_size} !== 0`);\n    }\n    const chunks = new Array(count);\n    for (let i = 0; i < count; i++) {\n        const idx = i * chunk_size;\n        chunks[i] = data_blob.subarray(idx, idx + chunk_size);\n    }\n    return chunks;\n}\n\nvar utils = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    bigint_replacer: bigint_replacer,\n    bigint_reviver: bigint_reviver,\n    is_bytes: is_bytes,\n    is_hex: is_hex,\n    join_array: join_array,\n    parse_data: parse_data$1,\n    random: random,\n    set_buffer: set_buffer\n});\n\nfunction buffer_data(data, size, endian) {\n    if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else if (data instanceof Uint8Array) {\n        return set_buffer(data, size, endian);\n    }\n    else if (Array.isArray(data)) {\n        const bytes = data.map(e => buffer_data(e, size, endian));\n        return join_array(bytes);\n    }\n    else if (typeof data === 'string') {\n        return hexToBytes(data, size, endian);\n    }\n    else if (typeof data === 'bigint') {\n        return bigToBytes(data, size, endian);\n    }\n    else if (typeof data === 'number') {\n        return numToBytes(data, size, endian);\n    }\n    else if (typeof data === 'boolean') {\n        return Uint8Array.of(data ? 1 : 0);\n    }\n    throw new TypeError('Unsupported format:' + String(typeof data));\n}\n\nclass Buff extends Uint8Array {\n    static { this.num = numToBuff; }\n    static { this.big = bigToBuff; }\n    static { this.bin = binToBuff; }\n    static { this.raw = rawToBuff; }\n    static { this.str = strToBuff; }\n    static { this.hex = hexToBuff; }\n    static { this.bytes = buffer; }\n    static { this.json = jsonToBuff; }\n    static { this.base64 = base64ToBuff; }\n    static { this.b64url = b64urlToBuff; }\n    static { this.bech32 = bech32ToBuff; }\n    static { this.bech32m = bech32mToBuff; }\n    static { this.b58chk = b58chkToBuff; }\n    static { this.encode = strToBytes; }\n    static { this.decode = bytesToStr; }\n    static { this.parse = parse_data; }\n    static { this.is_bytes = is_bytes; }\n    static { this.is_hex = is_hex; }\n    static random(size = 32) {\n        const rand = random(size);\n        return new Buff(rand, size);\n    }\n    static now(size = 4) {\n        const stamp = Math.floor(Date.now() / 1000);\n        return new Buff(stamp, size);\n    }\n    constructor(data, size, endian) {\n        if (data instanceof Buff &&\n            size === undefined) {\n            return data;\n        }\n        const buffer = buffer_data(data, size, endian);\n        super(buffer);\n    }\n    get arr() {\n        return [...this];\n    }\n    get num() {\n        return this.to_num();\n    }\n    get big() {\n        return this.to_big();\n    }\n    get str() {\n        return this.to_str();\n    }\n    get hex() {\n        return this.to_hex();\n    }\n    get raw() {\n        return new Uint8Array(this);\n    }\n    get bin() {\n        return this.to_bin();\n    }\n    get b58chk() {\n        return this.to_b58chk();\n    }\n    get base64() {\n        return this.to_base64();\n    }\n    get b64url() {\n        return this.to_b64url();\n    }\n    get digest() {\n        return this.to_hash();\n    }\n    get id() {\n        return this.to_hash().hex;\n    }\n    get stream() {\n        return new Stream(this);\n    }\n    to_num(endian = 'be') {\n        const bytes = (endian === 'be')\n            ? this.reverse()\n            : this;\n        return bytesToNum(bytes);\n    }\n    to_big(endian = 'be') {\n        const bytes = (endian === 'be')\n            ? this.reverse()\n            : this;\n        return bytesToBig(bytes);\n    }\n    to_bin() {\n        return bytesToBin(this);\n    }\n    to_hash() {\n        const digest = sha256(this);\n        return new Buff(digest);\n    }\n    to_json(reviver) {\n        if (reviver === undefined) {\n            reviver = bigint_reviver;\n        }\n        const str = bytesToStr(this);\n        return JSON.parse(str, reviver);\n    }\n    to_bech32(prefix, limit) {\n        const { encode, to_words } = Encoder.bech32;\n        const words = to_words(this);\n        return encode(prefix, words, limit);\n    }\n    to_bech32m(prefix, limit) {\n        const { encode, to_words } = Encoder.bech32m;\n        const words = to_words(this);\n        return encode(prefix, words, limit);\n    }\n    to_str() { return bytesToStr(this); }\n    to_hex() { return bytesToHex(this); }\n    to_bytes() { return new Uint8Array(this); }\n    to_b58chk() { return Encoder.b58chk.encode(this); }\n    to_base64() { return Encoder.base64.encode(this); }\n    to_b64url() { return Encoder.b64url.encode(this); }\n    append(data) {\n        return Buff.join([this, Buff.bytes(data)]);\n    }\n    prepend(data) {\n        return Buff.join([Buff.bytes(data), this]);\n    }\n    reverse() {\n        const arr = new Uint8Array(this).reverse();\n        return new Buff(arr);\n    }\n    slice(start, end) {\n        const arr = new Uint8Array(this).slice(start, end);\n        return new Buff(arr);\n    }\n    set(array, offset) {\n        this.set(array, offset);\n    }\n    subarray(begin, end) {\n        const arr = new Uint8Array(this).subarray(begin, end);\n        return new Buff(arr);\n    }\n    write(bytes, offset) {\n        const b = Buff.bytes(bytes);\n        this.set(b, offset);\n    }\n    add_varint(endian) {\n        const size = Buff.calc_varint(this.length, endian);\n        return Buff.join([size, this]);\n    }\n    static from(data) {\n        return new Buff(Uint8Array.from(data));\n    }\n    static of(...args) {\n        return new Buff(Uint8Array.of(...args));\n    }\n    static join(arr) {\n        const bytes = arr.map(e => Buff.bytes(e));\n        const joined = join_array(bytes);\n        return new Buff(joined);\n    }\n    static sort(arr, size) {\n        const hex = arr.map(e => buffer(e, size).hex);\n        hex.sort();\n        return hex.map(e => Buff.hex(e, size));\n    }\n    static calc_varint(num, endian) {\n        if (num < 0xFD) {\n            return Buff.num(num, 1);\n        }\n        else if (num < 0x10000) {\n            return Buff.of(0xFD, ...Buff.num(num, 2, endian));\n        }\n        else if (num < 0x100000000) {\n            return Buff.of(0xFE, ...Buff.num(num, 4, endian));\n        }\n        else if (BigInt(num) < 0x10000000000000000n) {\n            return Buff.of(0xFF, ...Buff.num(num, 8, endian));\n        }\n        else {\n            throw new Error(`Value is too large: ${num}`);\n        }\n    }\n}\nfunction numToBuff(number, size, endian) {\n    return new Buff(number, size, endian);\n}\nfunction binToBuff(data, size, endian) {\n    return new Buff(binToBytes(data), size, endian);\n}\nfunction bigToBuff(bigint, size, endian) {\n    return new Buff(bigint, size, endian);\n}\nfunction rawToBuff(data, size, endian) {\n    return new Buff(data, size, endian);\n}\nfunction strToBuff(data, size, endian) {\n    return new Buff(strToBytes(data), size, endian);\n}\nfunction hexToBuff(data, size, endian) {\n    return new Buff(data, size, endian);\n}\nfunction jsonToBuff(data, replacer) {\n    if (replacer === undefined) {\n        replacer = bigint_replacer;\n    }\n    const str = JSON.stringify(data, replacer);\n    return new Buff(strToBytes(str));\n}\nfunction base64ToBuff(data) {\n    return new Buff(Encoder.base64.decode(data));\n}\nfunction b64urlToBuff(data) {\n    return new Buff(Encoder.b64url.decode(data));\n}\nfunction bech32ToBuff(data, limit, chk_prefix) {\n    const { decode, to_bytes } = Encoder.bech32;\n    const { prefix, words } = decode(data, limit);\n    const bytes = to_bytes(words);\n    if (typeof chk_prefix === 'string') {\n        is_prefix(prefix, chk_prefix);\n    }\n    return new Buff(bytes);\n}\nfunction bech32mToBuff(data, limit, chk_prefix) {\n    const { decode, to_bytes } = Encoder.bech32m;\n    const { prefix, words } = decode(data, limit);\n    const bytes = to_bytes(words);\n    if (typeof chk_prefix === 'string') {\n        is_prefix(prefix, chk_prefix);\n    }\n    return new Buff(bytes);\n}\nfunction b58chkToBuff(data) {\n    return new Buff(Encoder.b58chk.decode(data));\n}\nfunction parse_data(data_blob, chunk_size, total_size) {\n    const bytes = buffer_data(data_blob);\n    const chunks = parse_data$1(bytes, chunk_size, total_size);\n    return chunks.map(e => Buff.bytes(e));\n}\nclass Stream {\n    constructor(data) {\n        this.data = Buff.bytes(data);\n        this.size = this.data.length;\n    }\n    peek(size) {\n        if (size > this.size) {\n            throw new Error(`Size greater than stream: ${size} > ${this.size}`);\n        }\n        return new Buff(this.data.slice(0, size));\n    }\n    read(size) {\n        const chunk = this.peek(size);\n        this.data = this.data.slice(size);\n        this.size = this.data.length;\n        return chunk;\n    }\n    read_varint(endian) {\n        const num = this.read(1).num;\n        switch (true) {\n            case (num >= 0 && num < 0xFD):\n                return num;\n            case (num === 0xFD):\n                return this.read(2).to_num(endian);\n            case (num === 0xFE):\n                return this.read(4).to_num(endian);\n            case (num === 0xFF):\n                return this.read(8).to_num(endian);\n            default:\n                throw new Error(`Varint is out of range: ${num}`);\n        }\n    }\n}\nfunction buffer(bytes, size, end) {\n    return new Buff(bytes, size, end);\n}\n\nexport { Buff, Encoder, Hex, Stream, Txt, assert, buffer, utils as util };\n//# sourceMappingURL=module.mjs.map\n", null, null, null, null, "const U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n// We are not using BigUint64Array, because they are extremely slow as per 2022\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    let Ah = new Uint32Array(lst.length);\n    let Al = new Uint32Array(lst.length);\n    for (let i = 0; i < lst.length; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nconst rotr32L = (h, _l) => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n// prettier-ignore\nexport { fromBig, split, toBig, shrSH, shrSL, rotrSH, rotrSL, rotrBH, rotrBL, rotr32H, rotr32L, rotlSH, rotlSL, rotlBH, rotlBL, add, add3L, add3H, add4L, add4H, add5H, add5L, };\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n//# sourceMappingURL=_u64.js.map", "import { SHA2 } from './_sha2.js';\nimport u64 from './_u64.js';\nimport { wrapConstructor } from './utils.js';\n// Round contants (first 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409):\n// prettier-ignore\nconst [SHA512_Kh, SHA512_Kl] = /* @__PURE__ */ (() => u64.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\n// Temporary buffer, not used to store anything between runs\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nexport class SHA512 extends SHA2 {\n    constructor() {\n        super(128, 64, 16, false);\n        // We cannot use array here since array allows indexing by variable which means optimizer/compiler cannot use registers.\n        // Also looks cleaner and easier to verify with spec.\n        // Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x6a09e667 | 0;\n        this.Al = 0xf3bcc908 | 0;\n        this.Bh = 0xbb67ae85 | 0;\n        this.Bl = 0x84caa73b | 0;\n        this.Ch = 0x3c6ef372 | 0;\n        this.Cl = 0xfe94f82b | 0;\n        this.Dh = 0xa54ff53a | 0;\n        this.Dl = 0x5f1d36f1 | 0;\n        this.Eh = 0x510e527f | 0;\n        this.El = 0xade682d1 | 0;\n        this.Fh = 0x9b05688c | 0;\n        this.Fl = 0x2b3e6c1f | 0;\n        this.Gh = 0x1f83d9ab | 0;\n        this.Gl = 0xfb41bd6b | 0;\n        this.Hh = 0x5be0cd19 | 0;\n        this.Hl = 0x137e2179 | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n            const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n            const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n            const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n            const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = u64.add3L(T1l, sigma0l, MAJl);\n            Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        SHA512_W_H.fill(0);\n        SHA512_W_L.fill(0);\n    }\n    destroy() {\n        this.buffer.fill(0);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nclass SHA512_224 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x8c3d37c8 | 0;\n        this.Al = 0x19544da2 | 0;\n        this.Bh = 0x73e19966 | 0;\n        this.Bl = 0x89dcd4d6 | 0;\n        this.Ch = 0x1dfab7ae | 0;\n        this.Cl = 0x32ff9c82 | 0;\n        this.Dh = 0x679dd514 | 0;\n        this.Dl = 0x582f9fcf | 0;\n        this.Eh = 0x0f6d2b69 | 0;\n        this.El = 0x7bd44da8 | 0;\n        this.Fh = 0x77e36f73 | 0;\n        this.Fl = 0x04c48942 | 0;\n        this.Gh = 0x3f9d85a8 | 0;\n        this.Gl = 0x6a1d36c8 | 0;\n        this.Hh = 0x1112e6ad | 0;\n        this.Hl = 0x91d692a1 | 0;\n        this.outputLen = 28;\n    }\n}\nclass SHA512_256 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0x22312194 | 0;\n        this.Al = 0xfc2bf72c | 0;\n        this.Bh = 0x9f555fa3 | 0;\n        this.Bl = 0xc84c64c2 | 0;\n        this.Ch = 0x2393b86b | 0;\n        this.Cl = 0x6f53b151 | 0;\n        this.Dh = 0x96387719 | 0;\n        this.Dl = 0x5940eabd | 0;\n        this.Eh = 0x96283ee2 | 0;\n        this.El = 0xa88effe3 | 0;\n        this.Fh = 0xbe5e1e25 | 0;\n        this.Fl = 0x53863992 | 0;\n        this.Gh = 0x2b0199fc | 0;\n        this.Gl = 0x2c85b8aa | 0;\n        this.Hh = 0x0eb72ddc | 0;\n        this.Hl = 0x81c52ca2 | 0;\n        this.outputLen = 32;\n    }\n}\nclass SHA384 extends SHA512 {\n    constructor() {\n        super();\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = 0xcbbb9d5d | 0;\n        this.Al = 0xc1059ed8 | 0;\n        this.Bh = 0x629a292a | 0;\n        this.Bl = 0x367cd507 | 0;\n        this.Ch = 0x9159015a | 0;\n        this.Cl = 0x3070dd17 | 0;\n        this.Dh = 0x152fecd8 | 0;\n        this.Dl = 0xf70e5939 | 0;\n        this.Eh = 0x67332667 | 0;\n        this.El = 0xffc00b31 | 0;\n        this.Fh = 0x8eb44a87 | 0;\n        this.Fl = 0x68581511 | 0;\n        this.Gh = 0xdb0c2e0d | 0;\n        this.Gl = 0x64f98fa7 | 0;\n        this.Hh = 0x47b5481d | 0;\n        this.Hl = 0xbefa4fa4 | 0;\n        this.outputLen = 48;\n    }\n}\nexport const sha512 = /* @__PURE__ */ wrapConstructor(() => new SHA512());\nexport const sha512_224 = /* @__PURE__ */ wrapConstructor(() => new SHA512_224());\nexport const sha512_256 = /* @__PURE__ */ wrapConstructor(() => new SHA512_256());\nexport const sha384 = /* @__PURE__ */ wrapConstructor(() => new SHA384());\n//# sourceMappingURL=sha512.js.map", "import { SHA2 } from './_sha2.js';\nimport { wrapConstructor } from './utils.js';\n// https://homes.esat.kuleuven.be/~bosselae/ripemd160.html\n// https://homes.esat.kuleuven.be/~bosselae/ripemd160/pdf/AB-9601/AB-9601.pdf\nconst Rho = /* @__PURE__ */ new Uint8Array([7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8]);\nconst Id = /* @__PURE__ */ Uint8Array.from({ length: 16 }, (_, i) => i);\nconst Pi = /* @__PURE__ */ Id.map((i) => (9 * i + 5) % 16);\nlet idxL = [Id];\nlet idxR = [Pi];\nfor (let i = 0; i < 4; i++)\n    for (let j of [idxL, idxR])\n        j.push(j[i].map((k) => Rho[k]));\nconst shifts = /* @__PURE__ */ [\n    [11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8],\n    [12, 13, 11, 15, 6, 9, 9, 7, 12, 15, 11, 13, 7, 8, 7, 7],\n    [13, 15, 14, 11, 7, 7, 6, 8, 13, 14, 13, 12, 5, 5, 6, 9],\n    [14, 11, 12, 14, 8, 6, 5, 5, 15, 12, 15, 14, 9, 9, 8, 6],\n    [15, 12, 13, 13, 9, 5, 8, 6, 14, 11, 12, 11, 8, 6, 5, 5],\n].map((i) => new Uint8Array(i));\nconst shiftsL = /* @__PURE__ */ idxL.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst shiftsR = /* @__PURE__ */ idxR.map((idx, i) => idx.map((j) => shifts[i][j]));\nconst Kl = /* @__PURE__ */ new Uint32Array([\n    0x00000000, 0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xa953fd4e,\n]);\nconst Kr = /* @__PURE__ */ new Uint32Array([\n    0x50a28be6, 0x5c4dd124, 0x6d703ef3, 0x7a6d76e9, 0x00000000,\n]);\n// The rotate left (circular left shift) operation for uint32\nconst rotl = (word, shift) => (word << shift) | (word >>> (32 - shift));\n// It's called f() in spec.\nfunction f(group, x, y, z) {\n    if (group === 0)\n        return x ^ y ^ z;\n    else if (group === 1)\n        return (x & y) | (~x & z);\n    else if (group === 2)\n        return (x | ~y) ^ z;\n    else if (group === 3)\n        return (x & z) | (y & ~z);\n    else\n        return x ^ (y | ~z);\n}\n// Temporary buffer, not used to store anything between runs\nconst BUF = /* @__PURE__ */ new Uint32Array(16);\nexport class RIPEMD160 extends SHA2 {\n    constructor() {\n        super(64, 20, 8, true);\n        this.h0 = 0x67452301 | 0;\n        this.h1 = 0xefcdab89 | 0;\n        this.h2 = 0x98badcfe | 0;\n        this.h3 = 0x10325476 | 0;\n        this.h4 = 0xc3d2e1f0 | 0;\n    }\n    get() {\n        const { h0, h1, h2, h3, h4 } = this;\n        return [h0, h1, h2, h3, h4];\n    }\n    set(h0, h1, h2, h3, h4) {\n        this.h0 = h0 | 0;\n        this.h1 = h1 | 0;\n        this.h2 = h2 | 0;\n        this.h3 = h3 | 0;\n        this.h4 = h4 | 0;\n    }\n    process(view, offset) {\n        for (let i = 0; i < 16; i++, offset += 4)\n            BUF[i] = view.getUint32(offset, true);\n        // prettier-ignore\n        let al = this.h0 | 0, ar = al, bl = this.h1 | 0, br = bl, cl = this.h2 | 0, cr = cl, dl = this.h3 | 0, dr = dl, el = this.h4 | 0, er = el;\n        // Instead of iterating 0 to 80, we split it into 5 groups\n        // And use the groups in constants, functions, etc. Much simpler\n        for (let group = 0; group < 5; group++) {\n            const rGroup = 4 - group;\n            const hbl = Kl[group], hbr = Kr[group]; // prettier-ignore\n            const rl = idxL[group], rr = idxR[group]; // prettier-ignore\n            const sl = shiftsL[group], sr = shiftsR[group]; // prettier-ignore\n            for (let i = 0; i < 16; i++) {\n                const tl = (rotl(al + f(group, bl, cl, dl) + BUF[rl[i]] + hbl, sl[i]) + el) | 0;\n                al = el, el = dl, dl = rotl(cl, 10) | 0, cl = bl, bl = tl; // prettier-ignore\n            }\n            // 2 loops are 10% faster\n            for (let i = 0; i < 16; i++) {\n                const tr = (rotl(ar + f(rGroup, br, cr, dr) + BUF[rr[i]] + hbr, sr[i]) + er) | 0;\n                ar = er, er = dr, dr = rotl(cr, 10) | 0, cr = br, br = tr; // prettier-ignore\n            }\n        }\n        // Add the compressed chunk to the current hash value\n        this.set((this.h1 + cl + dr) | 0, (this.h2 + dl + er) | 0, (this.h3 + el + ar) | 0, (this.h4 + al + br) | 0, (this.h0 + bl + cr) | 0);\n    }\n    roundClean() {\n        BUF.fill(0);\n    }\n    destroy() {\n        this.destroyed = true;\n        this.buffer.fill(0);\n        this.set(0, 0, 0, 0, 0);\n    }\n}\n/**\n * RIPEMD-160 - a hash function from 1990s.\n * @param message - msg that would be hashed\n */\nexport const ripemd160 = /* @__PURE__ */ wrapConstructor(() => new RIPEMD160());\n//# sourceMappingURL=ripemd160.js.map", null, null, null, null, null, null], "names": ["number", "n", "Number", "isSafeInteger", "Error", "bytes", "b", "lengths", "Uint8Array", "length", "includes", "exists", "instance", "checkFinished", "destroyed", "finished", "crypto", "globalThis", "undefined", "u8a", "a", "createView", "arr", "DataView", "buffer", "byteOffset", "byteLength", "rotr", "word", "shift", "Uint32Array", "toBytes", "data", "str", "TextEncoder", "encode", "utf8ToBytes", "Hash$1", "clone", "this", "_cloneInto", "wrapConstructor", "hashCons", "hashC", "msg", "update", "digest", "tmp", "outputLen", "blockLen", "create", "randomBytes", "bytesLength", "getRandomValues", "Hash", "constructor", "padOffset", "isLE", "super", "pos", "view", "len", "take", "Math", "min", "set", "subarray", "process", "dataView", "roundClean", "digestInto", "out", "output", "fill", "i", "value", "setBigUint64", "_32n", "BigInt", "_u32_max", "wh", "wl", "h", "l", "setUint32", "oview", "outLen", "state", "get", "res", "slice", "destroy", "to", "Maj", "c", "SHA256_K", "IV", "SHA256_W", "SHA2", "A", "B", "C", "D", "E", "F", "G", "H", "offset", "getUint32", "W15", "W2", "s0", "s1", "T1", "T2", "sha256", "SHA256", "_0n", "_1n", "_2n", "hexes", "Array", "from", "_", "toString", "padStart", "bytesToHex", "hex", "numberToHexUnpadded", "num", "hexToNumber", "hexToBytes", "array", "j", "hexByte", "byte", "parseInt", "isNaN", "bytesToNumberBE", "bytesToNumberLE", "reverse", "numberToBytesBE", "numberToBytesLE", "ensureBytes", "title", "<PERSON><PERSON><PERSON><PERSON>", "e", "concatBytes", "arrays", "r", "reduce", "sum", "pad", "for<PERSON>ach", "bitMask", "u8n", "u8fr", "createHmacDrbg", "hashLen", "qByteLen", "hmacFn", "v", "k", "reset", "reseed", "seed", "gen", "sl", "push", "pred", "validatorFns", "bigint", "val", "function", "boolean", "string", "stringOrUint8Array", "isArray", "field", "object", "Fp", "<PERSON><PERSON><PERSON><PERSON>", "hash", "validateObject", "validators", "optValidators", "checkField", "fieldName", "type", "isOptional", "checkVal", "String", "Object", "entries", "b1", "b2", "_3n", "_4n", "_5n", "_8n", "mod", "result", "pow", "power", "modulo", "pow2", "x", "invert", "u", "m", "FpSqrt", "P", "p1div4", "root", "eql", "sqr", "c1", "n2", "mul", "nv", "sub", "ONE", "legendreC", "Q", "S", "Z", "Q1div2", "neg", "g", "ZERO", "t2", "ge", "tonelliShanks", "FIELD_FIELDS", "nLength", "nBitLength", "_nBitLength", "nByteLength", "ceil", "Field", "ORDER", "bitLen", "redef", "BITS", "BYTES", "sqrtP", "f", "freeze", "MASK", "is0", "isOdd", "lhs", "rhs", "add", "p", "d", "FpPow", "div", "sqrN", "addN", "subN", "mulN", "inv", "sqrt", "invertBatch", "lst", "nums", "lastMultiplied", "acc", "inverted", "reduceRight", "FpInvertBatch", "cmov", "fromBytes", "getFieldBytesLength", "fieldOrder", "bitLength", "get<PERSON>in<PERSON>ash<PERSON><PERSON><PERSON>", "validateBasic", "curve", "map", "Gx", "Gy", "b2n", "h2b", "ut", "DER", "Err", "_parseInt", "to<PERSON><PERSON>", "sBytes", "s", "rBytesLeft", "hexFromSig", "sig", "shl", "rhl", "rl", "weierstrassPoints", "opts", "CURVE", "ut.validateObject", "allowedPrivateKeyLengths", "wrapPrivateKey", "isTorsionFree", "clearCofactor", "allowInfinityPoint", "endo", "beta", "splitScalar", "validatePointOpts", "_c", "point", "_isCompressed", "toAffine", "ut.concatBytes", "y", "tail", "weierstrassEquation", "x2", "x3", "isWithinCurveOrder", "assertGE", "normPrivateKeyToScalar", "key", "ut.bytesToHex", "ut.bytesToNumberBE", "error", "mod.mod", "pointPrecomputes", "Map", "assertPrjPoint", "other", "Point", "px", "py", "pz", "fromAffine", "normalizeZ", "points", "toInv", "fromHex", "assertValidity", "fromPrivateKey", "privateKey", "BASE", "multiply", "_setWindowSize", "windowSize", "_WINDOW_SIZE", "delete", "left", "right", "hasEvenY", "equals", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "U1", "U2", "negate", "double", "b3", "X3", "Y3", "Z3", "t0", "t1", "t3", "t4", "t5", "subtract", "wNAF", "wnaf", "wNAFCached", "comp", "multiplyUnsafe", "I", "unsafeL<PERSON>der", "k1neg", "k1", "k2neg", "k2", "k1p", "k2p", "scalar", "fake", "f1p", "f2p", "constTimeNegate", "multiplyAndAddUnsafe", "iz", "z", "ax", "ay", "zz", "cofactor", "toRawBytes", "isCompressed", "toHex", "_bits", "bits", "condition", "item", "W", "windows", "elm", "precomputeWindow", "base", "window", "precomputes", "mask", "maxNumber", "shiftBy", "wbits", "offset1", "offset2", "abs", "cond1", "cond2", "precomputesMap", "transform", "ProjectivePoint", "<PERSON><PERSON><PERSON><PERSON>", "curveDef", "hmac", "bits2int", "bits2int_modN", "lowS", "validateOpts", "CURVE_ORDER", "compressedLen", "uncompressedLen", "modN", "invN", "mod.invert", "cat", "head", "y2", "numToNByteStr", "ut.numberToBytesBE", "isBiggerThanHalfOrder", "slcNum", "Signature", "recovery", "fromCompact", "fromDER", "addRecoveryBit", "recoverPublicKey", "msgHash", "rec", "radj", "prefix", "R", "ir", "u1", "u2", "hasHighS", "normalizeS", "toDERRawBytes", "ut.hexToBytes", "toDERHex", "toCompactRawBytes", "toCompactHex", "utils", "isValidPrivateKey", "randomPrivateKey", "mod.getMin<PERSON>ash<PERSON><PERSON><PERSON>", "fieldLen", "minLen", "reduced", "mod.mapHashToField", "precompute", "isProbPub", "delta", "ORDER_MASK", "ut.bitMask", "int2octets", "prepSig", "defaultSigOpts", "some", "prehash", "extraEntropy", "ent", "h1int", "seedArgs", "k2sig", "kBytes", "ik", "q", "normS", "defaultVerOpts", "getPublicKey", "getSharedSecret", "privateA", "publicB", "sign", "privKey", "ut.createHmacDrbg", "drbg", "verify", "signature", "public<PERSON>ey", "sg", "_sig", "<PERSON><PERSON><PERSON><PERSON>", "message", "is", "HMAC", "_key", "assertHash", "iHash", "oHash", "buf", "assertExists", "assertBytes", "getPrototypeOf", "getHash", "msgs", "secp256k1P", "secp256k1N", "divNearest", "sqrtMod", "_6n", "_11n", "_22n", "_23n", "_44n", "_88n", "b6", "b9", "b11", "b22", "b44", "b88", "b176", "b220", "b223", "secp256k1", "defHash", "createCurve", "a1", "a2", "POW_2_128", "c2", "fe", "TAGGED_HASH_PREFIXES", "taggedHash", "tag", "messages", "tagP", "tagH", "charCodeAt", "pointToBytes", "numTo32b", "modP", "GmulAdd", "schnorrGetExtPubKey", "priv", "d_", "lift_x", "xx", "challenge", "args", "schnorrGetPublicKey", "schnorrSign", "auxRand", "t", "rand", "k_", "rx", "schnorrVerify", "pub", "schnorr", "SIGN_DEFAULTS", "throws", "xonly", "sign_config", "config", "chain", "wrap", "decode", "alphabet", "digits", "input", "letter", "index", "indexOf", "join", "separator", "split", "padding", "chr", "end", "convertRadix", "carry", "done", "digit", "digitBase", "rounded", "floor", "gcd", "radix2carry", "convertRadix2", "radix2", "revPadding", "unsafeWrapper", "fn", "apply", "base64", "base64url", "genBase58", "abc", "base58", "base58check", "checksum", "payload", "newChe<PERSON><PERSON>", "oldChecksum", "BECH_ALPHABET", "POLYMOD_GENERATORS", "bech32Polymod", "pre", "chk", "bechChecksum", "words", "encodingConst", "genBech32", "encoding", "ENCODING_CONST", "_words", "fromWords", "to<PERSON><PERSON>s", "fromWordsUnsafe", "limit", "TypeError", "lowered", "toLowerCase", "toUpperCase", "sepIndex", "lastIndexOf", "endsWith", "actualLength", "decodeToBytes", "decodeUnsafe", "bech32", "bech32m", "Encoder", "b58chk", "b64url", "to_words", "to_bytes", "is_safe_num", "MAX_SAFE_INTEGER", "is_prefix", "actual", "target", "_255n", "_256n", "bigToBytes", "big", "size", "endian", "big_size", "use_le", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setUint8", "numToBytes", "num_size", "ec", "dc", "TextDecoder", "strToBytes", "bytesToStr", "hex_size", "hexstr", "match", "is_hex$1", "is_hex", "is_bytes", "every", "set_buffer", "within_size", "join_array", "buff", "bigint_replacer", "bigint_reviver", "test", "buffer_data", "char", "substring", "of", "Buff", "numToBuff", "bigToBuff", "bin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "raw", "rawToBuff", "str<PERSON><PERSON><PERSON><PERSON>", "hex<PERSON><PERSON><PERSON><PERSON>", "json", "json<PERSON>oBuff", "base64ToBuff", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bech32ToBuff", "bech32mToBuff", "b58chk<PERSON>oBuff", "parse", "parse_data", "random", "now", "stamp", "Date", "to_num", "to_big", "to_str", "to_hex", "to_bin", "to_b58chk", "to_base64", "to_b<PERSON>url", "to_hash", "id", "stream", "Stream", "bytesToNum", "bytesToBig", "count", "bytesToBin", "to_json", "reviver", "JSON", "to_bech32", "to_bech32m", "chars", "append", "prepend", "start", "begin", "write", "add_varint", "calc_varint", "joined", "sort", "binary", "bins", "ct", "binToBytes", "replacer", "stringify", "chk_prefix", "data_blob", "chunk_size", "total_size", "chunks", "idx", "parse_data$1", "peek", "read", "chunk", "read_varint", "_N", "_P", "_G", "exp", "ok", "fail", "<PERSON><PERSON><PERSON>", "NFD", "NoblePoint", "N", "is_valid", "assert.in_field", "math.modN", "normalizeField", "fd", "U32_MASK64", "fromBig", "le", "u64$1", "Ah", "Al", "toBig", "shrSH", "_l", "shrSL", "rotrSH", "rotrSL", "rotrBH", "rotrBL", "rotr32H", "_h", "rotr32L", "rotlSH", "rotlSL", "rotlBH", "rotlBL", "Bh", "Bl", "add3L", "Cl", "add3H", "low", "Ch", "add4L", "Dl", "add4H", "Dh", "add5H", "Eh", "add5L", "El", "SHA512_Kh", "SHA512_Kl", "u64", "SHA512_W_H", "SHA512_W_L", "SHA512", "Fh", "Fl", "Gh", "Gl", "Hh", "Hl", "W15h", "W15l", "s0h", "s0l", "W2h", "W2l", "s1h", "s1l", "SUMl", "SUMh", "sigma1h", "sigma1l", "CHIh", "CHIl", "T1ll", "T1h", "T1l", "sigma0h", "sigma0l", "MAJh", "MAJl", "All", "sha512", "Rho", "Id", "idxL", "idxR", "shifts", "shiftsL", "shiftsR", "Kl", "Kr", "rotl", "group", "BUF", "RIPEMD160", "h0", "h1", "h2", "h3", "h4", "al", "ar", "bl", "br", "cl", "cr", "dl", "dr", "el", "er", "rGroup", "hbl", "hbr", "rr", "sr", "tl", "tr", "ripemd160", "s256", "pubkey", "INT_REGEX", "STR_REGEX", "derive", "path", "input_key", "chain_code", "is_private", "assert.valid_chain", "tags", "seckey", "params", "options", "kind", "content", "util.stringify", "get_pubkey", "ref", "img", "pid", "ECPoint", "<PERSON>", "is_even", "is_point"], "mappings": "0CAAA,SAASA,EAAOC,GACZ,IAAKC,OAAOC,cAAcF,IAAMA,EAAI,EAChC,MAAM,IAAIG,MAAM,2BAA2BH,IACnD,CAKA,SAASI,EAAMC,KAAMC,GACjB,KAAMD,aAAaE,YACf,MAAM,IAAIJ,MAAM,uBACpB,GAAIG,EAAQE,OAAS,IAAMF,EAAQG,SAASJ,EAAEG,QAC1C,MAAM,IAAIL,MAAM,iCAAiCG,oBAA0BD,EAAEG,SACrF,CAOA,SAASE,EAAOC,EAAUC,GAAgB,GACtC,GAAID,EAASE,UACT,MAAM,IAAIV,MAAM,oCACpB,GAAIS,GAAiBD,EAASG,SAC1B,MAAM,IAAIX,MAAM,wCACxB,CCzBO,MAAMY,EAA+B,iBAAfC,YAA2B,WAAYA,WAAaA,WAAWD,YAASE,ECQ/FC,EAAOC,GAAMA,aAAaZ,WAKnBa,EAAcC,GAAQ,IAAIC,SAASD,EAAIE,OAAQF,EAAIG,WAAYH,EAAII,YAEnEC,EAAO,CAACC,EAAMC,IAAWD,GAAS,GAAKC,EAAWD,IAASC;sEAIxE,KADgF,KAA5D,IAAIrB,WAAW,IAAIsB,YAAY,CAAC,YAAaN,QAAQ,IAErE,MAAM,IAAIpB,MAAM,+CAiEb,SAAS2B,EAAQC,GAGpB,GAFoB,iBAATA,IACPA,EAZD,SAAqBC,GACxB,GAAmB,iBAARA,EACP,MAAM,IAAI7B,MAAM,2CAA2C6B,GAC/D,OAAO,IAAIzB,YAAW,IAAI0B,aAAcC,OAAOF,GACnD,CAQeG,CAAYJ,KAClBb,EAAIa,GACL,MAAM,IAAI5B,MAAM,mCAAmC4B,GACvD,OAAOA,CACX,CAgBO,IAAAK,EAAA,MAEH,KAAAC,GACI,OAAOC,KAAKC,YACf,GASE,SAASC,EAAgBC,GAC5B,MAAMC,EAASC,GAAQF,IAAWG,OAAOd,EAAQa,IAAME,SACjDC,EAAML,IAIZ,OAHAC,EAAMK,UAAYD,EAAIC,UACtBL,EAAMM,SAAWF,EAAIE,SACrBN,EAAMO,OAAS,IAAMR,IACdC,CACX,CAoBO,SAASQ,EAAYC,EAAc,IACtC,GAAIpC,GAA4C,mBAA3BA,EAAOqC,gBACxB,OAAOrC,EAAOqC,gBAAgB,IAAI7C,WAAW4C,IAEjD,MAAM,IAAIhD,MAAM,yCACpB,OCxIO,cAAmBkD,EACtB,WAAAC,CAAYN,EAAUD,EAAWQ,EAAWC,GACxCC,QACAnB,KAAKU,SAAWA,EAChBV,KAAKS,UAAYA,EACjBT,KAAKiB,UAAYA,EACjBjB,KAAKkB,KAAOA,EACZlB,KAAKxB,UAAW,EAChBwB,KAAK9B,OAAS,EACd8B,KAAKoB,IAAM,EACXpB,KAAKzB,WAAY,EACjByB,KAAKf,OAAS,IAAIhB,WAAWyC,GAC7BV,KAAKqB,KAAOvC,EAAWkB,KAAKf,OAC/B,CACD,MAAAqB,CAAOb,GACHrB,EAAO4B,MACP,MAAMqB,KAAEA,EAAIpC,OAAEA,EAAMyB,SAAEA,GAAaV,KAE7BsB,GADN7B,EAAOD,EAAQC,IACEvB,OACjB,IAAK,IAAIkD,EAAM,EAAGA,EAAME,GAAM,CAC1B,MAAMC,EAAOC,KAAKC,IAAIf,EAAWV,KAAKoB,IAAKE,EAAMF,GAEjD,GAAIG,IAASb,EAMbzB,EAAOyC,IAAIjC,EAAKkC,SAASP,EAAKA,EAAMG,GAAOvB,KAAKoB,KAChDpB,KAAKoB,KAAOG,EACZH,GAAOG,EACHvB,KAAKoB,MAAQV,IACbV,KAAK4B,QAAQP,EAAM,GACnBrB,KAAKoB,IAAM,OAXf,CACI,MAAMS,EAAW/C,EAAWW,GAC5B,KAAOiB,GAAYY,EAAMF,EAAKA,GAAOV,EACjCV,KAAK4B,QAAQC,EAAUT,EAE9B,CAQJ,CAGD,OAFApB,KAAK9B,QAAUuB,EAAKvB,OACpB8B,KAAK8B,aACE9B,IACV,CACD,UAAA+B,CAAWC,GACP5D,EAAO4B,MH/Bf,SAAgBgC,EAAK3D,GACjBP,EAAMkE,GACN,MAAMP,EAAMpD,EAASoC,UACrB,GAAIuB,EAAI9D,OAASuD,EACb,MAAM,IAAI5D,MAAM,yDAAyD4D,IAEjF,CG0BQQ,CAAOD,EAAKhC,MACZA,KAAKxB,UAAW,EAIhB,MAAMS,OAAEA,EAAMoC,KAAEA,EAAIX,SAAEA,EAAQQ,KAAEA,GAASlB,KACzC,IAAIoB,IAAEA,GAAQpB,KAEdf,EAAOmC,KAAS,IAChBpB,KAAKf,OAAO0C,SAASP,GAAKc,KAAK,GAE3BlC,KAAKiB,UAAYP,EAAWU,IAC5BpB,KAAK4B,QAAQP,EAAM,GACnBD,EAAM,GAGV,IAAK,IAAIe,EAAIf,EAAKe,EAAIzB,EAAUyB,IAC5BlD,EAAOkD,GAAK,GAxExB,SAAsBd,EAAMnC,EAAYkD,EAAOlB,GAC3C,GAAiC,mBAAtBG,EAAKgB,aACZ,OAAOhB,EAAKgB,aAAanD,EAAYkD,EAAOlB,GAChD,MAAMoB,EAAOC,OAAO,IACdC,EAAWD,OAAO,YAClBE,EAAK9E,OAAQyE,GAASE,EAAQE,GAC9BE,EAAK/E,OAAOyE,EAAQI,GACpBG,EAAIzB,EAAO,EAAI,EACf0B,EAAI1B,EAAO,EAAI,EACrBG,EAAKwB,UAAU3D,EAAayD,EAAGF,EAAIvB,GACnCG,EAAKwB,UAAU3D,EAAa0D,EAAGF,EAAIxB,EACvC,CAiEQmB,CAAahB,EAAMX,EAAW,EAAG6B,OAAqB,EAAdvC,KAAK9B,QAAagD,GAC1DlB,KAAK4B,QAAQP,EAAM,GACnB,MAAMyB,EAAQhE,EAAWkD,GACnBV,EAAMtB,KAAKS,UAEjB,GAAIa,EAAM,EACN,MAAM,IAAIzD,MAAM,+CACpB,MAAMkF,EAASzB,EAAM,EACf0B,EAAQhD,KAAKiD,MACnB,GAAIF,EAASC,EAAM9E,OACf,MAAM,IAAIL,MAAM,sCACpB,IAAK,IAAIsE,EAAI,EAAGA,EAAIY,EAAQZ,IACxBW,EAAMD,UAAU,EAAIV,EAAGa,EAAMb,GAAIjB,EACxC,CACD,MAAAX,GACI,MAAMtB,OAAEA,EAAMwB,UAAEA,GAAcT,KAC9BA,KAAK+B,WAAW9C,GAChB,MAAMiE,EAAMjE,EAAOkE,MAAM,EAAG1C,GAE5B,OADAT,KAAKoD,UACEF,CACV,CACD,UAAAjD,CAAWoD,GACPA,IAAOA,EAAK,IAAIrD,KAAKgB,aACrBqC,EAAG3B,OAAO1B,KAAKiD,OACf,MAAMvC,SAAEA,EAAQzB,OAAEA,EAAMf,OAAEA,EAAMM,SAAEA,EAAQD,UAAEA,EAAS6C,IAAEA,GAAQpB,KAO/D,OANAqD,EAAGnF,OAASA,EACZmF,EAAGjC,IAAMA,EACTiC,EAAG7E,SAAWA,EACd6E,EAAG9E,UAAYA,EACXL,EAASwC,GACT2C,EAAGpE,OAAOyC,IAAIzC,GACXoE,CACV,GC1GL,MAEMC,EAAM,CAACzE,EAAGd,EAAGwF,IAAO1E,EAAId,EAAMc,EAAI0E,EAAMxF,EAAIwF,EAI5CC,EAA2B,IAAIjE,YAAY,CAC7C,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WACpF,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UACpF,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UACpF,UAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,aAIlFkE,EAAqB,IAAIlE,YAAY,CACvC,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,aAIlFmE,EAA2B,IAAInE,YAAY,UACjD,cAAqBoE,EACjB,WAAA3C,GACIG,MAAM,GAAI,GAAI,GAAG,GAGjBnB,KAAK4D,EAAY,EAARH,EAAG,GACZzD,KAAK6D,EAAY,EAARJ,EAAG,GACZzD,KAAK8D,EAAY,EAARL,EAAG,GACZzD,KAAK+D,EAAY,EAARN,EAAG,GACZzD,KAAKgE,EAAY,EAARP,EAAG,GACZzD,KAAKiE,EAAY,EAARR,EAAG,GACZzD,KAAKkE,EAAY,EAART,EAAG,GACZzD,KAAKmE,EAAY,EAARV,EAAG,EACf,CACD,GAAAR,GACI,MAAMW,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,GAAMnE,KACnC,MAAO,CAAC4D,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAChC,CAED,GAAAzC,CAAIkC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACrBnE,KAAK4D,EAAQ,EAAJA,EACT5D,KAAK6D,EAAQ,EAAJA,EACT7D,KAAK8D,EAAQ,EAAJA,EACT9D,KAAK+D,EAAQ,EAAJA,EACT/D,KAAKgE,EAAQ,EAAJA,EACThE,KAAKiE,EAAQ,EAAJA,EACTjE,KAAKkE,EAAQ,EAAJA,EACTlE,KAAKmE,EAAQ,EAAJA,CACZ,CACD,OAAAvC,CAAQP,EAAM+C,GAEV,IAAK,IAAIjC,EAAI,EAAGA,EAAI,GAAIA,IAAKiC,GAAU,EACnCV,EAASvB,GAAKd,EAAKgD,UAAUD,GAAQ,GACzC,IAAK,IAAIjC,EAAI,GAAIA,EAAI,GAAIA,IAAK,CAC1B,MAAMmC,EAAMZ,EAASvB,EAAI,IACnBoC,EAAKb,EAASvB,EAAI,GAClBqC,EAAKpF,EAAKkF,EAAK,GAAKlF,EAAKkF,EAAK,IAAOA,IAAQ,EAC7CG,EAAKrF,EAAKmF,EAAI,IAAMnF,EAAKmF,EAAI,IAAOA,IAAO,GACjDb,EAASvB,GAAMsC,EAAKf,EAASvB,EAAI,GAAKqC,EAAKd,EAASvB,EAAI,IAAO,CAClE,CAED,IAAIyB,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,GAAMnE,KACjC,IAAK,IAAImC,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MACMuC,EAAMP,GADG/E,EAAK4E,EAAG,GAAK5E,EAAK4E,EAAG,IAAM5E,EAAK4E,EAAG,OAnEjDnF,EAoE4BmF,GAAGC,GApEPpF,EAoEUqF,GAAKV,EAASrB,GAAKuB,EAASvB,GAAM,EAE/DwC,GADSvF,EAAKwE,EAAG,GAAKxE,EAAKwE,EAAG,IAAMxE,EAAKwE,EAAG,KAC7BN,EAAIM,EAAGC,EAAGC,GAAM,EACrCK,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKD,EAAIW,EAAM,EACfX,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKc,EAAKC,EAAM,CACnB,CA/EG,IAAC9F,EAiFL+E,EAAKA,EAAI5D,KAAK4D,EAAK,EACnBC,EAAKA,EAAI7D,KAAK6D,EAAK,EACnBC,EAAKA,EAAI9D,KAAK8D,EAAK,EACnBC,EAAKA,EAAI/D,KAAK+D,EAAK,EACnBC,EAAKA,EAAIhE,KAAKgE,EAAK,EACnBC,EAAKA,EAAIjE,KAAKiE,EAAK,EACnBC,EAAKA,EAAIlE,KAAKkE,EAAK,EACnBC,EAAKA,EAAInE,KAAKmE,EAAK,EACnBnE,KAAK0B,IAAIkC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EACjC,CACD,UAAArC,GACI4B,EAASxB,KAAK,EACjB,CACD,OAAAkB,GACIpD,KAAK0B,IAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC9B1B,KAAKf,OAAOiD,KAAK,EACpB,GAqBE,MAAM0C,EAAyB1E,GAAgB,IAAM,IAAI2E,ICtH1DC,EAAMvC,OAAO,GACbwC,EAAMxC,OAAO,GACbyC,EAAMzC,OAAO,GACb3D,EAAOC,GAAMA,aAAaZ,WAC1BgH,EAAwBC,MAAMC,KAAK,CAAEjH,OAAQ,MAAO,CAACkH,EAAGjD,IAAMA,EAAEkD,SAAS,IAAIC,SAAS,EAAG;sEAIxF,SAASC,EAAWzH,GACvB,IAAKc,EAAId,GACL,MAAM,IAAID,MAAM,uBAEpB,IAAI2H,EAAM,GACV,IAAK,IAAIrD,EAAI,EAAGA,EAAIrE,EAAMI,OAAQiE,IAC9BqD,GAAOP,EAAMnH,EAAMqE,IAEvB,OAAOqD,CACX,CACO,SAASC,EAAoBC,GAChC,MAAMF,EAAME,EAAIL,SAAS,IACzB,OAAoB,EAAbG,EAAItH,OAAa,IAAIsH,IAAQA,CACxC,CACO,SAASG,EAAYH,GACxB,GAAmB,iBAARA,EACP,MAAM,IAAI3H,MAAM,mCAAqC2H,GAEzD,OAAOjD,OAAe,KAARiD,EAAa,IAAM,KAAKA,IAC1C,CAIO,SAASI,EAAWJ,GACvB,GAAmB,iBAARA,EACP,MAAM,IAAI3H,MAAM,mCAAqC2H,GACzD,MAAMlE,EAAMkE,EAAItH,OAChB,GAAIoD,EAAM,EACN,MAAM,IAAIzD,MAAM,0DAA4DyD,GAChF,MAAMuE,EAAQ,IAAI5H,WAAWqD,EAAM,GACnC,IAAK,IAAIa,EAAI,EAAGA,EAAI0D,EAAM3H,OAAQiE,IAAK,CACnC,MAAM2D,EAAQ,EAAJ3D,EACJ4D,EAAUP,EAAIrC,MAAM2C,EAAGA,EAAI,GAC3BE,EAAOrI,OAAOsI,SAASF,EAAS,IACtC,GAAIpI,OAAOuI,MAAMF,IAASA,EAAO,EAC7B,MAAM,IAAInI,MAAM,yBACpBgI,EAAM1D,GAAK6D,CACd,CACD,OAAOH,CACX,CAEO,SAASM,EAAgBrI,GAC5B,OAAO6H,EAAYJ,EAAWzH,GAClC,CACO,SAASsI,EAAgBtI,GAC5B,IAAKc,EAAId,GACL,MAAM,IAAID,MAAM,uBACpB,OAAO8H,EAAYJ,EAAWtH,WAAWkH,KAAKrH,GAAOuI,WACzD,CACO,SAASC,EAAgB5I,EAAG4D,GAC/B,OAAOsE,EAAWlI,EAAE2H,SAAS,IAAIC,SAAe,EAANhE,EAAS,KACvD,CACO,SAASiF,EAAgB7I,EAAG4D,GAC/B,OAAOgF,EAAgB5I,EAAG4D,GAAK+E,SACnC,CAcO,SAASG,EAAYC,EAAOjB,EAAKkB,GACpC,IAAIxD,EACJ,GAAmB,iBAARsC,EACP,IACItC,EAAM0C,EAAWJ,EACpB,CACD,MAAOmB,GACH,MAAM,IAAI9I,MAAM,GAAG4I,oCAAwCjB,cAAgBmB,IAC9E,KAEA,KAAI/H,EAAI4G,GAMT,MAAM,IAAI3H,MAAM,GAAG4I,sCAHnBvD,EAAMjF,WAAWkH,KAAKK,EAIzB,CACD,MAAMlE,EAAM4B,EAAIhF,OAChB,GAA8B,iBAAnBwI,GAA+BpF,IAAQoF,EAC9C,MAAM,IAAI7I,MAAM,GAAG4I,cAAkBC,gBAA6BpF,KACtE,OAAO4B,CACX,CAIO,SAAS0D,KAAeC,GAC3B,MAAMC,EAAI,IAAI7I,WAAW4I,EAAOE,QAAO,CAACC,EAAKnI,IAAMmI,EAAMnI,EAAEX,QAAQ,IACnE,IAAI+I,EAAM,EAOV,OANAJ,EAAOK,SAASrI,IACZ,IAAKD,EAAIC,GACL,MAAM,IAAIhB,MAAM,uBACpBiJ,EAAEpF,IAAI7C,EAAGoI,GACTA,GAAOpI,EAAEX,MAAM,IAEZ4I,CACX,CAwCO,MAOMK,EAAWzJ,IAAOsH,GAAOzC,OAAO7E,EAAI,IAAMqH,EAEjDqC,EAAO3H,GAAS,IAAIxB,WAAWwB,GAC/B4H,EAAQtI,GAAQd,WAAWkH,KAAKpG,GAQ/B,SAASuI,EAAeC,EAASC,EAAUC,GAC9C,GAAuB,iBAAZF,GAAwBA,EAAU,EACzC,MAAM,IAAI1J,MAAM,4BACpB,GAAwB,iBAAb2J,GAAyBA,EAAW,EAC3C,MAAM,IAAI3J,MAAM,6BACpB,GAAsB,mBAAX4J,EACP,MAAM,IAAI5J,MAAM,6BAEpB,IAAI6J,EAAIN,EAAIG,GACRI,EAAIP,EAAIG,GACRpF,EAAI,EACR,MAAMyF,EAAQ,KACVF,EAAExF,KAAK,GACPyF,EAAEzF,KAAK,GACPC,EAAI,CAAC,EAEHQ,EAAI,IAAI5E,IAAM0J,EAAOE,EAAGD,KAAM3J,GAC9B8J,EAAS,CAACC,EAAOV,OAEnBO,EAAIhF,EAAE0E,EAAK,CAAC,IAAQS,GACpBJ,EAAI/E,IACgB,IAAhBmF,EAAK5J,SAETyJ,EAAIhF,EAAE0E,EAAK,CAAC,IAAQS,GACpBJ,EAAI/E,IAAG,EAELoF,EAAM,KAER,GAAI5F,KAAO,IACP,MAAM,IAAItE,MAAM,2BACpB,IAAIyD,EAAM,EACV,MAAMU,EAAM,GACZ,KAAOV,EAAMkG,GAAU,CACnBE,EAAI/E,IACJ,MAAMqF,EAAKN,EAAEvE,QACbnB,EAAIiG,KAAKD,GACT1G,GAAOoG,EAAExJ,MACZ,CACD,OAAO0I,KAAe5E,EAAI,EAW9B,MATiB,CAAC8F,EAAMI,KAGpB,IAAIhF,EACJ,IAHA0E,IACAC,EAAOC,KAEE5E,EAAMgF,EAAKH,OAChBF,IAEJ,OADAD,IACO1E,CAAG,CAGlB,CAEA,MAAMiF,EAAe,CACjBC,OAASC,GAAuB,iBAARA,EACxBC,SAAWD,GAAuB,mBAARA,EAC1BE,QAAUF,GAAuB,kBAARA,EACzBG,OAASH,GAAuB,iBAARA,EACxBI,mBAAqBJ,GAAuB,iBAARA,GAAoBA,aAAepK,WACvEL,cAAgByK,GAAQ1K,OAAOC,cAAcyK,GAC7CxC,MAAQwC,GAAQnD,MAAMwD,QAAQL,GAC9BM,MAAO,CAACN,EAAKO,IAAWA,EAAOC,GAAGC,QAAQT,GAC1CU,KAAOV,GAAuB,mBAARA,GAAsB1K,OAAOC,cAAcyK,EAAI5H,YAGlE,SAASuI,EAAeJ,EAAQK,EAAYC,EAAgB,CAAA,GAC/D,MAAMC,EAAa,CAACC,EAAWC,EAAMC,KACjC,MAAMC,EAAWpB,EAAakB,GAC9B,GAAwB,mBAAbE,EACP,MAAM,IAAI1L,MAAM,sBAAsBwL,yBAC1C,MAAMhB,EAAMO,EAAOQ,GACnB,KAAIE,QAAsB3K,IAAR0J,GAEbkB,EAASlB,EAAKO,IACf,MAAM,IAAI/K,MAAM,iBAAiB2L,OAAOJ,MAAcf,aAAeA,gBAAkBgB,IAC1F,EAEL,IAAK,MAAOD,EAAWC,KAASI,OAAOC,QAAQT,GAC3CE,EAAWC,EAAWC,GAAM,GAChC,IAAK,MAAOD,EAAWC,KAASI,OAAOC,QAAQR,GAC3CC,EAAWC,EAAWC,GAAM,GAChC,OAAOT,CACX,4CAzGO,SAAgBlL,EAAG0D,GACtB,OAAQ1D,GAAK6E,OAAOnB,GAAQ2D,CAChC,SAbO,SAAgBrH,GACnB,IAAI4D,EACJ,IAAKA,EAAM,EAAG5D,EAAIoH,EAAKpH,IAAMqH,EAAKzD,GAAO,GAEzC,OAAOA,CACX,mBAYsB,CAAC5D,EAAG0D,EAAKgB,IACpB1E,GAAM0E,EAAQ2C,EAAMD,IAAQvC,OAAOnB,4GAxCvC,SAAoBuI,EAAIC,GAE3B,GAAID,EAAGzL,SAAW0L,EAAG1L,OACjB,OAAO,EACX,IAAK,IAAIiE,EAAI,EAAGA,EAAIwH,EAAGzL,OAAQiE,IAC3B,GAAIwH,EAAGxH,KAAOyH,EAAGzH,GACb,OAAO,EACf,OAAO,CACX,0GAzDO,SAA4BzE,GAC/B,OAAOkI,EAAWH,EAAoB/H,GAC1C,cA2DO,SAAqBgC,GACxB,GAAmB,iBAARA,EACP,MAAM,IAAI7B,MAAM,2CAA2C6B,GAC/D,OAAO,IAAIzB,YAAW,IAAI0B,aAAcC,OAAOF,GACnD;sEClIA,MAAMoF,EAAMvC,OAAO,GAAIwC,EAAMxC,OAAO,GAAIyC,EAAMzC,OAAO,GAAIsH,EAAMtH,OAAO,GAEhEuH,EAAMvH,OAAO,GAAIwH,EAAMxH,OAAO,GAAIyH,EAAMzH,OAAO,GAI9C,SAAS0H,EAAIpL,EAAGd,GACnB,MAAMmM,EAASrL,EAAId,EACnB,OAAOmM,GAAUpF,EAAMoF,EAASnM,EAAImM,CACxC,CAQO,SAASC,EAAIzE,EAAK0E,EAAOC,GAC5B,GAAIA,GAAUvF,GAAOsF,EAAQtF,EACzB,MAAM,IAAIjH,MAAM,6BACpB,GAAIwM,IAAWtF,EACX,OAAOD,EACX,IAAI5B,EAAM6B,EACV,KAAOqF,EAAQtF,GACPsF,EAAQrF,IACR7B,EAAOA,EAAMwC,EAAO2E,GACxB3E,EAAOA,EAAMA,EAAO2E,EACpBD,IAAUrF,EAEd,OAAO7B,CACX,CAEO,SAASoH,EAAKC,EAAGH,EAAOC,GAC3B,IAAInH,EAAMqH,EACV,KAAOH,KAAUtF,GACb5B,GAAOA,EACPA,GAAOmH,EAEX,OAAOnH,CACX,CAEO,SAASsH,EAAO/M,EAAQ4M,GAC3B,GAAI5M,IAAWqH,GAAOuF,GAAUvF,EAC5B,MAAM,IAAIjH,MAAM,6CAA6CJ,SAAc4M,KAI/E,IAAIxL,EAAIoL,EAAIxM,EAAQ4M,GAChBtM,EAAIsM,EAEJE,EAAIzF,EAAc2F,EAAI1F,EAC1B,KAAOlG,IAAMiG,GAAK,CAEd,MACMgC,EAAI/I,EAAIc,EACR6L,EAAIH,EAAIE,GAFJ1M,EAAIc,GAKdd,EAAIc,EAAGA,EAAIiI,EAAGyD,EAAIE,EAAUA,EAAIC,CACnC,CAED,GADY3M,IACAgH,EACR,MAAM,IAAIlH,MAAM,0BACpB,OAAOoM,EAAIM,EAAGF,EAClB,CAiEO,SAASM,EAAOC,GAKnB,GAAIA,EAAId,IAAQD,EAAK,CAKjB,MAAMgB,GAAUD,EAAI7F,GAAO+E,EAC3B,OAAO,SAAmBjB,EAAInL,GAC1B,MAAMoN,EAAOjC,EAAGsB,IAAIzM,EAAGmN,GAEvB,IAAKhC,EAAGkC,IAAIlC,EAAGmC,IAAIF,GAAOpN,GACtB,MAAM,IAAIG,MAAM,2BACpB,OAAOiN,CACnB,CACK,CAED,GAAIF,EAAIZ,IAAQD,EAAK,CACjB,MAAMkB,GAAML,EAAIb,GAAOC,EACvB,OAAO,SAAmBnB,EAAInL,GAC1B,MAAMwN,EAAKrC,EAAGsC,IAAIzN,EAAGsH,GACf0C,EAAImB,EAAGsB,IAAIe,EAAID,GACfG,EAAKvC,EAAGsC,IAAIzN,EAAGgK,GACfvF,EAAI0G,EAAGsC,IAAItC,EAAGsC,IAAIC,EAAIpG,GAAM0C,GAC5BoD,EAAOjC,EAAGsC,IAAIC,EAAIvC,EAAGwC,IAAIlJ,EAAG0G,EAAGyC,MACrC,IAAKzC,EAAGkC,IAAIlC,EAAGmC,IAAIF,GAAOpN,GACtB,MAAM,IAAIG,MAAM,2BACpB,OAAOiN,CACnB,CACK,CAwBD,OAhHG,SAAuBF,GAM1B,MAAMW,GAAaX,EAAI7F,GAAOC,EAC9B,IAAIwG,EAAGC,EAAGC,EAGV,IAAKF,EAAIZ,EAAI7F,EAAK0G,EAAI,EAAGD,EAAIxG,IAAQF,EAAK0G,GAAKxG,EAAKyG,KAGpD,IAAKC,EAAI1G,EAAK0G,EAAId,GAAKT,EAAIuB,EAAGH,EAAWX,KAAOA,EAAI7F,EAAK2G,KAGzD,GAAU,IAAND,EAAS,CACT,MAAMZ,GAAUD,EAAI7F,GAAO+E,EAC3B,OAAO,SAAqBjB,EAAInL,GAC5B,MAAMoN,EAAOjC,EAAGsB,IAAIzM,EAAGmN,GACvB,IAAKhC,EAAGkC,IAAIlC,EAAGmC,IAAIF,GAAOpN,GACtB,MAAM,IAAIG,MAAM,2BACpB,OAAOiN,CACnB,CACK,CAED,MAAMa,GAAUH,EAAIzG,GAAOC,EAC3B,OAAO,SAAqB6D,EAAInL,GAE5B,GAAImL,EAAGsB,IAAIzM,EAAG6N,KAAe1C,EAAG+C,IAAI/C,EAAGyC,KACnC,MAAM,IAAIzN,MAAM,2BACpB,IAAIiJ,EAAI2E,EAEJI,EAAIhD,EAAGsB,IAAItB,EAAGsC,IAAItC,EAAGyC,IAAKI,GAAIF,GAC9BjB,EAAI1B,EAAGsB,IAAIzM,EAAGiO,GACd5N,EAAI8K,EAAGsB,IAAIzM,EAAG8N,GAClB,MAAQ3C,EAAGkC,IAAIhN,EAAG8K,EAAGyC,MAAM,CACvB,GAAIzC,EAAGkC,IAAIhN,EAAG8K,EAAGiD,MACb,OAAOjD,EAAGiD,KAEd,IAAIpB,EAAI,EACR,IAAK,IAAIqB,EAAKlD,EAAGmC,IAAIjN,GAAI2M,EAAI5D,IACrB+B,EAAGkC,IAAIgB,EAAIlD,EAAGyC,KADUZ,IAG5BqB,EAAKlD,EAAGmC,IAAIe,GAGhB,MAAMC,EAAKnD,EAAGsB,IAAI0B,EAAG9G,GAAOxC,OAAOuE,EAAI4D,EAAI,IAC3CmB,EAAIhD,EAAGmC,IAAIgB,GACXzB,EAAI1B,EAAGsC,IAAIZ,EAAGyB,GACdjO,EAAI8K,EAAGsC,IAAIpN,EAAG8N,GACd/E,EAAI4D,CACP,CACD,OAAOH,CACf,CACA,CAyDW0B,CAAcrB,EACzB,CAtLYrI,OAAO,GAAWA,OAAO,IA0LrC,MAAM2J,EAAe,CACjB,SAAU,UAAW,MAAO,MAAO,MAAO,OAAQ,MAClD,MAAO,MAAO,MAAO,MAAO,MAAO,MACnC,OAAQ,OAAQ,OAAQ,QA2ErB,SAASC,GAAQzO,EAAG0O,GAEvB,MAAMC,OAA6B1N,IAAfyN,EAA2BA,EAAa1O,EAAE2H,SAAS,GAAGnH,OAE1E,MAAO,CAAEkO,WAAYC,EAAaC,YADd9K,KAAK+K,KAAKF,EAAc,GAEhD,CAaO,SAASG,GAAMC,EAAOC,EAAQxL,GAAO,EAAOyL,EAAQ,IACvD,GAAIF,GAAS3H,EACT,MAAM,IAAIjH,MAAM,iCAAiC4O,KACrD,MAAQL,WAAYQ,EAAMN,YAAaO,GAAUV,GAAQM,EAAOC,GAChE,GAAIG,EAAQ,KACR,MAAM,IAAIhP,MAAM,mDACpB,MAAMiP,EAAQnC,EAAO8B,GACfM,EAAItD,OAAOuD,OAAO,CACpBP,QACAG,OACAC,QACAI,KAAM9F,EAAQyF,GACdd,KAAMhH,EACNwG,IAAKvG,EACLpE,OAAS+E,GAAQuE,EAAIvE,EAAK+G,GAC1B3D,QAAUpD,IACN,GAAmB,iBAARA,EACP,MAAM,IAAI7H,MAAM,sDAAsD6H,GAC1E,OAAOZ,GAAOY,GAAOA,EAAM+G,CAAK,EAEpCS,IAAMxH,GAAQA,IAAQZ,EACtBqI,MAAQzH,IAASA,EAAMX,KAASA,EAChC6G,IAAMlG,GAAQuE,GAAKvE,EAAK+G,GACxB1B,IAAK,CAACqC,EAAKC,IAAQD,IAAQC,EAC3BrC,IAAMtF,GAAQuE,EAAIvE,EAAMA,EAAK+G,GAC7Ba,IAAK,CAACF,EAAKC,IAAQpD,EAAImD,EAAMC,EAAKZ,GAClCpB,IAAK,CAAC+B,EAAKC,IAAQpD,EAAImD,EAAMC,EAAKZ,GAClCtB,IAAK,CAACiC,EAAKC,IAAQpD,EAAImD,EAAMC,EAAKZ,GAClCtC,IAAK,CAACzE,EAAK0E,IArGZ,SAAe2C,EAAGrH,EAAK0E,GAG1B,GAAIA,EAAQtF,EACR,MAAM,IAAIjH,MAAM,sBACpB,GAAIuM,IAAUtF,EACV,OAAOiI,EAAEzB,IACb,GAAIlB,IAAUrF,EACV,OAAOW,EACX,IAAI6H,EAAIR,EAAEzB,IACNkC,EAAI9H,EACR,KAAO0E,EAAQtF,GACPsF,EAAQrF,IACRwI,EAAIR,EAAE5B,IAAIoC,EAAGC,IACjBA,EAAIT,EAAE/B,IAAIwC,GACVpD,IAAUrF,EAEd,OAAOwI,CACX,CAmF6BE,CAAMV,EAAGrH,EAAK0E,GACnCsD,IAAK,CAACN,EAAKC,IAAQpD,EAAImD,EAAM5C,EAAO6C,EAAKZ,GAAQA,GAEjDkB,KAAOjI,GAAQA,EAAMA,EACrBkI,KAAM,CAACR,EAAKC,IAAQD,EAAMC,EAC1BQ,KAAM,CAACT,EAAKC,IAAQD,EAAMC,EAC1BS,KAAM,CAACV,EAAKC,IAAQD,EAAMC,EAC1BU,IAAMrI,GAAQ8E,EAAO9E,EAAK+G,GAC1BuB,KAAMrB,EAAMqB,MAAS,CAACtQ,GAAMoP,EAAMC,EAAGrP,IACrCuQ,YAAcC,GAvFf,SAAuBnB,EAAGoB,GAC7B,MAAM3N,EAAM,IAAI0E,MAAMiJ,EAAKjQ,QAErBkQ,EAAiBD,EAAKpH,QAAO,CAACsH,EAAK3I,EAAKvD,IACtC4K,EAAEG,IAAIxH,GACC2I,GACX7N,EAAI2B,GAAKkM,EACFtB,EAAE5B,IAAIkD,EAAK3I,KACnBqH,EAAEzB,KAECgD,EAAWvB,EAAEgB,IAAIK,GAQvB,OANAD,EAAKI,aAAY,CAACF,EAAK3I,EAAKvD,IACpB4K,EAAEG,IAAIxH,GACC2I,GACX7N,EAAI2B,GAAK4K,EAAE5B,IAAIkD,EAAK7N,EAAI2B,IACjB4K,EAAE5B,IAAIkD,EAAK3I,KACnB4I,GACI9N,CACX,CAoE8BgO,CAAczB,EAAGmB,GAGvCO,KAAM,CAAC5P,EAAGd,EAAGwF,IAAOA,EAAIxF,EAAIc,EAC5BW,QAAUkG,GAASxE,EAAOqF,EAAgBb,EAAKmH,GAASvG,EAAgBZ,EAAKmH,GAC7E6B,UAAY5Q,IACR,GAAIA,EAAMI,SAAW2O,EACjB,MAAM,IAAIhP,MAAM,0BAA0BgP,UAAc/O,EAAMI,UAClE,OAAOgD,EAAOkF,EAAgBtI,GAASqI,EAAgBrI,EAAM,IAGrE,OAAO2L,OAAOuD,OAAOD,EACzB,CAkCO,SAAS4B,GAAoBC,GAChC,GAA0B,iBAAfA,EACP,MAAM,IAAI/Q,MAAM,8BACpB,MAAMgR,EAAYD,EAAWvJ,SAAS,GAAGnH,OACzC,OAAOsD,KAAK+K,KAAKsC,EAAY,EACjC,CAQO,SAASC,GAAiBF,GAC7B,MAAM1Q,EAASyQ,GAAoBC,GACnC,OAAO1Q,EAASsD,KAAK+K,KAAKrO,EAAS,EACvC;;ACjYA,MAAM4G,GAAMvC,OAAO,GACbwC,GAAMxC,OAAO,GAoIZ,SAASwM,GAAcC,GAY1B,OD6DOhG,ECxEOgG,EAAMnG,GDoEPqD,EAAanF,QAAO,CAACkI,EAAK5G,KACnC4G,EAAI5G,GAAO,WACJ4G,IARK,CACZxC,MAAO,SACPQ,KAAM,SACNJ,MAAO,gBACPD,KAAM,mBCjEV5D,EAAegG,EAAO,CAClBtR,EAAG,SACHiF,EAAG,SACHuM,GAAI,QACJC,GAAI,SACL,CACC/C,WAAY,gBACZE,YAAa,kBAGV7C,OAAOuD,OAAO,IACdb,GAAQ6C,EAAMtR,EAAGsR,EAAM5C,eACvB4C,EACEzB,EAAGyB,EAAMnG,GAAG4D,OAEzB;sECxHA,MAAQtG,gBAAiBiJ,GAAKxJ,WAAYyJ,IAAQC,EACrCC,GAAM,CAEfC,IAAK,cAAqB3R,MACtB,WAAAmD,CAAY0J,EAAI,IACZvJ,MAAMuJ,EACT,GAEL,SAAA+E,CAAUhQ,GACN,MAAQ+P,IAAKxL,GAAMuL,GACnB,GAAI9P,EAAKvB,OAAS,GAAiB,IAAZuB,EAAK,GACxB,MAAM,IAAIuE,EAAE,iCAChB,MAAM1C,EAAM7B,EAAK,GACXyD,EAAMzD,EAAKkC,SAAS,EAAGL,EAAM,GACnC,IAAKA,GAAO4B,EAAIhF,SAAWoD,EACvB,MAAM,IAAI0C,EAAE,2CAKhB,GAAa,IAATd,EAAI,GACJ,MAAM,IAAIc,EAAE,uCAChB,GAAe,IAAXd,EAAI,MAA0B,IAATA,EAAI,IACzB,MAAM,IAAIc,EAAE,uDAChB,MAAO,CAAEwJ,EAAG4B,GAAIlM,GAAMN,EAAGnD,EAAKkC,SAASL,EAAM,GAChD,EACD,KAAAoO,CAAMlK,GAEF,MAAQgK,IAAKxL,GAAMuL,GACb9P,EAAsB,iBAAR+F,EAAmB6J,GAAI7J,GAAOA,EAClD,KAAM/F,aAAgBxB,YAClB,MAAM,IAAIJ,MAAM,iBACpB,IAAI+E,EAAInD,EAAKvB,OACb,GAAI0E,EAAI,GAAgB,IAAXnD,EAAK,GACd,MAAM,IAAIuE,EAAE,yBAChB,GAAIvE,EAAK,KAAOmD,EAAI,EAChB,MAAM,IAAIoB,EAAE,uCAChB,MAAQwJ,EAAG1G,EAAGlE,EAAG+M,GAAWJ,GAAIE,UAAUhQ,EAAKkC,SAAS,KAChD6L,EAAGoC,EAAGhN,EAAGiN,GAAeN,GAAIE,UAAUE,GAC9C,GAAIE,EAAW3R,OACX,MAAM,IAAI8F,EAAE,+CAChB,MAAO,CAAE8C,IAAG8I,IACf,EACD,UAAAE,CAAWC,GAEP,MAAM5M,EAASyM,GAAmC,EAA5BjS,OAAOsI,SAAS2J,EAAE,GAAI,IAAe,KAAOA,EAAIA,EAChEjN,EAAK+C,IACP,MAAMF,EAAME,EAAIL,SAAS,IACzB,OAAoB,EAAbG,EAAItH,OAAa,IAAIsH,IAAQA,CAAG,EAErCoK,EAAIzM,EAAMR,EAAEoN,EAAIH,IAChB9I,EAAI3D,EAAMR,EAAEoN,EAAIjJ,IAChBkJ,EAAMJ,EAAE1R,OAAS,EACjB+R,EAAMnJ,EAAE5I,OAAS,EACjB8J,EAAKrF,EAAEqN,GACPE,EAAKvN,EAAEsN,GACb,MAAO,KAAKtN,EAAEsN,EAAMD,EAAM,OAAOE,IAAKpJ,MAAMkB,IAAK4H,GACpD,GAIC9K,GAAMvC,OAAO,GAAIwC,GAAMxC,OAAO,GAAUA,OAAO,GAAG,MAACsH,GAAMtH,OAAO,GAC/D,SAAS4N,GAAkBC,GAC9B,MAAMC,EA3FV,SAA2BrB,GACvB,MAAMoB,EAAOrB,GAAcC,GAC3BsB,EAAkBF,EAAM,CACpBvR,EAAG,QACHd,EAAG,SACJ,CACCwS,yBAA0B,QAC1BC,eAAgB,UAChBC,cAAe,WACfC,cAAe,WACfC,mBAAoB,UACpBjC,UAAW,WACXlP,QAAS,aAEb,MAAMoR,KAAEA,EAAI/H,GAAEA,EAAEhK,EAAEA,GAAMuR,EACxB,GAAIQ,EAAM,CACN,IAAK/H,EAAGkC,IAAIlM,EAAGgK,EAAGiD,MACd,MAAM,IAAIjO,MAAM,qEAEpB,GAAoB,iBAAT+S,GACc,iBAAdA,EAAKC,MACgB,mBAArBD,EAAKE,YACZ,MAAM,IAAIjT,MAAM,oEAEvB,CACD,OAAO4L,OAAOuD,OAAO,IAAKoD,GAC9B,CAiEkBW,CAAkBX,IAC1BvH,GAAEA,GAAOwH,EACT7Q,EAAU6Q,EAAM7Q,SAC1B,EAAUwR,EAAIC,EAAOC,KACT,MAAMrS,EAAIoS,EAAME,WAChB,OAAOC,EAAenT,WAAWkH,KAAK,CAAC,IAAQ0D,EAAGrJ,QAAQX,EAAE0L,GAAI1B,EAAGrJ,QAAQX,EAAEwS,GAChF,GACC3C,EAAY2B,EAAM3B,WACnB,CAAC5Q,IAEE,MAAMwT,EAAOxT,EAAM6D,SAAS,GAI5B,MAAO,CAAE4I,EAFC1B,EAAG6F,UAAU4C,EAAK3P,SAAS,EAAGkH,EAAGgE,QAE/BwE,EADFxI,EAAG6F,UAAU4C,EAAK3P,SAASkH,EAAGgE,MAAO,EAAIhE,EAAGgE,QAEzD,GAKL,SAAS0E,EAAoBhH,GACzB,MAAM1L,EAAEA,EAACd,EAAEA,GAAMsS,EACXmB,EAAK3I,EAAGmC,IAAIT,GACZkH,EAAK5I,EAAGsC,IAAIqG,EAAIjH,GACtB,OAAO1B,EAAGyE,IAAIzE,EAAGyE,IAAImE,EAAI5I,EAAGsC,IAAIZ,EAAG1L,IAAKd,EAC3C,CAKD,IAAK8K,EAAGkC,IAAIlC,EAAGmC,IAAIqF,EAAMlB,IAAKoC,EAAoBlB,EAAMnB,KACpD,MAAM,IAAIrR,MAAM,+CAEpB,SAAS6T,EAAmBhM,GACxB,MAAsB,iBAARA,GAAoBZ,GAAMY,GAAOA,EAAM2K,EAAM3S,CAC9D,CACD,SAASiU,EAASjM,GACd,IAAKgM,EAAmBhM,GACpB,MAAM,IAAI7H,MAAM,8CACvB,CAGD,SAAS+T,EAAuBC,GAC5B,MAAQtB,yBAA0BvS,EAAOsO,YAAEA,EAAWkE,eAAEA,EAAc9S,EAAEA,GAAM2S,EAC9E,GAAIrS,GAA0B,iBAAR6T,EAAkB,CAIpC,GAHIA,aAAe5T,aACf4T,EAAMC,EAAcD,IAEL,iBAARA,IAAqB7T,EAAQG,SAAS0T,EAAI3T,QACjD,MAAM,IAAIL,MAAM,eACpBgU,EAAMA,EAAIvM,SAAuB,EAAdgH,EAAiB,IACvC,CACD,IAAI5G,EACJ,IACIA,EACmB,iBAARmM,EACDA,EACAE,EAAmBvL,EAAY,cAAeqL,EAAKvF,GAChE,CACD,MAAO0F,GACH,MAAM,IAAInU,MAAM,uBAAuByO,sCAAgDuF,IAC1F,CAID,OAHIrB,IACA9K,EAAMuM,EAAQvM,EAAKhI,IACvBiU,EAASjM,GACFA,CACV,CACD,MAAMwM,EAAmB,IAAIC,IAC7B,SAASC,EAAeC,GACpB,KAAMA,aAAiBC,GACnB,MAAM,IAAIzU,MAAM,2BACvB,CAMD,MAAMyU,EACF,WAAAtR,CAAYuR,EAAIC,EAAIC,GAIhB,GAHAzS,KAAKuS,GAAKA,EACVvS,KAAKwS,GAAKA,EACVxS,KAAKyS,GAAKA,EACA,MAANF,IAAe1J,EAAGC,QAAQyJ,GAC1B,MAAM,IAAI1U,MAAM,cACpB,GAAU,MAAN2U,IAAe3J,EAAGC,QAAQ0J,GAC1B,MAAM,IAAI3U,MAAM,cACpB,GAAU,MAAN4U,IAAe5J,EAAGC,QAAQ2J,GAC1B,MAAM,IAAI5U,MAAM,aACvB,CAGD,iBAAO6U,CAAWnF,GACd,MAAMhD,EAAEA,EAAC8G,EAAEA,GAAM9D,GAAK,CAAA,EACtB,IAAKA,IAAM1E,EAAGC,QAAQyB,KAAO1B,EAAGC,QAAQuI,GACpC,MAAM,IAAIxT,MAAM,wBACpB,GAAI0P,aAAa+E,EACb,MAAM,IAAIzU,MAAM,gCACpB,MAAMqP,EAAO/K,GAAM0G,EAAGkC,IAAI5I,EAAG0G,EAAGiD,MAEhC,OAAIoB,EAAI3C,IAAM2C,EAAImE,GACPiB,EAAMxG,KACV,IAAIwG,EAAM/H,EAAG8G,EAAGxI,EAAGyC,IAC7B,CACD,KAAIf,GACA,OAAOvK,KAAKmR,WAAW5G,CAC1B,CACD,KAAI8G,GACA,OAAOrR,KAAKmR,WAAWE,CAC1B,CAOD,iBAAOsB,CAAWC,GACd,MAAMC,EAAQhK,EAAGoF,YAAY2E,EAAO3D,KAAK1B,GAAMA,EAAEkF,MACjD,OAAOG,EAAO3D,KAAI,CAAC1B,EAAGpL,IAAMoL,EAAE4D,SAAS0B,EAAM1Q,MAAK8M,IAAIqD,EAAMI,WAC/D,CAKD,cAAOI,CAAQtN,GACX,MAAMoF,EAAI0H,EAAMI,WAAWhE,EAAUlI,EAAY,WAAYhB,KAE7D,OADAoF,EAAEmI,iBACKnI,CACV,CAED,qBAAOoI,CAAeC,GAClB,OAAOX,EAAMY,KAAKC,SAASvB,EAAuBqB,GACrD,CAED,cAAAG,CAAeC,GACXrT,KAAKsT,aAAeD,EACpBnB,EAAiBqB,OAAOvT,KAC3B,CAED,cAAA+S,GACI,GAAI/S,KAAKkN,MAAO,CAIZ,GAAImD,EAAMM,qBAAuB9H,EAAGqE,IAAIlN,KAAKwS,IACzC,OACJ,MAAM,IAAI3U,MAAM,kBACnB,CAED,MAAM0M,EAAEA,EAAC8G,EAAEA,GAAMrR,KAAKmR,WAEtB,IAAKtI,EAAGC,QAAQyB,KAAO1B,EAAGC,QAAQuI,GAC9B,MAAM,IAAIxT,MAAM,4BACpB,MAAM2V,EAAO3K,EAAGmC,IAAIqG,GACdoC,EAAQlC,EAAoBhH,GAClC,IAAK1B,EAAGkC,IAAIyI,EAAMC,GACd,MAAM,IAAI5V,MAAM,qCACpB,IAAKmC,KAAKyQ,gBACN,MAAM,IAAI5S,MAAM,yCACvB,CACD,QAAA6V,GACI,MAAMrC,EAAEA,GAAMrR,KAAKmR,WACnB,GAAItI,EAAGsE,MACH,OAAQtE,EAAGsE,MAAMkE,GACrB,MAAM,IAAIxT,MAAM,8BACnB,CAID,MAAA8V,CAAOtB,GACHD,EAAeC,GACf,MAAQE,GAAIqB,EAAIpB,GAAIqB,EAAIpB,GAAIqB,GAAO9T,MAC3BuS,GAAIwB,EAAIvB,GAAIwB,EAAIvB,GAAIwB,GAAO5B,EAC7B6B,EAAKrL,EAAGkC,IAAIlC,EAAGsC,IAAIyI,EAAIK,GAAKpL,EAAGsC,IAAI4I,EAAID,IACvCK,EAAKtL,EAAGkC,IAAIlC,EAAGsC,IAAI0I,EAAII,GAAKpL,EAAGsC,IAAI6I,EAAIF,IAC7C,OAAOI,GAAMC,CAChB,CAID,MAAAC,GACI,OAAO,IAAI9B,EAAMtS,KAAKuS,GAAI1J,EAAG+C,IAAI5L,KAAKwS,IAAKxS,KAAKyS,GACnD,CAKD,MAAA4B,GACI,MAAMxV,EAAEA,EAACd,EAAEA,GAAMsS,EACXiE,EAAKzL,EAAGsC,IAAIpN,EAAG8L,KACb0I,GAAIqB,EAAIpB,GAAIqB,EAAIpB,GAAIqB,GAAO9T,KACnC,IAAIuU,EAAK1L,EAAGiD,KAAM0I,EAAK3L,EAAGiD,KAAM2I,EAAK5L,EAAGiD,KACpC4I,EAAK7L,EAAGsC,IAAIyI,EAAIA,GAChBe,EAAK9L,EAAGsC,IAAI0I,EAAIA,GAChB9H,EAAKlD,EAAGsC,IAAI2I,EAAIA,GAChBc,EAAK/L,EAAGsC,IAAIyI,EAAIC,GA4BpB,OA3BAe,EAAK/L,EAAGyE,IAAIsH,EAAIA,GAChBH,EAAK5L,EAAGsC,IAAIyI,EAAIE,GAChBW,EAAK5L,EAAGyE,IAAImH,EAAIA,GAChBF,EAAK1L,EAAGsC,IAAItM,EAAG4V,GACfD,EAAK3L,EAAGsC,IAAImJ,EAAIvI,GAChByI,EAAK3L,EAAGyE,IAAIiH,EAAIC,GAChBD,EAAK1L,EAAGwC,IAAIsJ,EAAIH,GAChBA,EAAK3L,EAAGyE,IAAIqH,EAAIH,GAChBA,EAAK3L,EAAGsC,IAAIoJ,EAAIC,GAChBD,EAAK1L,EAAGsC,IAAIyJ,EAAIL,GAChBE,EAAK5L,EAAGsC,IAAImJ,EAAIG,GAChB1I,EAAKlD,EAAGsC,IAAItM,EAAGkN,GACf6I,EAAK/L,EAAGwC,IAAIqJ,EAAI3I,GAChB6I,EAAK/L,EAAGsC,IAAItM,EAAG+V,GACfA,EAAK/L,EAAGyE,IAAIsH,EAAIH,GAChBA,EAAK5L,EAAGyE,IAAIoH,EAAIA,GAChBA,EAAK7L,EAAGyE,IAAImH,EAAIC,GAChBA,EAAK7L,EAAGyE,IAAIoH,EAAI3I,GAChB2I,EAAK7L,EAAGsC,IAAIuJ,EAAIE,GAChBJ,EAAK3L,EAAGyE,IAAIkH,EAAIE,GAChB3I,EAAKlD,EAAGsC,IAAI0I,EAAIC,GAChB/H,EAAKlD,EAAGyE,IAAIvB,EAAIA,GAChB2I,EAAK7L,EAAGsC,IAAIY,EAAI6I,GAChBL,EAAK1L,EAAGwC,IAAIkJ,EAAIG,GAChBD,EAAK5L,EAAGsC,IAAIY,EAAI4I,GAChBF,EAAK5L,EAAGyE,IAAImH,EAAIA,GAChBA,EAAK5L,EAAGyE,IAAImH,EAAIA,GACT,IAAInC,EAAMiC,EAAIC,EAAIC,EAC5B,CAKD,GAAAnH,CAAI+E,GACAD,EAAeC,GACf,MAAQE,GAAIqB,EAAIpB,GAAIqB,EAAIpB,GAAIqB,GAAO9T,MAC3BuS,GAAIwB,EAAIvB,GAAIwB,EAAIvB,GAAIwB,GAAO5B,EACnC,IAAIkC,EAAK1L,EAAGiD,KAAM0I,EAAK3L,EAAGiD,KAAM2I,EAAK5L,EAAGiD,KACxC,MAAMjN,EAAIwR,EAAMxR,EACVyV,EAAKzL,EAAGsC,IAAIkF,EAAMtS,EAAG8L,IAC3B,IAAI6K,EAAK7L,EAAGsC,IAAIyI,EAAIG,GAChBY,EAAK9L,EAAGsC,IAAI0I,EAAIG,GAChBjI,EAAKlD,EAAGsC,IAAI2I,EAAIG,GAChBW,EAAK/L,EAAGyE,IAAIsG,EAAIC,GAChBgB,EAAKhM,EAAGyE,IAAIyG,EAAIC,GACpBY,EAAK/L,EAAGsC,IAAIyJ,EAAIC,GAChBA,EAAKhM,EAAGyE,IAAIoH,EAAIC,GAChBC,EAAK/L,EAAGwC,IAAIuJ,EAAIC,GAChBA,EAAKhM,EAAGyE,IAAIsG,EAAIE,GAChB,IAAIgB,EAAKjM,EAAGyE,IAAIyG,EAAIE,GA+BpB,OA9BAY,EAAKhM,EAAGsC,IAAI0J,EAAIC,GAChBA,EAAKjM,EAAGyE,IAAIoH,EAAI3I,GAChB8I,EAAKhM,EAAGwC,IAAIwJ,EAAIC,GAChBA,EAAKjM,EAAGyE,IAAIuG,EAAIC,GAChBS,EAAK1L,EAAGyE,IAAI0G,EAAIC,GAChBa,EAAKjM,EAAGsC,IAAI2J,EAAIP,GAChBA,EAAK1L,EAAGyE,IAAIqH,EAAI5I,GAChB+I,EAAKjM,EAAGwC,IAAIyJ,EAAIP,GAChBE,EAAK5L,EAAGsC,IAAItM,EAAGgW,GACfN,EAAK1L,EAAGsC,IAAImJ,EAAIvI,GAChB0I,EAAK5L,EAAGyE,IAAIiH,EAAIE,GAChBF,EAAK1L,EAAGwC,IAAIsJ,EAAIF,GAChBA,EAAK5L,EAAGyE,IAAIqH,EAAIF,GAChBD,EAAK3L,EAAGsC,IAAIoJ,EAAIE,GAChBE,EAAK9L,EAAGyE,IAAIoH,EAAIA,GAChBC,EAAK9L,EAAGyE,IAAIqH,EAAID,GAChB3I,EAAKlD,EAAGsC,IAAItM,EAAGkN,GACf8I,EAAKhM,EAAGsC,IAAImJ,EAAIO,GAChBF,EAAK9L,EAAGyE,IAAIqH,EAAI5I,GAChBA,EAAKlD,EAAGwC,IAAIqJ,EAAI3I,GAChBA,EAAKlD,EAAGsC,IAAItM,EAAGkN,GACf8I,EAAKhM,EAAGyE,IAAIuH,EAAI9I,GAChB2I,EAAK7L,EAAGsC,IAAIwJ,EAAIE,GAChBL,EAAK3L,EAAGyE,IAAIkH,EAAIE,GAChBA,EAAK7L,EAAGsC,IAAI2J,EAAID,GAChBN,EAAK1L,EAAGsC,IAAIyJ,EAAIL,GAChBA,EAAK1L,EAAGwC,IAAIkJ,EAAIG,GAChBA,EAAK7L,EAAGsC,IAAIyJ,EAAID,GAChBF,EAAK5L,EAAGsC,IAAI2J,EAAIL,GAChBA,EAAK5L,EAAGyE,IAAImH,EAAIC,GACT,IAAIpC,EAAMiC,EAAIC,EAAIC,EAC5B,CACD,QAAAM,CAAS1C,GACL,OAAOrS,KAAKsN,IAAI+E,EAAM+B,SACzB,CACD,GAAAlH,GACI,OAAOlN,KAAK2T,OAAOrB,EAAMxG,KAC5B,CACD,IAAAkJ,CAAKtX,GACD,OAAOuX,EAAKC,WAAWlV,KAAMkS,EAAkBxU,GAAIyX,IAC/C,MAAMtC,EAAQhK,EAAGoF,YAAYkH,EAAKlG,KAAK1B,GAAMA,EAAEkF,MAC/C,OAAO0C,EAAKlG,KAAI,CAAC1B,EAAGpL,IAAMoL,EAAE4D,SAAS0B,EAAM1Q,MAAK8M,IAAIqD,EAAMI,WAAW,GAE5E,CAMD,cAAA0C,CAAe1X,GACX,MAAM2X,EAAI/C,EAAMxG,KAChB,GAAIpO,IAAMoH,GACN,OAAOuQ,EAEX,GADA1D,EAASjU,GACLA,IAAMqH,GACN,OAAO/E,KACX,MAAM4Q,KAAEA,GAASP,EACjB,IAAKO,EACD,OAAOqE,EAAKK,aAAatV,KAAMtC,GAEnC,IAAI6X,MAAEA,EAAKC,GAAEA,EAAEC,MAAEA,EAAKC,GAAEA,GAAO9E,EAAKE,YAAYpT,GAC5CiY,EAAMN,EACNO,EAAMP,EACN7H,EAAIxN,KACR,KAAOwV,EAAK1Q,IAAO4Q,EAAK5Q,IAChB0Q,EAAKzQ,KACL4Q,EAAMA,EAAIrI,IAAIE,IACdkI,EAAK3Q,KACL6Q,EAAMA,EAAItI,IAAIE,IAClBA,EAAIA,EAAE6G,SACNmB,IAAOzQ,GACP2Q,IAAO3Q,GAOX,OALIwQ,IACAI,EAAMA,EAAIvB,UACVqB,IACAG,EAAMA,EAAIxB,UACdwB,EAAM,IAAItD,EAAMzJ,EAAGsC,IAAIyK,EAAIrD,GAAI3B,EAAKC,MAAO+E,EAAIpD,GAAIoD,EAAInD,IAChDkD,EAAIrI,IAAIsI,EAClB,CAUD,QAAAzC,CAAS0C,GACLlE,EAASkE,GACT,IACI5E,EAAO6E,EADPpY,EAAImY,EAER,MAAMjF,KAAEA,GAASP,EACjB,GAAIO,EAAM,CACN,MAAM2E,MAAEA,EAAKC,GAAEA,EAAEC,MAAEA,EAAKC,GAAEA,GAAO9E,EAAKE,YAAYpT,GAClD,IAAM6P,EAAGoI,EAAK5I,EAAGgJ,GAAQ/V,KAAKgV,KAAKQ,IAC7BjI,EAAGqI,EAAK7I,EAAGiJ,GAAQhW,KAAKgV,KAAKU,GACnCC,EAAMV,EAAKgB,gBAAgBV,EAAOI,GAClCC,EAAMX,EAAKgB,gBAAgBR,EAAOG,GAClCA,EAAM,IAAItD,EAAMzJ,EAAGsC,IAAIyK,EAAIrD,GAAI3B,EAAKC,MAAO+E,EAAIpD,GAAIoD,EAAInD,IACvDxB,EAAQ0E,EAAIrI,IAAIsI,GAChBE,EAAOC,EAAIzI,IAAI0I,EAClB,KACI,CACD,MAAMzI,EAAEA,EAACR,EAAEA,GAAM/M,KAAKgV,KAAKtX,GAC3BuT,EAAQ1D,EACRuI,EAAO/I,CACV,CAED,OAAOuF,EAAMK,WAAW,CAAC1B,EAAO6E,IAAO,EAC1C,CAOD,oBAAAI,CAAqB1K,EAAG3M,EAAGd,GACvB,MAAMmG,EAAIoO,EAAMY,KACV/H,EAAM,CAACP,EAAG/L,IACVA,IAAMiG,IAAOjG,IAAMkG,IAAQ6F,EAAE+I,OAAOzP,GAA2B0G,EAAEuI,SAAStU,GAAjC+L,EAAEwK,eAAevW,GAC1DmI,EAAMmE,EAAInL,KAAMnB,GAAGyO,IAAInC,EAAIK,EAAGzN,IACpC,OAAOiJ,EAAIkG,WAAQvO,EAAYqI,CAClC,CAID,QAAAmK,CAASgF,GACL,MAAQ5D,GAAIhI,EAAGiI,GAAInB,EAAGoB,GAAI2D,GAAMpW,KAC1BkN,EAAMlN,KAAKkN,MAGP,MAANiJ,IACAA,EAAKjJ,EAAMrE,EAAGyC,IAAMzC,EAAGkF,IAAIqI,IAC/B,MAAMC,EAAKxN,EAAGsC,IAAIZ,EAAG4L,GACfG,EAAKzN,EAAGsC,IAAIkG,EAAG8E,GACfI,EAAK1N,EAAGsC,IAAIiL,EAAGD,GACrB,GAAIjJ,EACA,MAAO,CAAE3C,EAAG1B,EAAGiD,KAAMuF,EAAGxI,EAAGiD,MAC/B,IAAKjD,EAAGkC,IAAIwL,EAAI1N,EAAGyC,KACf,MAAM,IAAIzN,MAAM,oBACpB,MAAO,CAAE0M,EAAG8L,EAAIhF,EAAGiF,EACtB,CACD,aAAA7F,GACI,MAAQ9N,EAAG6T,EAAQ/F,cAAEA,GAAkBJ,EACvC,GAAImG,IAAazR,GACb,OAAO,EACX,GAAI0L,EACA,OAAOA,EAAc6B,EAAOtS,MAChC,MAAM,IAAInC,MAAM,+DACnB,CACD,aAAA6S,GACI,MAAQ/N,EAAG6T,EAAQ9F,cAAEA,GAAkBL,EACvC,OAAImG,IAAazR,GACN/E,KACP0Q,EACOA,EAAc4B,EAAOtS,MACzBA,KAAKoV,eAAe/E,EAAM1N,EACpC,CACD,UAAA8T,CAAWC,GAAe,GAEtB,OADA1W,KAAK+S,iBACEvT,EAAQ8S,EAAOtS,KAAM0W,EAC/B,CACD,KAAAC,CAAMD,GAAe,GACjB,OAAO5E,EAAc9R,KAAKyW,WAAWC,GACxC,EAELpE,EAAMY,KAAO,IAAIZ,EAAMjC,EAAMnB,GAAImB,EAAMlB,GAAItG,EAAGyC,KAC9CgH,EAAMxG,KAAO,IAAIwG,EAAMzJ,EAAGiD,KAAMjD,EAAGyC,IAAKzC,EAAGiD,MAC3C,MAAM8K,EAAQvG,EAAMjE,WACd6I,EDhfH,SAAc1R,EAAGsT,GACpB,MAAMZ,EAAkB,CAACa,EAAWC,KAChC,MAAMnL,EAAMmL,EAAK3C,SACjB,OAAO0C,EAAYlL,EAAMmL,CAAI,EAE3B3G,EAAQ4G,IAGH,CAAEC,QAFOzV,KAAK+K,KAAKsK,EAAOG,GAAK,EAEpB3D,WADC,IAAM2D,EAAI,KAGjC,MAAO,CACHf,kBAEA,YAAAX,CAAa4B,EAAKxZ,GACd,IAAI6P,EAAIhK,EAAEuI,KACN0B,EAAI0J,EACR,KAAOxZ,EAAIoH,IACHpH,EAAIqH,KACJwI,EAAIA,EAAED,IAAIE,IACdA,EAAIA,EAAE6G,SACN3W,IAAMqH,GAEV,OAAOwI,CACV,EAWD,gBAAA4J,CAAiBD,EAAKF,GAClB,MAAMC,QAAEA,EAAO5D,WAAEA,GAAejD,EAAK4G,GAC/BpE,EAAS,GACf,IAAIrF,EAAI2J,EACJE,EAAO7J,EACX,IAAK,IAAI8J,EAAS,EAAGA,EAASJ,EAASI,IAAU,CAC7CD,EAAO7J,EACPqF,EAAO3K,KAAKmP,GAEZ,IAAK,IAAIjV,EAAI,EAAGA,EAAIkR,EAAYlR,IAC5BiV,EAAOA,EAAK9J,IAAIC,GAChBqF,EAAO3K,KAAKmP,GAEhB7J,EAAI6J,EAAK/C,QACZ,CACD,OAAOzB,CACV,EAQD,IAAAoC,CAAKgC,EAAGM,EAAa5Z,GAGjB,MAAMuZ,QAAEA,EAAO5D,WAAEA,GAAejD,EAAK4G,GACrC,IAAIzJ,EAAIhK,EAAEuI,KACNiB,EAAIxJ,EAAE2P,KACV,MAAMqE,EAAOhV,OAAO,GAAKyU,EAAI,GACvBQ,EAAY,GAAKR,EACjBS,EAAUlV,OAAOyU,GACvB,IAAK,IAAIK,EAAS,EAAGA,EAASJ,EAASI,IAAU,CAC7C,MAAMjT,EAASiT,EAAShE,EAExB,IAAIqE,EAAQ/Z,OAAOD,EAAI6Z,GAEvB7Z,IAAM+Z,EAGFC,EAAQrE,IACRqE,GAASF,EACT9Z,GAAKqH,IAST,MAAM4S,EAAUvT,EACVwT,EAAUxT,EAAS5C,KAAKqW,IAAIH,GAAS,EACrCI,EAAQT,EAAS,GAAM,EACvBU,EAAQL,EAAQ,EACR,IAAVA,EAEA3K,EAAIA,EAAEO,IAAI2I,EAAgB6B,EAAOR,EAAYK,KAG7CpK,EAAIA,EAAED,IAAI2I,EAAgB8B,EAAOT,EAAYM,IAEpD,CAMD,MAAO,CAAErK,IAAGR,IACf,EACD,UAAAmI,CAAWtK,EAAGoN,EAAgBta,EAAGua,GAE7B,MAAMjB,EAAIpM,EAAE0I,cAAgB,EAE5B,IAAI6B,EAAO6C,EAAe/U,IAAI2H,GAO9B,OANKuK,IACDA,EAAOnV,KAAKmX,iBAAiBvM,EAAGoM,GACtB,IAANA,GACAgB,EAAetW,IAAIkJ,EAAGqN,EAAU9C,KAGjCnV,KAAKgV,KAAKgC,EAAG7B,EAAMzX,EAC7B,EAET,CCyXiBsX,CAAK1C,EAAOjC,EAAMO,KAAOpP,KAAK+K,KAAKqK,EAAQ,GAAKA,GAE7D,MAAO,CACHvG,QACA6H,gBAAiB5F,EACjBV,yBACAL,sBACAG,qBAER,CAcO,SAASyG,GAAYC,GACxB,MAAM/H,EAdV,SAAsBrB,GAClB,MAAMoB,EAAOrB,GAAcC,GAU3B,OATAsB,EAAkBF,EAAM,CACpBrH,KAAM,OACNsP,KAAM,WACNzX,YAAa,YACd,CACC0X,SAAU,WACVC,cAAe,WACfC,KAAM,YAEH/O,OAAOuD,OAAO,CAAEwL,MAAM,KAASpI,GAC1C,CAEkBqI,CAAaL,IACrBvP,GAAEA,EAAInL,EAAGgb,GAAgBrI,EACzBsI,EAAgB9P,EAAGgE,MAAQ,EAC3B+L,EAAkB,EAAI/P,EAAGgE,MAAQ,EAIvC,SAASgM,EAAKha,GACV,OAAOoT,EAAQpT,EAAG6Z,EACrB,CACD,SAASI,EAAKja,GACV,OAAOka,EAAWla,EAAG6Z,EACxB,CACD,MAAQR,gBAAiB5F,EAAKV,uBAAEA,EAAsBL,oBAAEA,EAAmBG,mBAAEA,GAAwBvB,GAAkB,IAChHE,EACH,OAAA7Q,CAAQwR,EAAIC,EAAOyF,GACf,MAAM7X,EAAIoS,EAAME,WACV5G,EAAI1B,EAAGrJ,QAAQX,EAAE0L,GACjByO,EAAM5H,EACZ,OAAIsF,EACOsC,EAAI/a,WAAWkH,KAAK,CAAC8L,EAAMyC,WAAa,EAAO,IAAQnJ,GAGvDyO,EAAI/a,WAAWkH,KAAK,CAAC,IAAQoF,EAAG1B,EAAGrJ,QAAQX,EAAEwS,GAE3D,EACD,SAAA3C,CAAU5Q,GACN,MAAMwD,EAAMxD,EAAMI,OACZ+a,EAAOnb,EAAM,GACbwT,EAAOxT,EAAM6D,SAAS,GAE5B,GAAIL,IAAQqX,GAA2B,IAATM,GAA0B,IAATA,EAa1C,IAAI3X,IAAQsX,GAA4B,IAATK,EAAe,CAG/C,MAAO,CAAE1O,EAFC1B,EAAG6F,UAAU4C,EAAK3P,SAAS,EAAGkH,EAAGgE,QAE/BwE,EADFxI,EAAG6F,UAAU4C,EAAK3P,SAASkH,EAAGgE,MAAO,EAAIhE,EAAGgE,QAEzD,CAEG,MAAM,IAAIhP,MAAM,mBAAmByD,2BAA6BqX,yBAAqCC,uBACxG,CApB8D,CAC3D,MAAMrO,EAAIwH,EAAmBT,GAC7B,KA5BDxM,IADkBY,EA6BQ6E,IA5Bb7E,EAAMmD,EAAG4D,OA6BjB,MAAM,IAAI5O,MAAM,yBACpB,MAAMqb,EAAK3H,EAAoBhH,GAC/B,IAAI8G,EAAIxI,EAAGmF,KAAKkL,GAMhB,OAHiC,IAAP,EAAPD,OAFH5H,EAAItM,MAASA,MAIzBsM,EAAIxI,EAAG+C,IAAIyF,IACR,CAAE9G,IAAG8G,IACf,CAvCT,IAA6B3L,CAgDxB,IAECyT,EAAiBzT,GAAQoM,EAAcsH,EAAmB1T,EAAK2K,EAAM/D,cAC3E,SAAS+M,EAAsB5b,GAE3B,OAAOA,EADMib,GAAe3T,EAE/B,CAKD,MAAMuU,EAAS,CAACvb,EAAGoH,EAAM9B,IAAO0O,EAAmBhU,EAAEoF,MAAMgC,EAAM9B,IAIjE,MAAMkW,EACF,WAAAvY,CAAY8F,EAAG8I,EAAG4J,GACdxZ,KAAK8G,EAAIA,EACT9G,KAAK4P,EAAIA,EACT5P,KAAKwZ,SAAWA,EAChBxZ,KAAK+S,gBACR,CAED,kBAAO0G,CAAYjU,GACf,MAAM5C,EAAIyN,EAAM/D,YAEhB,OADA9G,EAAMgB,EAAY,mBAAoBhB,EAAS,EAAJ5C,GACpC,IAAI2W,EAAUD,EAAO9T,EAAK,EAAG5C,GAAI0W,EAAO9T,EAAK5C,EAAG,EAAIA,GAC9D,CAGD,cAAO8W,CAAQlU,GACX,MAAMsB,EAAEA,EAAC8I,EAAEA,GAAML,GAAIG,MAAMlJ,EAAY,MAAOhB,IAC9C,OAAO,IAAI+T,EAAUzS,EAAG8I,EAC3B,CACD,cAAAmD,GAEI,IAAKrB,EAAmB1R,KAAK8G,GACzB,MAAM,IAAIjJ,MAAM,6BACpB,IAAK6T,EAAmB1R,KAAK4P,GACzB,MAAM,IAAI/R,MAAM,4BACvB,CACD,cAAA8b,CAAeH,GACX,OAAO,IAAID,EAAUvZ,KAAK8G,EAAG9G,KAAK4P,EAAG4J,EACxC,CACD,gBAAAI,CAAiBC,GACb,MAAM/S,EAAEA,EAAC8I,EAAEA,EAAG4J,SAAUM,GAAQ9Z,KAC1B2C,EAAI4V,EAAc/R,EAAY,UAAWqT,IAC/C,GAAW,MAAPC,IAAgB,CAAC,EAAG,EAAG,EAAG,GAAG3b,SAAS2b,GACtC,MAAM,IAAIjc,MAAM,uBACpB,MAAMkc,EAAe,IAARD,GAAqB,IAARA,EAAYhT,EAAIuJ,EAAM3S,EAAIoJ,EACpD,GAAIiT,GAAQlR,EAAG4D,MACX,MAAM,IAAI5O,MAAM,8BACpB,MAAMmc,EAAuB,IAAP,EAANF,GAAiB,KAAO,KAClCG,EAAI3H,EAAMQ,QAAQkH,EAASb,EAAcY,IACzCG,EAAKpB,EAAKiB,GACVI,EAAKtB,GAAMlW,EAAIuX,GACfE,EAAKvB,EAAKjJ,EAAIsK,GACd1O,EAAI8G,EAAMY,KAAKgD,qBAAqB+D,EAAGE,EAAIC,GACjD,IAAK5O,EACD,MAAM,IAAI3N,MAAM,qBAEpB,OADA2N,EAAEuH,iBACKvH,CACV,CAED,QAAA6O,GACI,OAAOhB,EAAsBrZ,KAAK4P,EACrC,CACD,UAAA0K,GACI,OAAOta,KAAKqa,WAAa,IAAId,EAAUvZ,KAAK8G,EAAG+R,GAAM7Y,KAAK4P,GAAI5P,KAAKwZ,UAAYxZ,IAClF,CAED,aAAAua,GACI,OAAOC,EAAcxa,KAAKya,WAC7B,CACD,QAAAA,GACI,OAAOlL,GAAIO,WAAW,CAAEhJ,EAAG9G,KAAK8G,EAAG8I,EAAG5P,KAAK4P,GAC9C,CAED,iBAAA8K,GACI,OAAOF,EAAcxa,KAAK2a,eAC7B,CACD,YAAAA,GACI,OAAOxB,EAAcnZ,KAAK8G,GAAKqS,EAAcnZ,KAAK4P,EACrD,EAEL,MAAMgL,EAAQ,CACV,iBAAAC,CAAkB5H,GACd,IAEI,OADArB,EAAuBqB,IAChB,CACV,CACD,MAAOjB,GACH,OAAO,CACV,CACJ,EACDJ,uBAAwBA,EAKxBkJ,iBAAkB,KACd,MAAM5c,EAAS6c,GAAqB1K,EAAM3S,GAC1C,OFhSL,SAAwBmU,EAAKjD,EAAY1N,GAAO,GACnD,MAAMI,EAAMuQ,EAAI3T,OACV8c,EAAWrM,GAAoBC,GAC/BqM,EAASnM,GAAiBF,GAEhC,GAAItN,EAAM,IAAMA,EAAM2Z,GAAU3Z,EAAM,KAClC,MAAM,IAAIzD,MAAM,YAAYod,8BAAmC3Z,KACnE,MAEM4Z,EAAUjR,EAFJ/I,EAAOiF,EAAgB0L,GAAOzL,EAAgByL,GAEjCjD,EAAa7J,GAAOA,EAC7C,OAAO7D,EAAOqF,EAAgB2U,EAASF,GAAY1U,EAAgB4U,EAASF,EAChF,CEqRmBG,CAAmB9K,EAAMzP,YAAY1C,GAASmS,EAAM3S,EAAE,EAUjE0d,WAAU,CAAC/H,EAAa,EAAGpC,EAAQqB,EAAMY,QACrCjC,EAAMmC,eAAeC,GACrBpC,EAAMkC,SAAS5Q,OAAO,IACf0O,IAef,SAASoK,EAAUtE,GACf,MAAMhY,EAAMgY,aAAgB9Y,WACtByB,EAAsB,iBAATqX,EACbzV,GAAOvC,GAAOW,IAAQqX,EAAK7Y,OACjC,OAAIa,EACOuC,IAAQqX,GAAiBrX,IAAQsX,EACxClZ,EACO4B,IAAQ,EAAIqX,GAAiBrX,IAAQ,EAAIsX,EAChD7B,aAAgBzE,CAGvB,CAuBD,MAAMgG,EAAWjI,EAAMiI,UACnB,SAAUxa,GAGN,MAAM4H,EAAMqM,EAAmBjU,GACzBwd,EAAuB,EAAfxd,EAAMI,OAAamS,EAAMjE,WACvC,OAAOkP,EAAQ,EAAI5V,GAAOnD,OAAO+Y,GAAS5V,CACtD,EACU6S,EAAgBlI,EAAMkI,eACxB,SAAUza,GACN,OAAO+a,EAAKP,EAASxa,GACjC,EAEUyd,EAAaC,EAAWnL,EAAMjE,YAIpC,SAASqP,EAAW/V,GAChB,GAAmB,iBAARA,EACP,MAAM,IAAI7H,MAAM,mBACpB,KAAMiH,IAAOY,GAAOA,EAAM6V,GACtB,MAAM,IAAI1d,MAAM,uBAAuBwS,EAAMjE,cAEjD,OAAOgN,EAAmB1T,EAAK2K,EAAM/D,YACxC,CAMD,SAASoP,EAAQ7B,EAAS5G,EAAY7C,EAAOuL,GACzC,GAAI,CAAC,YAAa,aAAaC,MAAMjU,GAAMA,KAAKyI,IAC5C,MAAM,IAAIvS,MAAM,uCACpB,MAAMkL,KAAEA,EAAInI,YAAEA,GAAgByP,EAC9B,IAAImI,KAAEA,EAAIqD,QAAEA,EAASC,aAAcC,GAAQ3L,EAC/B,MAARoI,IACAA,GAAO,GACXqB,EAAUrT,EAAY,UAAWqT,GAC7BgC,IACAhC,EAAUrT,EAAY,oBAAqBuC,EAAK8Q,KAIpD,MAAMmC,EAAQzD,EAAcsB,GACtBrM,EAAIoE,EAAuBqB,GAC3BgJ,EAAW,CAACR,EAAWjO,GAAIiO,EAAWO,IAE5C,GAAW,MAAPD,EAAa,CAEb,MAAMpV,GAAY,IAARoV,EAAenb,EAAYiI,EAAGgE,OAASkP,EACjDE,EAAShU,KAAKzB,EAAY,eAAgBG,GAC7C,CACD,MAAMmB,EAAOsJ,KAAkB6K,GACzBvR,EAAIsR,EA0BV,MAAO,CAAElU,OAAMoU,MAxBf,SAAeC,GAEX,MAAMxU,EAAI2Q,EAAS6D,GACnB,IAAKzK,EAAmB/J,GACpB,OACJ,MAAMyU,EAAKtD,EAAKnR,GACV0U,EAAI/J,EAAMY,KAAKC,SAASxL,GAAGwJ,WAC3BrK,EAAI+R,EAAKwD,EAAE9R,GACjB,GAAIzD,IAAMhC,GACN,OAIJ,MAAM8K,EAAIiJ,EAAKuD,EAAKvD,EAAKnO,EAAI5D,EAAI0G,IACjC,GAAIoC,IAAM9K,GACN,OACJ,IAAI0U,GAAY6C,EAAE9R,IAAMzD,EAAI,EAAI,GAAKnJ,OAAO0e,EAAEhL,EAAItM,IAC9CuX,EAAQ1M,EAKZ,OAJI4I,GAAQa,EAAsBzJ,KAC9B0M,EAvOZ,SAAoB1M,GAChB,OAAOyJ,EAAsBzJ,GAAKiJ,GAAMjJ,GAAKA,CAChD,CAqOmB0K,CAAW1K,GACnB4J,GAAY,GAET,IAAID,EAAUzS,EAAGwV,EAAO9C,EAClC,EAEJ,CACD,MAAMmC,EAAiB,CAAEnD,KAAMnI,EAAMmI,KAAMqD,SAAS,GAC9CU,EAAiB,CAAE/D,KAAMnI,EAAMmI,KAAMqD,SAAS,GAuFpD,OAlEAvJ,EAAMY,KAAKE,eAAe,GAkEnB,CACH/C,QACAmM,aAnNJ,SAAsBvJ,EAAYyD,GAAe,GAC7C,OAAOpE,EAAMU,eAAeC,GAAYwD,WAAWC,EACtD,EAkNG+F,gBAxLJ,SAAyBC,EAAUC,EAASjG,GAAe,GACvD,GAAI2E,EAAUqB,GACV,MAAM,IAAI7e,MAAM,iCACpB,IAAKwd,EAAUsB,GACX,MAAM,IAAI9e,MAAM,iCAEpB,OADUyU,EAAMQ,QAAQ6J,GACfxJ,SAASvB,EAAuB8K,IAAWjG,WAAWC,EAClE,EAkLGkG,KA7EJ,SAAc/C,EAASgD,EAASzM,EAAOuL,GACnC,MAAM7T,KAAEA,EAAIoU,MAAEA,GAAUR,EAAQ7B,EAASgD,EAASzM,GAC5CtM,EAAIuM,EAEV,OADayM,EAAkBhZ,EAAEiF,KAAKtI,UAAWqD,EAAEwI,YAAaxI,EAAEuU,KAC3D0E,CAAKjV,EAAMoU,EACrB,EAyEGc,OAxDJ,SAAgBC,EAAWpD,EAASqD,EAAW9M,EAAOmM,GAClD,MAAMY,EAAKF,EAGX,GAFApD,EAAUrT,EAAY,UAAWqT,GACjCqD,EAAY1W,EAAY,YAAa0W,GACjC,WAAY9M,EACZ,MAAM,IAAIvS,MAAM,sCACpB,MAAM2a,KAAEA,EAAIqD,QAAEA,GAAYzL,EAC1B,IAAIgN,EACAxS,EACJ,IACI,GAAkB,iBAAPuS,GAAmBA,aAAclf,WAGxC,IACImf,EAAO7D,EAAUG,QAAQyD,EAC5B,CACD,MAAOE,GACH,KAAMA,aAAoB9N,GAAIC,KAC1B,MAAM6N,EACVD,EAAO7D,EAAUE,YAAY0D,EAChC,KAEA,IAAkB,iBAAPA,GAAmC,iBAATA,EAAGrW,GAAkC,iBAATqW,EAAGvN,EAKrE,MAAM,IAAI/R,MAAM,SALqE,CACrF,MAAMiJ,EAAEA,EAAC8I,EAAEA,GAAMuN,EACjBC,EAAO,IAAI7D,EAAUzS,EAAG8I,EAC3B,CAGA,CACDhF,EAAI0H,EAAMQ,QAAQoK,EACrB,CACD,MAAOlL,GACH,GAAsB,UAAlBA,EAAMsL,QACN,MAAM,IAAIzf,MAAM,kEACpB,OAAO,CACV,CACD,GAAI2a,GAAQ4E,EAAK/C,WACb,OAAO,EACPwB,IACAhC,EAAUxJ,EAAMtH,KAAK8Q,IACzB,MAAM/S,EAAEA,EAAC8I,EAAEA,GAAMwN,EACXza,EAAI4V,EAAcsB,GAClB0D,EAAKzE,EAAKlJ,GACVuK,EAAKtB,EAAKlW,EAAI4a,GACdnD,EAAKvB,EAAK/R,EAAIyW,GACdtD,EAAI3H,EAAMY,KAAKgD,qBAAqBtL,EAAGuP,EAAIC,IAAKjJ,WACtD,QAAK8I,GAEKpB,EAAKoB,EAAE1P,KACJzD,CAChB,EAOGoR,gBAAiB5F,EACjBiH,YACAqB,QAER,CAr0BgFrY,OAAO,GC5FhF,MAAMib,WAAazc,EACtB,WAAAC,CAAY+H,EAAM0U,GACdtc,QACAnB,KAAKxB,UAAW,EAChBwB,KAAKzB,WAAY,ETOzB,SAAcwK,GACV,GAAoB,mBAATA,GAA8C,mBAAhBA,EAAKpI,OAC1C,MAAM,IAAI9C,MAAM,mDACpBJ,EAAOsL,EAAKtI,WACZhD,EAAOsL,EAAKrI,SAChB,CSXQgd,CAAW3U,GACX,MAAM8I,EAAMrS,EAAQie,GAEpB,GADAzd,KAAK2d,MAAQ5U,EAAKpI,SACe,mBAAtBX,KAAK2d,MAAMrd,OAClB,MAAM,IAAIzC,MAAM,uDACpBmC,KAAKU,SAAWV,KAAK2d,MAAMjd,SAC3BV,KAAKS,UAAYT,KAAK2d,MAAMld,UAC5B,MAAMC,EAAWV,KAAKU,SAChBuG,EAAM,IAAIhJ,WAAWyC,GAE3BuG,EAAIvF,IAAImQ,EAAI3T,OAASwC,EAAWqI,EAAKpI,SAASL,OAAOuR,GAAKtR,SAAWsR,GACrE,IAAK,IAAI1P,EAAI,EAAGA,EAAI8E,EAAI/I,OAAQiE,IAC5B8E,EAAI9E,IAAM,GACdnC,KAAK2d,MAAMrd,OAAO2G,GAElBjH,KAAK4d,MAAQ7U,EAAKpI,SAElB,IAAK,IAAIwB,EAAI,EAAGA,EAAI8E,EAAI/I,OAAQiE,IAC5B8E,EAAI9E,IAAM,IACdnC,KAAK4d,MAAMtd,OAAO2G,GAClBA,EAAI/E,KAAK,EACZ,CACD,MAAA5B,CAAOud,GAGH,OAFAC,EAAa9d,MACbA,KAAK2d,MAAMrd,OAAOud,GACX7d,IACV,CACD,UAAA+B,CAAWC,GACP8b,EAAa9d,MACb+d,EAAY/b,EAAKhC,KAAKS,WACtBT,KAAKxB,UAAW,EAChBwB,KAAK2d,MAAM5b,WAAWC,GACtBhC,KAAK4d,MAAMtd,OAAO0B,GAClBhC,KAAK4d,MAAM7b,WAAWC,GACtBhC,KAAKoD,SACR,CACD,MAAA7C,GACI,MAAMyB,EAAM,IAAI/D,WAAW+B,KAAK4d,MAAMnd,WAEtC,OADAT,KAAK+B,WAAWC,GACTA,CACV,CACD,UAAA/B,CAAWoD,GAEPA,IAAOA,EAAKoG,OAAO9I,OAAO8I,OAAOuU,eAAehe,MAAO,CAAE,IACzD,MAAM4d,MAAEA,EAAKD,MAAEA,EAAKnf,SAAEA,EAAQD,UAAEA,EAASmC,SAAEA,EAAQD,UAAEA,GAAcT,KAQnE,OANAqD,EAAG7E,SAAWA,EACd6E,EAAG9E,UAAYA,EACf8E,EAAG3C,SAAWA,EACd2C,EAAG5C,UAAYA,EACf4C,EAAGua,MAAQA,EAAM3d,WAAWoD,EAAGua,OAC/Bva,EAAGsa,MAAQA,EAAM1d,WAAWoD,EAAGsa,OACxBta,CACV,CACD,OAAAD,GACIpD,KAAKzB,WAAY,EACjByB,KAAK4d,MAAMxa,UACXpD,KAAK2d,MAAMva,SACd,EAQE,MAAMiV,GAAO,CAACtP,EAAM8I,EAAKyL,IAAY,IAAIE,GAAKzU,EAAM8I,GAAKvR,OAAOgd,GAAS/c;;ACrEzE,SAAS0d,GAAQlV,GACpB,MAAO,CACHA,OACAsP,KAAM,CAACxG,KAAQqM,IAAS7F,GAAKtP,EAAM8I,ERuFpC,YAAwBhL,GAC3B,MAAMC,EAAI,IAAI7I,WAAW4I,EAAOE,QAAO,CAACC,EAAKnI,IAAMmI,EAAMnI,EAAEX,QAAQ,IACnE,IAAI+I,EAAM,EAOV,OANAJ,EAAOK,SAASrI,IACZ,IAAKD,EAAIC,GACL,MAAM,IAAIhB,MAAM,uBACpBiJ,EAAEpF,IAAI7C,EAAGoI,GACTA,GAAOpI,EAAEX,MAAM,IAEZ4I,CACX,CQjGgDF,IAAesX,IACvDtd,cAER,CDgEAyX,GAAK1X,OAAS,CAACoI,EAAM8I,IAAQ,IAAI2L,GAAKzU,EAAM8I;;AEnE5C,MAAMsM,GAAa5b,OAAO,sEACpB6b,GAAa7b,OAAO,sEACpBwC,GAAMxC,OAAO,GACbyC,GAAMzC,OAAO,GACb8b,GAAa,CAACxf,EAAGd,KAAOc,EAAId,EAAIiH,IAAOjH,EAK7C,SAASugB,GAAQjN,GACb,MAAMzG,EAAIuT,GAEJtU,EAAMtH,OAAO,GAAIgc,EAAMhc,OAAO,GAAIic,EAAOjc,OAAO,IAAKkc,EAAOlc,OAAO,IAEnEmc,EAAOnc,OAAO,IAAKoc,EAAOpc,OAAO,IAAKqc,EAAOrc,OAAO,IACpDqH,EAAMyH,EAAIA,EAAIA,EAAKzG,EACnB0J,EAAM1K,EAAKA,EAAKyH,EAAKzG,EACrBiU,EAAMvU,EAAKgK,EAAIzK,EAAKe,GAAK0J,EAAM1J,EAC/BkU,EAAMxU,EAAKuU,EAAIhV,EAAKe,GAAK0J,EAAM1J,EAC/BmU,EAAOzU,EAAKwU,EAAI9Z,GAAK4F,GAAKhB,EAAMgB,EAChCoU,EAAO1U,EAAKyU,EAAKP,EAAM5T,GAAKmU,EAAOnU,EACnCqU,EAAO3U,EAAK0U,EAAKP,EAAM7T,GAAKoU,EAAOpU,EACnCsU,EAAO5U,EAAK2U,EAAKN,EAAM/T,GAAKqU,EAAOrU,EACnCuU,EAAQ7U,EAAK4U,EAAKN,EAAMhU,GAAKsU,EAAOtU,EACpCwU,EAAQ9U,EAAK6U,EAAMR,EAAM/T,GAAKqU,EAAOrU,EACrCyU,EAAQ/U,EAAK8U,EAAMvV,EAAKe,GAAK0J,EAAM1J,EACnC+J,EAAMrK,EAAK+U,EAAMX,EAAM9T,GAAKoU,EAAOpU,EACnCmB,EAAMzB,EAAKqK,EAAI4J,EAAK3T,GAAKhB,EAAMgB,EAC/BE,EAAOR,EAAKyB,EAAI/G,GAAK4F,GAC3B,IAAK/B,GAAGkC,IAAIlC,GAAGmC,IAAIF,GAAOuG,GACtB,MAAM,IAAIxT,MAAM,2BACpB,OAAOiN,CACX,CACA,MAAMjC,GAAK2D,GAAM2R,QAAYxf,OAAWA,EAAW,CAAEqP,KAAMsQ,KAC9CgB,GD9BN,SAAqBlH,EAAUmH,GAClC,MAAM5e,EAAUoI,GAASoP,GAAY,IAAKC,KAAa6F,GAAQlV,KAC/D,OAAOU,OAAOuD,OAAO,IAAKrM,EAAO4e,GAAU5e,UAC/C,CC2ByB6e,CAAY,CACjC3gB,EAAG0D,OAAO,GACVxE,EAAGwE,OAAO,GACVsG,MACAnL,EAAG0gB,GAEHlP,GAAI3M,OAAO,iFACX4M,GAAI5M,OAAO,iFACXI,EAAGJ,OAAO,GACViW,MAAM,EAON5H,KAAM,CACFC,KAAMtO,OAAO,sEACbuO,YAAcnJ,IACV,MAAMjK,EAAI0gB,GACJqB,EAAKld,OAAO,sCACZoH,GAAM5E,GAAMxC,OAAO,sCACnBmd,EAAKnd,OAAO,uCACZqH,EAAK6V,EACLE,EAAYpd,OAAO,uCACnB0I,EAAKoT,GAAWzU,EAAKjC,EAAGjK,GACxBkiB,EAAKvB,IAAY1U,EAAKhC,EAAGjK,GAC/B,IAAI8X,EAAKvL,EAAItC,EAAIsD,EAAKwU,EAAKG,EAAKF,EAAIhiB,GAChCgY,EAAKzL,GAAKgB,EAAKtB,EAAKiW,EAAKhW,EAAIlM,GACjC,MAAM6X,EAAQC,EAAKmK,EACblK,EAAQC,EAAKiK,EAKnB,GAJIpK,IACAC,EAAK9X,EAAI8X,GACTC,IACAC,EAAKhY,EAAIgY,GACTF,EAAKmK,GAAajK,EAAKiK,EACvB,MAAM,IAAI9hB,MAAM,uCAAyC8J,GAE7D,MAAO,CAAE4N,QAAOC,KAAIC,QAAOC,KAAI,IAGxC9Q,GAGGE,GAAMvC,OAAO,GACbsd,GAAMtV,GAAmB,iBAANA,GAAkBzF,GAAMyF,GAAKA,EAAI4T,GACpDnS,GAAMzB,GAAmB,iBAANA,GAAkBzF,GAAMyF,GAAKA,EAAI6T,GAEpD0B,GAAuB,CAAA,EAC7B,SAASC,GAAWC,KAAQC,GACxB,IAAIC,EAAOJ,GAAqBE,GAChC,QAAarhB,IAATuhB,EAAoB,CACpB,MAAMC,EAAOvb,EAAO3G,WAAWkH,KAAK6a,GAAMzc,GAAMA,EAAE6c,WAAW,MAC7DF,EAAOtZ,EAAYuZ,EAAMA,GACzBL,GAAqBE,GAAOE,CAC/B,CACD,OAAOtb,EAAOgC,EAAYsZ,KAASD,GACvC,CAEA,MAAMI,GAAgBpP,GAAUA,EAAMwF,YAAW,GAAMtT,MAAM,GACvDmd,GAAY5iB,GAAM4I,EAAgB5I,EAAG,IACrC6iB,GAAQhW,GAAMN,EAAIM,EAAG4T,IACrBtF,GAAQtO,GAAMN,EAAIM,EAAG6T,IACrB9L,GAAQgN,GAAUpH,gBAClBsI,GAAU,CAAChV,EAAG3M,EAAGd,IAAMuU,GAAMY,KAAKgD,qBAAqB1K,EAAG3M,EAAGd,GAEnE,SAAS0iB,GAAoBC,GACzB,IAAIC,EAAKrB,GAAU1E,MAAMhJ,uBAAuB8O,GAC5CnT,EAAI+E,GAAMU,eAAe2N,GAE7B,MAAO,CAAE9K,OADMtI,EAAEmG,WAAaiN,EAAK9H,IAAM8H,GAChB7iB,MAAOuiB,GAAa9S,GACjD,CAKA,SAASqT,GAAOrW,GACZ,IAAKsV,GAAGtV,GACJ,MAAM,IAAI1M,MAAM,yBACpB,MAAMgjB,EAAKN,GAAKhW,EAAIA,GAEpB,IAAI8G,EAAIiN,GADEiC,GAAKM,EAAKtW,EAAIhI,OAAO,KAE3B8O,EAAIrM,KAAQF,KACZuM,EAAIkP,IAAMlP,IACd,MAAM9D,EAAI,IAAI+E,GAAM/H,EAAG8G,EAAGtM,IAE1B,OADAwI,EAAEwF,iBACKxF,CACX,CAIA,SAASuT,MAAaC,GAClB,OAAOlI,GAAK1S,EAAgB4Z,GAAW,uBAAwBgB,IACnE,CAIA,SAASC,GAAoB/N,GACzB,OAAOwN,GAAoBxN,GAAYnV,KAC3C,CAKA,SAASmjB,GAAY3D,EAASrK,EAAYiO,EAAUtgB,EAAY,KAC5D,MAAM8J,EAAIlE,EAAY,UAAW8W,IACzBxf,MAAOyU,EAAIsD,OAAQrI,GAAMiT,GAAoBxN,GAC/CpU,EAAI2H,EAAY,UAAW0a,EAAS,IACpCC,EAAIb,GAAS9S,EAAIrH,EAAgB4Z,GAAW,cAAelhB,KAC3DuiB,EAAOrB,GAAW,gBAAiBoB,EAAG5O,EAAI7H,GAC1C2W,EAAKxI,GAAK1S,EAAgBib,IAChC,GAAIC,IAAOvc,GACP,MAAM,IAAIjH,MAAM,0BACpB,MAAQC,MAAOwjB,EAAIzL,OAAQlO,GAAM8Y,GAAoBY,GAC/C1a,EAAIma,GAAUQ,EAAI/O,EAAI7H,GACtBqF,EAAM,IAAI9R,WAAW,IAI3B,GAHA8R,EAAIrO,IAAI4f,EAAI,GACZvR,EAAIrO,IAAI4e,GAASzH,GAAKlR,EAAIhB,EAAI6G,IAAK,KAE9B+T,GAAcxR,EAAKrF,EAAG6H,GACvB,MAAM,IAAI1U,MAAM,oCACpB,OAAOkS,CACX,CAKA,SAASwR,GAActE,EAAWK,EAASJ,GACvC,MAAMnN,EAAMvJ,EAAY,YAAayW,EAAW,IAC1CvS,EAAIlE,EAAY,UAAW8W,GAC3BkE,EAAMhb,EAAY,YAAa0W,EAAW,IAChD,IACI,MAAMtS,EAAIgW,GAAOza,EAAgBqb,IAC3B1a,EAAIX,EAAgB4J,EAAIpO,SAAS,EAAG,KAC1C,IAAKke,GAAG/Y,GACJ,OAAO,EACX,MAAM8I,EAAIzJ,EAAgB4J,EAAIpO,SAAS,GAAI,KAC3C,IAAKqK,GAAG4D,GACJ,OAAO,EACX,MAAMjJ,EAAIma,GAAUR,GAASxZ,GAAIuZ,GAAazV,GAAIF,GAC5CuP,EAAIuG,GAAQ5V,EAAGgF,EAAGiJ,IAAMlS,IAC9B,SAAKsT,IAAMA,EAAEvG,YAAcuG,EAAE9I,WAAW5G,IAAMzD,EAGjD,CACD,MAAOkL,GACH,OAAO,CACV,CACL,CACO,MAAMyP,GAA0B,MAAQ,CAC3CjF,aAAcwE,GACdpE,KAAMqE,GACNjE,OAAQuE,GACR3G,MAAO,CACHE,iBAAkBwE,GAAU1E,MAAME,iBAC1C8F,OAAQA,GACAP,gBACA/Z,kBACAH,kBACA4Z,cACA9V,SAX+B,GC/LvCyX,GAAsB,CAEtBC,QAAY,EAEZC,UAEe,SAAGC,GAAKC,EAAA,IACrB,MAAgB,IAAAJ,MAAKI,EACrB,CCFF,SAAS1jB,GAAOC,EAAUC,GAAgB,GACtC,GAAID,EAASE,UACT,MAAM,IAAIV,MAAM,oCACpB,GAAIS,GAAiBD,EAASG,SAC1B,MAAM,IAAIX,MAAM,wCACxB,CACA,SAASoE,GAAOD,EAAK3D,IAZrB,SAAeN,KAAMC,GACjB,KAAMD,aAAaE,YACf,MAAM,IAAIJ,MAAM,uBACpB,GAAIG,EAAQE,OAAS,IAAMF,EAAQG,SAASJ,EAAEG,QAC1C,MAAM,IAAIL,MAAM,iCAAiCG,oBAA0BD,EAAEG,SACrF,CAQIJ,CAAMkE,GACN,MAAMP,EAAMpD,EAASoC,UACrB,GAAIuB,EAAI9D,OAASuD,EACb,MAAM,IAAI5D,MAAM,yDAAyD4D,IAEjF;sEASA,MAAM7C,GAAOC,GAAMA,aAAaZ,WAE1Ba,GAAcC,GAAQ,IAAIC,SAASD,EAAIE,OAAQF,EAAIG,WAAYH,EAAII,YAEnEC,GAAO,CAACC,EAAMC,IAAWD,GAAS,GAAKC,EAAWD,IAASC,EAIjE,KADyE,KAA5D,IAAIrB,WAAW,IAAIsB,YAAY,CAAC,YAAaN,QAAQ,IAE9D,MAAM,IAAIpB,MAAM,+CAcpB,SAAS2B,GAAQC,GAGb,GAFoB,iBAATA,IACPA,EAZR,SAAqBC,GACjB,GAAmB,iBAARA,EACP,MAAM,IAAI7B,MAAM,2CAA2C6B,GAC/D,OAAO,IAAIzB,YAAW,IAAI0B,aAAcC,OAAOF,GACnD,CAQeG,CAAYJ,KAClBb,GAAIa,GACL,MAAM,IAAI5B,MAAM,mCAAmC4B,GACvD,OAAOA,CACX,CAEA,MAAMsB,GAEF,KAAAhB,GACI,OAAOC,KAAKC,YACf,EAEL,SAASC,GAAgBC,GACrB,MAAMC,EAASC,GAAQF,IAAWG,OAAOd,GAAQa,IAAME,SACjDC,EAAML,IAIZ,OAHAC,EAAMK,UAAYD,EAAIC,UACtBL,EAAMM,SAAWF,EAAIE,SACrBN,EAAMO,OAAS,IAAMR,IACdC,CACX,CAgBA,MAAMuD,WAAa5C,GACf,WAAAC,CAAYN,EAAUD,EAAWQ,EAAWC,GACxCC,QACAnB,KAAKU,SAAWA,EAChBV,KAAKS,UAAYA,EACjBT,KAAKiB,UAAYA,EACjBjB,KAAKkB,KAAOA,EACZlB,KAAKxB,UAAW,EAChBwB,KAAK9B,OAAS,EACd8B,KAAKoB,IAAM,EACXpB,KAAKzB,WAAY,EACjByB,KAAKf,OAAS,IAAIhB,WAAWyC,GAC7BV,KAAKqB,KAAOvC,GAAWkB,KAAKf,OAC/B,CACD,MAAAqB,CAAOb,GACHrB,GAAO4B,MACP,MAAMqB,KAAEA,EAAIpC,OAAEA,EAAMyB,SAAEA,GAAaV,KAE7BsB,GADN7B,EAAOD,GAAQC,IACEvB,OACjB,IAAK,IAAIkD,EAAM,EAAGA,EAAME,GAAM,CAC1B,MAAMC,EAAOC,KAAKC,IAAIf,EAAWV,KAAKoB,IAAKE,EAAMF,GAEjD,GAAIG,IAASb,EAMbzB,EAAOyC,IAAIjC,EAAKkC,SAASP,EAAKA,EAAMG,GAAOvB,KAAKoB,KAChDpB,KAAKoB,KAAOG,EACZH,GAAOG,EACHvB,KAAKoB,MAAQV,IACbV,KAAK4B,QAAQP,EAAM,GACnBrB,KAAKoB,IAAM,OAXf,CACI,MAAMS,EAAW/C,GAAWW,GAC5B,KAAOiB,GAAYY,EAAMF,EAAKA,GAAOV,EACjCV,KAAK4B,QAAQC,EAAUT,EAE9B,CAQJ,CAGD,OAFApB,KAAK9B,QAAUuB,EAAKvB,OACpB8B,KAAK8B,aACE9B,IACV,CACD,UAAA+B,CAAWC,GACP5D,GAAO4B,MACPiC,GAAOD,EAAKhC,MACZA,KAAKxB,UAAW,EAIhB,MAAMS,OAAEA,EAAMoC,KAAEA,EAAIX,SAAEA,EAAQQ,KAAEA,GAASlB,KACzC,IAAIoB,IAAEA,GAAQpB,KAEdf,EAAOmC,KAAS,IAChBpB,KAAKf,OAAO0C,SAASP,GAAKc,KAAK,GAE3BlC,KAAKiB,UAAYP,EAAWU,IAC5BpB,KAAK4B,QAAQP,EAAM,GACnBD,EAAM,GAGV,IAAK,IAAIe,EAAIf,EAAKe,EAAIzB,EAAUyB,IAC5BlD,EAAOkD,GAAK,GAxExB,SAAsBd,EAAMnC,EAAYkD,EAAOlB,GAC3C,GAAiC,mBAAtBG,EAAKgB,aACZ,OAAOhB,EAAKgB,aAAanD,EAAYkD,EAAOlB,GAChD,MAAMoB,EAAOC,OAAO,IACdC,EAAWD,OAAO,YAClBE,EAAK9E,OAAQyE,GAASE,EAAQE,GAC9BE,EAAK/E,OAAOyE,EAAQI,GACpBG,EAAIzB,EAAO,EAAI,EACf0B,EAAI1B,EAAO,EAAI,EACrBG,EAAKwB,UAAU3D,EAAayD,EAAGF,EAAIvB,GACnCG,EAAKwB,UAAU3D,EAAa0D,EAAGF,EAAIxB,EACvC,CAiEQmB,CAAahB,EAAMX,EAAW,EAAG6B,OAAqB,EAAdvC,KAAK9B,QAAagD,GAC1DlB,KAAK4B,QAAQP,EAAM,GACnB,MAAMyB,EAAQhE,GAAWkD,GACnBV,EAAMtB,KAAKS,UAEjB,GAAIa,EAAM,EACN,MAAM,IAAIzD,MAAM,+CACpB,MAAMkF,EAASzB,EAAM,EACf0B,EAAQhD,KAAKiD,MACnB,GAAIF,EAASC,EAAM9E,OACf,MAAM,IAAIL,MAAM,sCACpB,IAAK,IAAIsE,EAAI,EAAGA,EAAIY,EAAQZ,IACxBW,EAAMD,UAAU,EAAIV,EAAGa,EAAMb,GAAIjB,EACxC,CACD,MAAAX,GACI,MAAMtB,OAAEA,EAAMwB,UAAEA,GAAcT,KAC9BA,KAAK+B,WAAW9C,GAChB,MAAMiE,EAAMjE,EAAOkE,MAAM,EAAG1C,GAE5B,OADAT,KAAKoD,UACEF,CACV,CACD,UAAAjD,CAAWoD,GACPA,IAAOA,EAAK,IAAIrD,KAAKgB,aACrBqC,EAAG3B,OAAO1B,KAAKiD,OACf,MAAMvC,SAAEA,EAAQzB,OAAEA,EAAMf,OAAEA,EAAMM,SAAEA,EAAQD,UAAEA,EAAS6C,IAAEA,GAAQpB,KAO/D,OANAqD,EAAGnF,OAASA,EACZmF,EAAGjC,IAAMA,EACTiC,EAAG7E,SAAWA,EACd6E,EAAG9E,UAAYA,EACXL,EAASwC,GACT2C,EAAGpE,OAAOyC,IAAIzC,GACXoE,CACV,EAML,MAEMC,GAAM,CAACzE,EAAGd,EAAGwF,IAAO1E,EAAId,EAAMc,EAAI0E,EAAMxF,EAAIwF,EAI5CC,GAA2B,IAAIjE,YAAY,CAC7C,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WACpF,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UACpF,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UACpF,UAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WACpF,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,aAIlFkE,GAAqB,IAAIlE,YAAY,CACvC,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,aAIlFmE,GAA2B,IAAInE,YAAY,IACjD,MAAMsF,WAAelB,GACjB,WAAA3C,GACIG,MAAM,GAAI,GAAI,GAAG,GAGjBnB,KAAK4D,EAAY,EAARH,GAAG,GACZzD,KAAK6D,EAAY,EAARJ,GAAG,GACZzD,KAAK8D,EAAY,EAARL,GAAG,GACZzD,KAAK+D,EAAY,EAARN,GAAG,GACZzD,KAAKgE,EAAY,EAARP,GAAG,GACZzD,KAAKiE,EAAY,EAARR,GAAG,GACZzD,KAAKkE,EAAY,EAART,GAAG,GACZzD,KAAKmE,EAAY,EAARV,GAAG,EACf,CACD,GAAAR,GACI,MAAMW,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,GAAMnE,KACnC,MAAO,CAAC4D,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAChC,CAED,GAAAzC,CAAIkC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACrBnE,KAAK4D,EAAQ,EAAJA,EACT5D,KAAK6D,EAAQ,EAAJA,EACT7D,KAAK8D,EAAQ,EAAJA,EACT9D,KAAK+D,EAAQ,EAAJA,EACT/D,KAAKgE,EAAQ,EAAJA,EACThE,KAAKiE,EAAQ,EAAJA,EACTjE,KAAKkE,EAAQ,EAAJA,EACTlE,KAAKmE,EAAQ,EAAJA,CACZ,CACD,OAAAvC,CAAQP,EAAM+C,GAEV,IAAK,IAAIjC,EAAI,EAAGA,EAAI,GAAIA,IAAKiC,GAAU,EACnCV,GAASvB,GAAKd,EAAKgD,UAAUD,GAAQ,GACzC,IAAK,IAAIjC,EAAI,GAAIA,EAAI,GAAIA,IAAK,CAC1B,MAAMmC,EAAMZ,GAASvB,EAAI,IACnBoC,EAAKb,GAASvB,EAAI,GAClBqC,EAAKpF,GAAKkF,EAAK,GAAKlF,GAAKkF,EAAK,IAAOA,IAAQ,EAC7CG,EAAKrF,GAAKmF,EAAI,IAAMnF,GAAKmF,EAAI,IAAOA,IAAO,GACjDb,GAASvB,GAAMsC,EAAKf,GAASvB,EAAI,GAAKqC,EAAKd,GAASvB,EAAI,IAAO,CAClE,CAED,IAAIyB,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,EAACC,EAAEA,GAAMnE,KACjC,IAAK,IAAImC,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MACMuC,EAAMP,GADG/E,GAAK4E,EAAG,GAAK5E,GAAK4E,EAAG,IAAM5E,GAAK4E,EAAG,OAnEjDnF,EAoE4BmF,GAAGC,GApEPpF,EAoEUqF,GAAKV,GAASrB,GAAKuB,GAASvB,GAAM,EAE/DwC,GADSvF,GAAKwE,EAAG,GAAKxE,GAAKwE,EAAG,IAAMxE,GAAKwE,EAAG,KAC7BN,GAAIM,EAAGC,EAAGC,GAAM,EACrCK,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKD,EAAIW,EAAM,EACfX,EAAID,EACJA,EAAID,EACJA,EAAID,EACJA,EAAKc,EAAKC,EAAM,CACnB,CA/EG,IAAC9F,EAiFL+E,EAAKA,EAAI5D,KAAK4D,EAAK,EACnBC,EAAKA,EAAI7D,KAAK6D,EAAK,EACnBC,EAAKA,EAAI9D,KAAK8D,EAAK,EACnBC,EAAKA,EAAI/D,KAAK+D,EAAK,EACnBC,EAAKA,EAAIhE,KAAKgE,EAAK,EACnBC,EAAKA,EAAIjE,KAAKiE,EAAK,EACnBC,EAAKA,EAAIlE,KAAKkE,EAAK,EACnBC,EAAKA,EAAInE,KAAKmE,EAAK,EACnBnE,KAAK0B,IAAIkC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EACjC,CACD,UAAArC,GACI4B,GAASxB,KAAK,EACjB,CACD,OAAAkB,GACIpD,KAAK0B,IAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC9B1B,KAAKf,OAAOiD,KAAK,EACpB,EAML,MAAM0C,GAAyB1E,IAAgB,IAAM,IAAI2E;oEAQzD,SAASkd,MAAShB,GAEd,MAAMiB,EAAO,CAACnjB,EAAGd,IAAOwF,GAAM1E,EAAEd,EAAEwF,IAOlC,MAAO,CAAE3D,OALMsF,MAAMC,KAAK4b,GACrB1a,UACAU,QAAO,CAACsH,EAAKlM,IAAOkM,EAAM2T,EAAK3T,EAAKlM,EAAEvC,QAAUuC,EAAEvC,aAASjB,GAG/CsjB,OADFlB,EAAKha,QAAO,CAACsH,EAAKlM,IAAOkM,EAAM2T,EAAK3T,EAAKlM,EAAE8f,QAAU9f,EAAE8f,aAAStjB,GAEnF,CAKA,SAASujB,GAASA,GACd,MAAO,CACHtiB,OAASuiB,IACL,IAAKjd,MAAMwD,QAAQyZ,IAAYA,EAAOjkB,QAA+B,iBAAdikB,EAAO,GAC1D,MAAM,IAAItkB,MAAM,uDACpB,OAAOskB,EAAOlT,KAAK9M,IACf,GAAIA,EAAI,GAAKA,GAAK+f,EAAShkB,OACvB,MAAM,IAAIL,MAAM,iCAAiCsE,gBAAgB+f,EAAShkB,WAC9E,OAAOgkB,EAAS/f,EAAE,GACpB,EAEN8f,OAASG,IACL,IAAKld,MAAMwD,QAAQ0Z,IAAWA,EAAMlkB,QAA8B,iBAAbkkB,EAAM,GACvD,MAAM,IAAIvkB,MAAM,oDACpB,OAAOukB,EAAMnT,KAAKoT,IACd,GAAsB,iBAAXA,EACP,MAAM,IAAIxkB,MAAM,uCAAuCwkB,KAC3D,MAAMC,EAAQJ,EAASK,QAAQF,GAC/B,IAAe,IAAXC,EACA,MAAM,IAAIzkB,MAAM,oBAAoBwkB,gBAAqBH,KAC7D,OAAOI,CAAK,GACd,EAGd,CAIA,SAASE,GAAKC,EAAY,IACtB,GAAyB,iBAAdA,EACP,MAAM,IAAI5kB,MAAM,mCACpB,MAAO,CACH+B,OAASuF,IACL,IAAKD,MAAMwD,QAAQvD,IAAUA,EAAKjH,QAA6B,iBAAZiH,EAAK,GACpD,MAAM,IAAItH,MAAM,gDACpB,IAAK,IAAIsE,KAAKgD,EACV,GAAiB,iBAANhD,EACP,MAAM,IAAItE,MAAM,iCAAiCsE,KACzD,OAAOgD,EAAKqd,KAAKC,EAAU,EAE/BR,OAAS5e,IACL,GAAkB,iBAAPA,EACP,MAAM,IAAIxF,MAAM,sCACpB,OAAOwF,EAAGqf,MAAMD,EAAU,EAGtC,CAKA,SAASE,GAAQ9L,EAAM+L,EAAM,KACzB,GAAmB,iBAARA,EACP,MAAM,IAAI/kB,MAAM,gCACpB,MAAO,CACH,MAAA+B,CAAOH,GACH,IAAKyF,MAAMwD,QAAQjJ,IAAUA,EAAKvB,QAA6B,iBAAZuB,EAAK,GACpD,MAAM,IAAI5B,MAAM,mDACpB,IAAK,IAAIsE,KAAK1C,EACV,GAAiB,iBAAN0C,EACP,MAAM,IAAItE,MAAM,oCAAoCsE,KAC5D,KAAQ1C,EAAKvB,OAAS2Y,EAAQ,GAC1BpX,EAAKwI,KAAK2a,GACd,OAAOnjB,CACV,EACD,MAAAwiB,CAAOG,GACH,IAAKld,MAAMwD,QAAQ0Z,IAAWA,EAAMlkB,QAA8B,iBAAbkkB,EAAM,GACvD,MAAM,IAAIvkB,MAAM,mDACpB,IAAK,IAAIsE,KAAKigB,EACV,GAAiB,iBAANjgB,EACP,MAAM,IAAItE,MAAM,oCAAoCsE,KAC5D,IAAI0gB,EAAMT,EAAMlkB,OAChB,GAAK2kB,EAAMhM,EAAQ,EACf,MAAM,IAAIhZ,MAAM,6DACpB,KAAOglB,EAAM,GAAKT,EAAMS,EAAM,KAAOD,EAAKC,IACtC,MAAQA,EAAM,GAAKhM,EAAQ,GACvB,MAAM,IAAIhZ,MAAM,gDAExB,OAAOukB,EAAMjf,MAAM,EAAG0f,EACzB,EAET,CAKA,SAASC,GAAarjB,EAAM0F,EAAM9B,GAE9B,GAAI8B,EAAO,EACP,MAAM,IAAItH,MAAM,4BAA4BsH,iCAChD,GAAI9B,EAAK,EACL,MAAM,IAAIxF,MAAM,0BAA0BwF,iCAC9C,IAAK6B,MAAMwD,QAAQjJ,GACf,MAAM,IAAI5B,MAAM,sCACpB,IAAK4B,EAAKvB,OACN,MAAO,GACX,IAAIkD,EAAM,EACV,MAAM8B,EAAM,GACNif,EAASjd,MAAMC,KAAK1F,GAK1B,IAJA0iB,EAAOjb,SAASsG,IACZ,GAAIA,EAAI,GAAKA,GAAKrI,EACd,MAAM,IAAItH,MAAM,kBAAkB2P,IAAI,MAEjC,CACT,IAAIuV,EAAQ,EACRC,GAAO,EACX,IAAK,IAAI7gB,EAAIf,EAAKe,EAAIggB,EAAOjkB,OAAQiE,IAAK,CACtC,MAAM8gB,EAAQd,EAAOhgB,GACf+gB,EAAY/d,EAAO4d,EAAQE,EACjC,IAAKtlB,OAAOC,cAAcslB,IACrB/d,EAAO4d,EAAS5d,IAAS4d,GAC1BG,EAAYD,GAAU9d,EAAO4d,EAC7B,MAAM,IAAIllB,MAAM,gCAEpBklB,EAAQG,EAAY7f,EACpB,MAAM8f,EAAU3hB,KAAK4hB,MAAMF,EAAY7f,GAEvC,GADA8e,EAAOhgB,GAAKghB,GACPxlB,OAAOC,cAAculB,IAAYA,EAAU9f,EAAK0f,IAAUG,EAC3D,MAAM,IAAIrlB,MAAM,gCACfmlB,IAEKG,EAGNH,GAAO,EAFP5hB,EAAMe,EAGb,CAED,GADAe,EAAI+E,KAAK8a,GACLC,EACA,KACP,CACD,IAAK,IAAI7gB,EAAI,EAAGA,EAAI1C,EAAKvB,OAAS,GAAiB,IAAZuB,EAAK0C,GAAUA,IAClDe,EAAI+E,KAAK,GACb,OAAO/E,EAAImD,SACf,CACA,MAAMgd,GAAiC,CAACxkB,EAAGd,IAAQA,EAAQslB,GAAItlB,EAAGc,EAAId,GAAfc,EACjDykB,GAAwC,CAACne,EAAM9B,IAAO8B,GAAQ9B,EAAKggB,GAAIle,EAAM9B,IAKnF,SAASkgB,GAAc9jB,EAAM0F,EAAM9B,EAAIsf,GACnC,IAAKzd,MAAMwD,QAAQjJ,GACf,MAAM,IAAI5B,MAAM,uCACpB,GAAIsH,GAAQ,GAAKA,EAAO,GACpB,MAAM,IAAItH,MAAM,6BAA6BsH,KACjD,GAAI9B,GAAM,GAAKA,EAAK,GAChB,MAAM,IAAIxF,MAAM,2BAA2BwF,KAC/C,GAAIigB,GAAYne,EAAM9B,GAAM,GACxB,MAAM,IAAIxF,MAAM,sCAAsCsH,QAAW9B,eAAgBigB,GAAYne,EAAM9B,MAEvG,IAAI0f,EAAQ,EACR3hB,EAAM,EACV,MAAMmW,EAAO,GAAKlU,EAAK,EACjBH,EAAM,GACZ,IAAK,MAAMxF,KAAK+B,EAAM,CAClB,GAAI/B,GAAK,GAAKyH,EACV,MAAM,IAAItH,MAAM,oCAAoCH,UAAUyH,KAElE,GADA4d,EAASA,GAAS5d,EAAQzH,EACtB0D,EAAM+D,EAAO,GACb,MAAM,IAAItH,MAAM,qCAAqCuD,UAAY+D,KAErE,IADA/D,GAAO+D,EACA/D,GAAOiC,EAAIjC,GAAOiC,EACrBH,EAAI+E,MAAO8a,GAAU3hB,EAAMiC,EAAOkU,KAAU,GAChDwL,GAAS,GAAK3hB,EAAM,CACvB,CAED,GADA2hB,EAASA,GAAU1f,EAAKjC,EAAQmW,GAC3BoL,GAAWvhB,GAAO+D,EACnB,MAAM,IAAItH,MAAM,kBACpB,IAAK8kB,GAAWI,EACZ,MAAM,IAAIllB,MAAM,qBAAqBklB,KAGzC,OAFIJ,GAAWvhB,EAAM,GACjB8B,EAAI+E,KAAK8a,IAAU,GAChB7f,CACX,CAuBA,SAASsgB,GAAO3M,EAAM4M,GAAa,GAC/B,GAAI5M,GAAQ,GAAKA,EAAO,GACpB,MAAM,IAAIhZ,MAAM,qCACpB,GAAIylB,GAAY,EAAGzM,GAAQ,IAAMyM,GAAYzM,EAAM,GAAK,GACpD,MAAM,IAAIhZ,MAAM,0BACpB,MAAO,CACH+B,OAAS9B,IACL,KAAMA,aAAiBG,YACnB,MAAM,IAAIJ,MAAM,4CACpB,OAAO0lB,GAAcre,MAAMC,KAAKrH,GAAQ,EAAG+Y,GAAO4M,EAAW,EAEjExB,OAASE,IACL,IAAKjd,MAAMwD,QAAQyZ,IAAYA,EAAOjkB,QAA+B,iBAAdikB,EAAO,GAC1D,MAAM,IAAItkB,MAAM,kDACpB,OAAOI,WAAWkH,KAAKoe,GAAcpB,EAAQtL,EAAM,EAAG4M,GAAY,EAG9E,CAIA,SAASC,GAAcC,GACnB,GAAkB,mBAAPA,EACP,MAAM,IAAI9lB,MAAM,uCACpB,OAAO,YAAakjB,GAChB,IACI,OAAO4C,EAAGC,MAAM,KAAM7C,EACzB,CACD,MAAOpa,GAAM,CACrB,CACA,CA8BA,MAAMkd,GAAyB9B,GAAMyB,GAAO,GAAItB,GAAS,oEAAqES,GAAQ,GAAIH,GAAK,KACzIsB,GAA4B/B,GAAMyB,GAAO,GAAItB,GAAS,oEAAqES,GAAQ,GAAIH,GAAK,KAG5IuB,GAAaC,IAAQjC,WAnFZrc,EAmFwB,GAlF5B,CACH9F,OAAS9B,IACL,KAAMA,aAAiBG,YACnB,MAAM,IAAIJ,MAAM,2CACpB,OAAOilB,GAAa5d,MAAMC,KAAKrH,GAAQ,IAAQ4H,EAAI,EAEvDuc,OAASE,IACL,IAAKjd,MAAMwD,QAAQyZ,IAAYA,EAAOjkB,QAA+B,iBAAdikB,EAAO,GAC1D,MAAM,IAAItkB,MAAM,iDACpB,OAAOI,WAAWkH,KAAK2d,GAAaX,EAAQzc,EAAK,KAAQ,IAyEzBwc,GAAS8B,GAAMxB,GAAK,KAnFhE,IAAe9c,CAmFqD,EAC9Due,GAAyBF,GAAU,8DACnCG,GAA+Btf,GAAWmd,GAhChD,SAAkBzgB,EAAKqiB,GACnB,GAAkB,mBAAPA,EACP,MAAM,IAAI9lB,MAAM,kCACpB,MAAO,CACH,MAAA+B,CAAOH,GACH,KAAMA,aAAgBxB,YAClB,MAAM,IAAIJ,MAAM,+CACpB,MAAMsmB,EAAWR,EAAGlkB,GAAM0D,MAAM,EAAG7B,GAC7B4B,EAAM,IAAIjF,WAAWwB,EAAKvB,OAASoD,GAGzC,OAFA4B,EAAIxB,IAAIjC,GACRyD,EAAIxB,IAAIyiB,EAAU1kB,EAAKvB,QAChBgF,CACV,EACD,MAAA+e,CAAOxiB,GACH,KAAMA,aAAgBxB,YAClB,MAAM,IAAIJ,MAAM,+CACpB,MAAMumB,EAAU3kB,EAAK0D,MAAM,GAAI7B,GACzB+iB,EAAcV,EAAGS,GAASjhB,MAAM,EAAG7B,GACnCgjB,EAAc7kB,EAAK0D,OAAO7B,GAChC,IAAK,IAAIa,EAAI,EAAGA,EAAIb,EAAKa,IACrB,GAAIkiB,EAAYliB,KAAOmiB,EAAYniB,GAC/B,MAAM,IAAItE,MAAM,oBACxB,OAAOumB,CACV,EAET,CAOsDD,CAAS,GAAI1kB,GAASmF,EAAOA,EAAOnF,MAASwkB,IAC7FM,GAAgCxC,GAAMG,GAAS,oCAAqCM,GAAK,KACzFgC,GAAqB,CAAC,UAAY,UAAY,UAAY,WAAY,WAI5E,SAASC,GAAcC,GACnB,MAAM3mB,EAAI2mB,GAAO,GACjB,IAAIC,GAAa,SAAND,IAAoB,EAC/B,IAAK,IAAIviB,EAAI,EAAGA,EAAIqiB,GAAmBtmB,OAAQiE,IACpB,IAAjBpE,GAAKoE,EAAK,KACZwiB,GAAOH,GAAmBriB,IAElC,OAAOwiB,CACX,CAIA,SAASC,GAAa5K,EAAQ6K,EAAOC,EAAgB,GACjD,MAAMxjB,EAAM0Y,EAAO9b,OACnB,IAAIymB,EAAM,EACV,IAAK,IAAIxiB,EAAI,EAAGA,EAAIb,EAAKa,IAAK,CAC1B,MAAMoB,EAAIyW,EAAOoG,WAAWje,GAC5B,GAAIoB,EAAI,IAAMA,EAAI,IACd,MAAM,IAAI1F,MAAM,mBAAmBmc,MACvC2K,EAAMF,GAAcE,GAAQphB,GAAK,CACpC,CACDohB,EAAMF,GAAcE,GACpB,IAAK,IAAIxiB,EAAI,EAAGA,EAAIb,EAAKa,IACrBwiB,EAAMF,GAAcE,GAA+B,GAAvB3K,EAAOoG,WAAWje,GAClD,IAAK,IAAIuF,KAAKmd,EACVF,EAAMF,GAAcE,GAAOjd,EAC/B,IAAK,IAAIvF,EAAI,EAAGA,EAAI,EAAGA,IACnBwiB,EAAMF,GAAcE,GAExB,OADAA,GAAOG,EACAP,GAAc3kB,OAAO2jB,GAAc,CAACoB,EAAM,GAAK,IAAK,GAAI,GAAG,GACtE,CAIA,SAASI,GAAUC,GACf,MAAMC,EAA8B,WAAbD,EAAwB,EAAI,UAC7CE,EAAS1B,GAAO,GAChB2B,EAAYD,EAAOjD,OACnBmD,EAAUF,EAAOtlB,OACjBylB,EAAkB3B,GAAcyB,GAatC,SAASlD,EAAOviB,EAAK4lB,EAAQ,IACzB,GAAmB,iBAAR5lB,EACP,MAAM,IAAI7B,MAAM,oDAAoD6B,GACxE,GAAIA,EAAIxB,OAAS,IAAgB,IAAVonB,GAAmB5lB,EAAIxB,OAASonB,EACnD,MAAM,IAAIC,UAAU,wBAAwB7lB,EAAIxB,WAAWwB,oBAAsB4lB,MAErF,MAAME,EAAU9lB,EAAI+lB,cACpB,GAAI/lB,IAAQ8lB,GAAW9lB,IAAQA,EAAIgmB,cAC/B,MAAM,IAAI7nB,MAAM,yCAEpB,MAAM8nB,GADNjmB,EAAM8lB,GACeI,YAAY,KACjC,GAAiB,IAAbD,IAAgC,IAAdA,EAClB,MAAM,IAAI9nB,MAAM,2DACpB,MAAMmc,EAASta,EAAIyD,MAAM,EAAGwiB,GACtBT,EAASxlB,EAAIyD,MAAMwiB,EAAW,GACpC,GAAIT,EAAOhnB,OAAS,EAChB,MAAM,IAAIL,MAAM,2CACpB,MAAMgnB,EAAQN,GAActC,OAAOiD,GAAQ/hB,MAAM,GAAI,GAC/C6D,EAAM4d,GAAa5K,EAAQ6K,EAAOI,GACxC,IAAKC,EAAOW,SAAS7e,GACjB,MAAM,IAAInJ,MAAM,uBAAuB6B,gBAAkBsH,MAC7D,MAAO,CAAEgT,SAAQ6K,QACpB,CAMD,MAAO,CAAEjlB,OAxCT,SAAgBoa,EAAQ6K,EAAOS,EAAQ,IACnC,GAAsB,iBAAXtL,EACP,MAAM,IAAInc,MAAM,qDAAqDmc,GACzE,IAAK9U,MAAMwD,QAAQmc,IAAWA,EAAM3mB,QAA8B,iBAAb2mB,EAAM,GACvD,MAAM,IAAIhnB,MAAM,8DAA8DgnB,GAClF,MAAMiB,EAAe9L,EAAO9b,OAAS,EAAI2mB,EAAM3mB,OAC/C,IAAc,IAAVonB,GAAmBQ,EAAeR,EAClC,MAAM,IAAIC,UAAU,UAAUO,mBAA8BR,KAChE,MAAME,EAAUxL,EAAOyL,cACjBze,EAAM4d,GAAaY,EAASX,EAAOI,GACzC,MAAO,GAAGO,KAAWjB,GAAc3kB,OAAOilB,KAAS7d,GACtD,EA6BgBib,SAAQ8D,cAJzB,SAAuBrmB,GACnB,MAAMsa,OAAEA,EAAM6K,MAAEA,GAAU5C,EAAOviB,GAAK,GACtC,MAAO,CAAEsa,SAAQ6K,QAAO/mB,MAAOqnB,EAAUN,GAC5C,EACuCmB,aALnBtC,GAAczB,GAKmBkD,YAAWE,kBAAiBD,UACtF,CACA,MAAMa,GAAyBlB,GAAU,UACnCmB,GAA0BnB,GAAU,WAEpCoB,GAAU,CACZC,OAAQ,CACJxmB,OAASH,GAASykB,GAAYtf,IAAQhF,OAAOH,GAC7CwiB,OAASxiB,GAASykB,GAAYtf,IAAQqd,OAAOxiB,IAEjDokB,OAAQ,CACJjkB,OAASH,GAASokB,GAAOjkB,OAAOH,GAChCwiB,OAASxiB,GAASokB,GAAO5B,OAAOxiB,IAEpC4mB,OAAQ,CACJzmB,OAASH,GAASqkB,GAAUlkB,OAAOH,GACnCwiB,OAASxiB,GAASqkB,GAAU7B,OAAOxiB,IAEvCwmB,OAAQ,CACJK,SAAUL,GAAOb,QACjBmB,SAAUN,GAAOd,UACjBvlB,OAAQ,CAACoa,EAAQ6K,EAAOS,GAAQ,IACrBW,GAAOrmB,OAAOoa,EAAQ6K,EAAOS,GAExCrD,OAAQ,CAACxiB,EAAM6lB,GAAQ,KACnB,MAAMtL,OAAEA,EAAM6K,MAAEA,GAAUoB,GAAOhE,OAAOxiB,EAAM6lB,GAC9C,MAAO,CAAEtL,SAAQ6K,QAAO,GAGhCqB,QAAS,CACLI,SAAUJ,GAAQd,QAClBmB,SAAUL,GAAQf,UAClBvlB,OAAQ,CAACoa,EAAQ6K,EAAOS,GAAQ,IACrBY,GAAQtmB,OAAOoa,EAAQ6K,EAAOS,GAEzCrD,OAAQ,CAACxiB,EAAM6lB,GAAQ,KACnB,MAAMtL,OAAEA,EAAM6K,MAAEA,GAAUqB,GAAQjE,OAAOxiB,EAAM6lB,GAC/C,MAAO,CAAEtL,SAAQ6K,QAAO,IA0BpC,SAAS2B,GAAY9gB,GACjB,GAAIA,EAAM/H,OAAO8oB,iBACb,MAAM,IAAIlB,UAAU,8BAE5B,CACA,SAASmB,GAAUC,EAAQC,GACvB,GAAID,IAAWC,EACX,MAAM,IAAIrB,UAAU,iCAAiCoB,SAAcC,IAE3E,CAWA,MAAM9hB,GAAMvC,OAAO,GACbskB,GAAQtkB,OAAO,KACfukB,GAAQvkB,OAAO,KAiBrB,SAASwkB,GAAWC,EAAKC,EAAMC,EAAS,WACvBvoB,IAATsoB,IACAA,EAlBR,SAAkBD,GACd,GAAIA,GAAO,MACP,OAAO,EACX,GAAIA,GAAO,QACP,OAAO,EACX,GAAIA,GAAO,YACP,OAAO,EACX,GAAIA,GAAO,oBACP,OAAO,EACX,GAAIA,GAAO,oCACP,OAAO,GACX,GAAIA,GAAO,oEACP,OAAO,GAEX,MAAM,IAAIzB,UAAU,sEACxB,CAGe4B,CAASH,IACpB,MAAMI,EAAqB,OAAXF,EACVjoB,EAAS,IAAIooB,YAAYJ,GACzBplB,EAAW,IAAI7C,SAASC,GAC9B,IAAImF,EAAS,EAAW,EAAI6iB,EAAO,EACnC,KAAOD,EAAMliB,IAAK,CACd,MAAMkB,EAAOghB,EAAMH,GACbnhB,EAAM/H,OAAOqI,GACfohB,EACAvlB,EAASylB,SAASljB,IAAUsB,GAG5B7D,EAASylB,SAASljB,IAAUsB,GAEhCshB,GAAOA,EAAMhhB,GAAQ8gB,EACxB,CACD,OAAO,IAAI7oB,WAAWgB,EAC1B,CA+CA,SAASsoB,GAAW7hB,EAAKuhB,EAAMC,EAAS,WACvBvoB,IAATsoB,IACAA,EAXR,SAAkBvhB,GACd,GAAIA,GAAO,IACP,OAAO,EACX,GAAIA,GAAO,MACP,OAAO,EACX,GAAIA,GAAO,WACP,OAAO,EACX,MAAM,IAAI6f,UAAU,yDACxB,CAGeiC,CAAS9hB,IACpB,MAAM0hB,EAAqB,OAAXF,EACVjoB,EAAS,IAAIooB,YAAYJ,GACzBplB,EAAW,IAAI7C,SAASC,GAC9B,IAAImF,EAAS,EAAW,EAAI6iB,EAAO,EACnC,KAAOvhB,EAAM,GAAG,CACZ,MAAMM,EAAa,IAANN,EACT0hB,EACAvlB,EAASylB,SAASljB,IAAUsB,GAG5B7D,EAASylB,SAASljB,IAAUsB,GAEhCA,GAAOA,EAAMM,GAAQ,GACxB,CACD,OAAO,IAAI/H,WAAWgB,EAC1B,CAUA,MAAMwoB,GAAK,IAAI9nB,YACT+nB,GAAK,IAAIC,YACf,SAASC,GAAWloB,GAChB,OAAO+nB,GAAG7nB,OAAOF,EACrB,CACA,SAASmoB,GAAW/pB,GAChB,OAAO4pB,GAAGzF,OAAOnkB,EACrB,CACA,SAASgqB,GAASC,EAAQd,IA7J1B,SAAkBzhB,GACd,GAAkC,OAA9BA,EAAIwiB,MAAM,gBACV,MAAM,IAAIzC,UAAU,qCAAuC/f,GAE/D,GAAIA,EAAItH,OAAS,GAAM,EACnB,MAAM,IAAIL,MAAM,oCAAoC2H,EAAItH,SAEhE,CAuJI+pB,CAASF,GACT,MAAMzmB,EAAMymB,EAAO7pB,OAAS,EAG5B,QAFaS,IAATsoB,IACAA,EAAO3lB,GACPA,EAAM2lB,EACN,MAAM,IAAI1B,UAAU,yCAAyCjkB,OAAS2lB,KAE1E,OAAOA,CACX,CAmCA,MAAMnmB,gBAAEA,IAAoBrC,QAAUC,WAAWD,QAAU4Y,OAAO5Y,OAOlE,SAASypB,GAAO9F,GACZ,OAAoC,OAAhCA,EAAM4F,MAAM,iBACZ5F,EAAMlkB,OAAS,GAAM,CAI7B,CACA,SAASiqB,GAAS/F,GACd,QAAqB,iBAAVA,IACP8F,GAAO9F,MAGe,iBAAVA,GACK,iBAAVA,GACPA,aAAiBnkB,eAGZiH,MAAMwD,QAAQ0Z,KACnBA,EAAMgG,OAAMzhB,GAAkB,iBAANA,KAMhC,CACA,SAAS0hB,GAAW5oB,EAAMwnB,EAAMC,EAAS,WACxBvoB,IAATsoB,IACAA,EAAOxnB,EAAKvB,QAhPpB,SAAqBuB,EAAMwnB,GACvB,GAAIxnB,EAAKvB,OAAS+oB,EACd,MAAM,IAAI1B,UAAU,mCAAmC9lB,EAAKvB,YAAY+oB,IAEhF,CA6OIqB,CAAY7oB,EAAMwnB,GAClB,MAAMhoB,EAAS,IAAIhB,WAAWgpB,GAAM/kB,KAAK,GACnCkC,EAAqB,OAAX8iB,EAAmB,EAAID,EAAOxnB,EAAKvB,OAEnD,OADAe,EAAOyC,IAAIjC,EAAM2E,GACVnF,CACX,CACA,SAASspB,GAAWxpB,GAChB,IAAIoD,EAAGiC,EAAS,EAChB,MAAM6iB,EAAOloB,EAAIgI,QAAO,CAACzF,EAAKvC,IAAQuC,EAAMvC,EAAIb,QAAQ,GAClDsqB,EAAO,IAAIvqB,WAAWgpB,GAC5B,IAAK9kB,EAAI,EAAGA,EAAIpD,EAAIb,OAAQiE,IAAK,CAC7B,MAAMtD,EAAIE,EAAIoD,GACdqmB,EAAK9mB,IAAI7C,EAAGuF,GACZA,GAAUvF,EAAEX,MACf,CACD,OAAOsqB,CACX,CACA,SAASC,GAAgBrjB,EAAGsC,GACxB,MAAoB,iBAANA,EACR,GAAGA,KACHA,CACV,CACA,SAASghB,GAAetjB,EAAGsC,GACvB,MAAoB,iBAANA,GAAkB,KAAKihB,KAAKjhB,GACpCnF,OAAOmF,EAAEvE,MAAM,GAAI,IACnBuE,CACV,CAgCA,SAASkhB,GAAYnpB,EAAMwnB,EAAMC,GAC7B,GAAIznB,aAAgB4nB,YAChB,OAAO,IAAIppB,WAAWwB,GAErB,GAAIA,aAAgBxB,WACrB,OAAOoqB,GAAW5oB,EAAMwnB,EAAMC,GAE7B,GAAIhiB,MAAMwD,QAAQjJ,GAAO,CAE1B,OAAO8oB,GADO9oB,EAAKwP,KAAItI,GAAKiiB,GAAYjiB,EAAGsgB,EAAMC,KAEpD,CACI,GAAoB,iBAATznB,EACZ,OA3IR,SAAoBsoB,EAAQd,EAAMC,EAAS,MACvCD,EAAOa,GAASC,EAAQd,GACxB,MAAMG,EAAqB,OAAXF,EACVjoB,EAAS,IAAIooB,YAAYJ,GACzBplB,EAAW,IAAI7C,SAASC,GAC9B,IAAImF,EAAS,EAAW,EAAI6iB,EAAO,EACnC,IAAK,IAAI9kB,EAAI,EAAGA,EAAI4lB,EAAO7pB,OAAQiE,GAAK,EAAG,CACvC,MAAM0mB,EAAOd,EAAOe,UAAU3mB,EAAGA,EAAI,GAC/BuD,EAAMO,SAAS4iB,EAAM,IACvBzB,EACAvlB,EAASylB,SAASljB,IAAUsB,GAG5B7D,EAASylB,SAASljB,IAAUsB,EAEnC,CACD,OAAO,IAAIzH,WAAWgB,EAC1B,CA0He2G,CAAWnG,EAAMwnB,EAAMC,GAE7B,GAAoB,iBAATznB,EACZ,OAAOsnB,GAAWtnB,EAAMwnB,EAAMC,GAE7B,GAAoB,iBAATznB,EACZ,OAAO8nB,GAAW9nB,EAAMwnB,EAAMC,GAE7B,GAAoB,kBAATznB,EACZ,OAAOxB,WAAW8qB,GAAGtpB,EAAO,EAAI,GAEpC,MAAM,IAAI8lB,UAAU,sBAAwB/b,cAAc/J,GAC9D,CAEA,MAAMupB,WAAa/qB,kBACN+B,KAAK0F,IAAMujB,EAAY,QACvBjpB,KAAKgnB,IAAMkC,EAAY,QACvBlpB,KAAKmpB,IAAMC,EAAY,QACvBppB,KAAKqpB,IAAMC,EAAY,QACvBtpB,KAAKN,IAAM6pB,EAAY,QACvBvpB,KAAKwF,IAAMgkB,EAAY,QACvBxpB,KAAKlC,MAAQmB,EAAS,QACtBe,KAAKypB,KAAOC,EAAa,QACzB1pB,KAAK6jB,OAAS8F,EAAe,QAC7B3pB,KAAKqmB,OAASuD,EAAe,QAC7B5pB,KAAKimB,OAAS4D,EAAe,QAC7B7pB,KAAKkmB,QAAU4D,EAAgB,QAC/B9pB,KAAKomB,OAAS2D,EAAe,QAC7B/pB,KAAKJ,OAASgoB,EAAa,QAC3B5nB,KAAKiiB,OAAS4F,EAAa,QAC3B7nB,KAAKgqB,MAAQC,EAAa,QAC1BjqB,KAAKmoB,SAAWA,EAAW,QAC3BnoB,KAAKkoB,OAASA,EAAS,CAChC,aAAOgC,CAAOjD,EAAO,IACjB,MAAM7F,EA1Id,SAAgB6F,EAAO,IACnB,GAA+B,mBAApBnmB,GACP,OAAOrC,OAAOqC,gBAAgB,IAAI7C,WAAWgpB,IAEjD,MAAM,IAAIppB,MAAM,yCACpB,CAqIqBqsB,CAAOjD,GACpB,OAAO,IAAI+B,GAAK5H,EAAM6F,EACzB,CACD,UAAOkD,CAAIlD,EAAO,GACd,MAAMmD,EAAQ5oB,KAAK4hB,MAAMiH,KAAKF,MAAQ,KACtC,OAAO,IAAInB,GAAKoB,EAAOnD,EAC1B,CACD,WAAAjmB,CAAYvB,EAAMwnB,EAAMC,GACpB,GAAIznB,aAAgBupB,SACPrqB,IAATsoB,EACA,OAAOxnB,EAGX0B,MADeynB,GAAYnpB,EAAMwnB,EAAMC,GAE1C,CACD,OAAInoB,GACA,MAAO,IAAIiB,KACd,CACD,OAAI0F,GACA,OAAO1F,KAAKsqB,QACf,CACD,OAAItD,GACA,OAAOhnB,KAAKuqB,QACf,CACD,OAAI7qB,GACA,OAAOM,KAAKwqB,QACf,CACD,OAAIhlB,GACA,OAAOxF,KAAKyqB,QACf,CACD,OAAIpB,GACA,OAAO,IAAIprB,WAAW+B,KACzB,CACD,OAAImpB,GACA,OAAOnpB,KAAK0qB,QACf,CACD,UAAItE,GACA,OAAOpmB,KAAK2qB,WACf,CACD,UAAI9G,GACA,OAAO7jB,KAAK4qB,WACf,CACD,UAAIvE,GACA,OAAOrmB,KAAK6qB,WACf,CACD,UAAItqB,GACA,OAAOP,KAAK8qB,SACf,CACD,MAAIC,GACA,OAAO/qB,KAAK8qB,UAAUtlB,GACzB,CACD,UAAIwlB,GACA,OAAO,IAAIC,GAAOjrB,KACrB,CACD,MAAAsqB,CAAOpD,EAAS,MAIZ,OAlQR,SAAoBppB,GAChB,IAAI4H,EAAM,EACV,IAAK,IAAIvD,EAAIrE,EAAMI,OAAS,EAAGiE,GAAK,EAAGA,IACnCuD,EAAa,IAANA,EAAa5H,EAAMqE,GAC1BqkB,GAAY9gB,GAEhB,OAAOA,CACX,CA2PewlB,CAHmB,OAAXhE,EACTlnB,KAAKqG,UACLrG,KAET,CACD,MAAAuqB,CAAOrD,EAAS,MAIZ,OAzUR,SAAoBppB,GAChB,IAAI4H,EAAMnD,OAAO,GACjB,IAAK,IAAIJ,EAAIrE,EAAMI,OAAS,EAAGiE,GAAK,EAAGA,IACnCuD,EAAOA,EAAMohB,GAASvkB,OAAOzE,EAAMqE,IAEvC,OAAOI,OAAOmD,EAClB,CAmUeylB,CAHmB,OAAXjE,EACTlnB,KAAKqG,UACLrG,KAET,CACD,MAAA0qB,GACI,OArTR,SAAoB5sB,GAChB,MAAMqrB,EAAM,IAAIjkB,MAAqB,EAAfpH,EAAMI,QAC5B,IAAIktB,EAAQ,EACZ,IAAK,MAAM1lB,KAAO5H,EAAO,CACrB,GAAI4H,EAAM,IACN,MAAM,IAAI7H,MAAM,uBAAuB6H,6CAE3C,IAAK,IAAIvD,EAAI,EAAGA,GAAK,EAAGA,IAAKipB,IACzBjC,EAAIiC,GAAU1lB,GAAOvD,EAAK,CAEjC,CACD,OAAOgnB,EAAI3G,KAAK,GACpB,CAySe6I,CAAWrrB,KACrB,CACD,OAAA8qB,GACI,MAAMvqB,EAASqE,GAAO5E,MACtB,OAAO,IAAIgpB,GAAKzoB,EACnB,CACD,OAAA+qB,CAAQC,QACY5sB,IAAZ4sB,IACAA,EAAU7C,IAEd,MAAMhpB,EAAMmoB,GAAW7nB,MACvB,OAAOwrB,KAAKxB,MAAMtqB,EAAK6rB,EAC1B,CACD,SAAAE,CAAUzR,EAAQsL,GACd,MAAM1lB,OAAEA,EAAM0mB,SAAEA,GAAaH,GAAQF,OAErC,OAAOrmB,EAAOoa,EADAsM,EAAStmB,MACMslB,EAChC,CACD,UAAAoG,CAAW1R,EAAQsL,GACf,MAAM1lB,OAAEA,EAAM0mB,SAAEA,GAAaH,GAAQD,QAErC,OAAOtmB,EAAOoa,EADAsM,EAAStmB,MACMslB,EAChC,CACD,MAAAkF,GAAW,OAAO3C,GAAW7nB,KAAQ,CACrC,MAAAyqB,GAAW,OAtPf,SAAoB3sB,GAChB,IAAI6tB,EAAQ,GACZ,IAAK,IAAIxpB,EAAI,EAAGA,EAAIrE,EAAMI,OAAQiE,IAC9BwpB,GAAS7tB,EAAMqE,GAAGkD,SAAS,IAAIC,SAAS,EAAG,KAE/C,OAAOqmB,CACX,CAgPsBpmB,CAAWvF,KAAQ,CACrC,QAAAumB,GAAa,OAAO,IAAItoB,WAAW+B,KAAQ,CAC3C,SAAA2qB,GAAc,OAAOxE,GAAQC,OAAOxmB,OAAOI,KAAQ,CACnD,SAAA4qB,GAAc,OAAOzE,GAAQtC,OAAOjkB,OAAOI,KAAQ,CACnD,SAAA6qB,GAAc,OAAO1E,GAAQE,OAAOzmB,OAAOI,KAAQ,CACnD,MAAA4rB,CAAOnsB,GACH,OAAOupB,GAAKxG,KAAK,CAACxiB,KAAMgpB,GAAKlrB,MAAM2B,IACtC,CACD,OAAAosB,CAAQpsB,GACJ,OAAOupB,GAAKxG,KAAK,CAACwG,GAAKlrB,MAAM2B,GAAOO,MACvC,CACD,OAAAqG,GACI,MAAMtH,EAAM,IAAId,WAAW+B,MAAMqG,UACjC,OAAO,IAAI2iB,GAAKjqB,EACnB,CACD,KAAAoE,CAAM2oB,EAAOjJ,GACT,MAAM9jB,EAAM,IAAId,WAAW+B,MAAMmD,MAAM2oB,EAAOjJ,GAC9C,OAAO,IAAImG,GAAKjqB,EACnB,CACD,GAAA2C,CAAImE,EAAOzB,GACPpE,KAAK0B,IAAImE,EAAOzB,EACnB,CACD,QAAAzC,CAASoqB,EAAOlJ,GACZ,MAAM9jB,EAAM,IAAId,WAAW+B,MAAM2B,SAASoqB,EAAOlJ,GACjD,OAAO,IAAImG,GAAKjqB,EACnB,CACD,KAAAitB,CAAMluB,EAAOsG,GACT,MAAMrG,EAAIirB,GAAKlrB,MAAMA,GACrBkC,KAAK0B,IAAI3D,EAAGqG,EACf,CACD,UAAA6nB,CAAW/E,GACP,MAAMD,EAAO+B,GAAKkD,YAAYlsB,KAAK9B,OAAQgpB,GAC3C,OAAO8B,GAAKxG,KAAK,CAACyE,EAAMjnB,MAC3B,CACD,WAAOmF,CAAK1F,GACR,OAAO,IAAIupB,GAAK/qB,WAAWkH,KAAK1F,GACnC,CACD,SAAOspB,IAAMhI,GACT,OAAO,IAAIiI,GAAK/qB,WAAW8qB,MAAMhI,GACpC,CACD,WAAOyB,CAAKzjB,GACR,MACMotB,EAAS5D,GADDxpB,EAAIkQ,KAAItI,GAAKqiB,GAAKlrB,MAAM6I,MAEtC,OAAO,IAAIqiB,GAAKmD,EACnB,CACD,WAAOC,CAAKrtB,EAAKkoB,GACb,MAAMzhB,EAAMzG,EAAIkQ,KAAItI,GAAK1H,GAAO0H,EAAGsgB,GAAMzhB,MAEzC,OADAA,EAAI4mB,OACG5mB,EAAIyJ,KAAItI,GAAKqiB,GAAKxjB,IAAImB,EAAGsgB,IACnC,CACD,kBAAOiF,CAAYxmB,EAAKwhB,GACpB,GAAIxhB,EAAM,IACN,OAAOsjB,GAAKtjB,IAAIA,EAAK,GAEpB,GAAIA,EAAM,MACX,OAAOsjB,GAAKD,GAAG,OAASC,GAAKtjB,IAAIA,EAAK,EAAGwhB,IAExC,GAAIxhB,EAAM,WACX,OAAOsjB,GAAKD,GAAG,OAASC,GAAKtjB,IAAIA,EAAK,EAAGwhB,IAExC,GAAI3kB,OAAOmD,GAAO,qBACnB,OAAOsjB,GAAKD,GAAG,OAASC,GAAKtjB,IAAIA,EAAK,EAAGwhB,IAGzC,MAAM,IAAIrpB,MAAM,uBAAuB6H,IAE9C,EAEL,SAASujB,GAAUxrB,EAAQwpB,EAAMC,GAC7B,OAAO,IAAI8B,GAAKvrB,EAAQwpB,EAAMC,EAClC,CACA,SAASkC,GAAU3pB,EAAMwnB,EAAMC,GAC3B,OAAO,IAAI8B,GApaf,SAAoBqD,GAChB,MAAMC,EAAOD,EAAO3J,MAAM,IAAIzT,IAAItR,QAClC,GAAI2uB,EAAKpuB,OAAS,GAAM,EACpB,MAAM,IAAIL,MAAM,mCAAmCwuB,EAAOnuB,UAE9D,MAAMJ,EAAQ,IAAIG,WAAWquB,EAAKpuB,OAAS,GAC3C,IAAK,IAAIiE,EAAI,EAAGoqB,EAAK,EAAGpqB,EAAImqB,EAAKpuB,OAAQiE,GAAK,EAAGoqB,IAAM,CACnD,IAAIvmB,EAAO,EACX,IAAK,IAAIF,EAAI,EAAGA,EAAI,EAAGA,IACnBE,GAASsmB,EAAKnqB,EAAI2D,IAAO,EAAIA,EAEjChI,EAAMyuB,GAAMvmB,CACf,CACD,OAAOlI,CACX,CAsZoB0uB,CAAW/sB,GAAOwnB,EAAMC,EAC5C,CACA,SAASgC,GAAU9gB,EAAQ6e,EAAMC,GAC7B,OAAO,IAAI8B,GAAK5gB,EAAQ6e,EAAMC,EAClC,CACA,SAASoC,GAAU7pB,EAAMwnB,EAAMC,GAC3B,OAAO,IAAI8B,GAAKvpB,EAAMwnB,EAAMC,EAChC,CACA,SAASqC,GAAU9pB,EAAMwnB,EAAMC,GAC3B,OAAO,IAAI8B,GAAKpB,GAAWnoB,GAAOwnB,EAAMC,EAC5C,CACA,SAASsC,GAAU/pB,EAAMwnB,EAAMC,GAC3B,OAAO,IAAI8B,GAAKvpB,EAAMwnB,EAAMC,EAChC,CACA,SAASwC,GAAWjqB,EAAMgtB,QACL9tB,IAAb8tB,IACAA,EAAWhE,IAEf,MAAM/oB,EAAM8rB,KAAKkB,UAAUjtB,EAAMgtB,GACjC,OAAO,IAAIzD,GAAKpB,GAAWloB,GAC/B,CACA,SAASiqB,GAAalqB,GAClB,OAAO,IAAIupB,GAAK7C,GAAQtC,OAAO5B,OAAOxiB,GAC1C,CACA,SAASmqB,GAAanqB,GAClB,OAAO,IAAIupB,GAAK7C,GAAQE,OAAOpE,OAAOxiB,GAC1C,CACA,SAASoqB,GAAapqB,EAAM6lB,EAAOqH,GAC/B,MAAM1K,OAAEA,EAAMsE,SAAEA,GAAaJ,GAAQF,QAC/BjM,OAAEA,EAAM6K,MAAEA,GAAU5C,EAAOxiB,EAAM6lB,GACjCxnB,EAAQyoB,EAAS1B,GAIvB,MAH0B,iBAAf8H,GACPjG,GAAU1M,EAAQ2S,GAEf,IAAI3D,GAAKlrB,EACpB,CACA,SAASgsB,GAAcrqB,EAAM6lB,EAAOqH,GAChC,MAAM1K,OAAEA,EAAMsE,SAAEA,GAAaJ,GAAQD,SAC/BlM,OAAEA,EAAM6K,MAAEA,GAAU5C,EAAOxiB,EAAM6lB,GACjCxnB,EAAQyoB,EAAS1B,GAIvB,MAH0B,iBAAf8H,GACPjG,GAAU1M,EAAQ2S,GAEf,IAAI3D,GAAKlrB,EACpB,CACA,SAASisB,GAAatqB,GAClB,OAAO,IAAIupB,GAAK7C,GAAQC,OAAOnE,OAAOxiB,GAC1C,CACA,SAASwqB,GAAW2C,EAAWC,EAAYC,GACvC,MACMC,EAlSV,SAAsBH,EAAWC,EAAYC,GACzC,MAAMxrB,EAAMsrB,EAAU1uB,OAAQktB,EAAQ0B,EAAaD,EACnD,GAAIC,EAAaD,GAAe,EAC5B,MAAM,IAAItH,UAAU,uBAAuBuH,OAAgBD,WAE/D,GAAIvrB,IAAQwrB,EACR,MAAM,IAAIvH,UAAU,wBAAwBjkB,SAAWwrB,KAE3D,GAAIxrB,EAAMurB,GAAe,EACrB,MAAM,IAAItH,UAAU,wBAAwBjkB,OAASurB,WAEzD,MAAME,EAAS,IAAI7nB,MAAMkmB,GACzB,IAAK,IAAIjpB,EAAI,EAAGA,EAAIipB,EAAOjpB,IAAK,CAC5B,MAAM6qB,EAAM7qB,EAAI0qB,EAChBE,EAAO5qB,GAAKyqB,EAAUjrB,SAASqrB,EAAKA,EAAMH,EAC7C,CACD,OAAOE,CACX,CAiRmBE,CADDrE,GAAYgE,GACSC,EAAYC,GAC/C,OAAOC,EAAO9d,KAAItI,GAAKqiB,GAAKlrB,MAAM6I,IACtC,CACA,MAAMskB,GACF,WAAAjqB,CAAYvB,GACRO,KAAKP,KAAOupB,GAAKlrB,MAAM2B,GACvBO,KAAKinB,KAAOjnB,KAAKP,KAAKvB,MACzB,CACD,IAAAgvB,CAAKjG,GACD,GAAIA,EAAOjnB,KAAKinB,KACZ,MAAM,IAAIppB,MAAM,6BAA6BopB,OAAUjnB,KAAKinB,QAEhE,OAAO,IAAI+B,GAAKhpB,KAAKP,KAAK0D,MAAM,EAAG8jB,GACtC,CACD,IAAAkG,CAAKlG,GACD,MAAMmG,EAAQptB,KAAKktB,KAAKjG,GAGxB,OAFAjnB,KAAKP,KAAOO,KAAKP,KAAK0D,MAAM8jB,GAC5BjnB,KAAKinB,KAAOjnB,KAAKP,KAAKvB,OACfkvB,CACV,CACD,WAAAC,CAAYnG,GACR,MAAMxhB,EAAM1F,KAAKmtB,KAAK,GAAGznB,IACzB,QAAQ,GACJ,KAAMA,GAAO,GAAKA,EAAM,IACpB,OAAOA,EACX,KAAc,MAARA,EACF,OAAO1F,KAAKmtB,KAAK,GAAG7C,OAAOpD,GAC/B,KAAc,MAARxhB,EACF,OAAO1F,KAAKmtB,KAAK,GAAG7C,OAAOpD,GAC/B,KAAc,MAARxhB,EACF,OAAO1F,KAAKmtB,KAAK,GAAG7C,OAAOpD,GAC/B,QACI,MAAM,IAAIrpB,MAAM,2BAA2B6H,KAEtD,EAEL,SAASzG,GAAOnB,EAAOmpB,EAAMpE,GACzB,OAAO,IAAImG,GAAKlrB,EAAOmpB,EAAMpE,EACjC,CC/wCA,MAAO7T,GAAAsQ,GAAQjP,MACRid,GAAAte,GAAQtR,EAEf6vB,GAAave,GAAKzB,EAEXigB,GAAA,CAAAjjB,EAASyE,GAAAE,GAAYmC,EAAArC,GAAAG,IACrBrK,GAAAvC,OAAS,GACTwC,GAAAxC,OAAS,GACTyC,GAAAzC,OAAS,GACTsH,GAAAtH,OAAS,4GCDT,MAAAsW,GAAMtO,GAAYN,EAAAM,EAAA+iB,gHAEZ/iB,GAAQN,EAAAM,EAAAgjB,yIAGRhjB,EAAQkjB,IAAAtjB,EAAcI,EAAAkjB,EAAAH,MCXnB,SAAAI,GAAAtrB,EAAAkb,GAQhB,IAAA,IAAAlb,EAYA,MAAA,IAAAvE,MAAgByf,GACR,oBAUR,CAUA,SAAAqQ,GAAA3b,EAAwB2P,GACN,GASlB,IAAAA,EAeA,OAAA,EAOA,MAAA,IAAA9jB,MAAAmU,EAOA,CAMA,SAAAiV,GAAA7E,OAOgB,MAAAtkB,EAAAkrB,GAAAlrB,MAAAskB,ywCClFhB,MAAawL,GAAAC,GAAAP,UACLQ,GAAOxO,GAAApH,mBAEG0V,GAIhB,MAAUphB,WAAMvO,uBAIA8vB,EAAUT,EAAA,CAI1B,UAAOhgB,CAAA/C,GAKM,OAAAA,EAAC0E,KAAatI,GAAA6F,GAAAvC,IAAAtD,KAAAI,QAAA,CAAAwG,EAAA7P,IAAA6P,EAAAD,IAAA5P,IAMvB,CAIJ,UAAOuM,CAAMM,GAIT,OAAS,IAAAiC,GAEZjC,EAEG,CAIJ,UAAIY,CAAWZ,GAIX,OAAOA,EAAM0E,KAAAtI,GAAO6F,GAEvBvC,IAAAtD,KAAAI,QAAA,CAAAwG,EAAA7P,IAAA6P,EAAApC,IAAAzN,IAEG,CAMJ,eAAIswB,CAAQ5rB,EAAcuf,GAUtB,OAAQsM,GALAjF,SAAqB5mB,EAAA,IAAA4kB,IAKArF,EAKjC,CAKA,WAAA3gB,CAAauJ,GAMR,MAAQxM,EAAAmwB,8SAAcC,CAAK5jB,IAM3BiC,GAAQwhB,SAAAjwB,MAMRoD,MAAQ6nB,GAAAhC,IAAAjpB,MAAmB,GAMhC,CAMA,QAAAyqB,GAKA,WAAmBQ,GAAAhpB,KAKpB,CAED,OAAAqpB,GACQ,OAAWrpB,KAAAwoB,KAAAa,GACX,CACN,OAAArC,0UAA8B,CAC9B,GAAA1Z,CAAMlL,GAEA,MAAOmI,EACXiC,GAAKvC,OAgBQpL,EAAAuvB,OAAOpuB,KAAGgnB,IAAAzc,EAAUyc,KAM7B,OAAqB,IAAAxa,GAAA3N,EAErB,CAQN,GAAAwM,CAAAjJ,GAGE,MAAAmI,EAAIiC,GAAAvC,IACH7H,GAMQvD,EAAAuvB,GAEV/iB,IAAArL,KAAAgnB,IAAAzc,EAAAyc,KAEG,OAAO,IAEVxa,GAAA3N,EAEG,CAIJ,GAAAsM,CAAI/I,GAIA,MAASmI,EAAAiC,GAAAvC,IAEZ7H,GAEYvD,EAAAuvB,GAAMjjB,IAElBnL,KAAAgnB,IAAAzc,EAAAyc,KAEG,OAAA,IAAcxa,GAAA3N,EAId,CAIJ,GAAAsL,CAAI/H,GAMA,MAAQmI,EAAAiC,GAAUvC,IAAW7H,GAKxBvD,EAAAuvB,GAAUjkB,IAASnK,KAAAgnB,IAAAzc,EAAAyc,KAMvB,OAAI,IAAAxa,GAAmB3N,EAM5B,CAMA,GAAA6O,CAAAtL,GAGD,MAAAmI,EAAAiC,GAAAvC,IAAA7H,ouDCzPD,MAAMisB,GAA6B9rB,OAAO,GAAK,GAAK,GAC9CD,GAAuBC,OAAO,IAEpC,SAAS+rB,GAAQ5wB,EAAG6wB,GAAK,GACrB,OAAIA,EACO,CAAE5rB,EAAGhF,OAAOD,EAAI2wB,IAAazrB,EAAGjF,OAAQD,GAAK4E,GAAQ+rB,KACzD,CAAE1rB,EAAsC,EAAnChF,OAAQD,GAAK4E,GAAQ+rB,IAAiBzrB,EAA4B,EAAzBjF,OAAOD,EAAI2wB,IACpE,CAqDA,IAAAG,GARY,CACRF,WAAS5L,MA7Cb,SAAexU,EAAKqgB,GAAK,GACrB,IAAIE,EAAK,IAAIlvB,YAAY2O,EAAIhQ,QACzBwwB,EAAK,IAAInvB,YAAY2O,EAAIhQ,QAC7B,IAAK,IAAIiE,EAAI,EAAGA,EAAI+L,EAAIhQ,OAAQiE,IAAK,CACjC,MAAMQ,EAAEA,EAACC,EAAEA,GAAM0rB,GAAQpgB,EAAI/L,GAAIosB,IAChCE,EAAGtsB,GAAIusB,EAAGvsB,IAAM,CAACQ,EAAGC,EACxB,CACD,MAAO,CAAC6rB,EAAIC,EAChB,EAqCoBC,MApCN,CAAChsB,EAAGC,IAAOL,OAAOI,IAAM,IAAML,GAAQC,OAAOK,IAAM,GAqC7DgsB,MAnCU,CAACjsB,EAAGksB,EAAIjf,IAAMjN,IAAMiN,EAmCvBkf,MAlCG,CAACnsB,EAAGC,EAAGgN,IAAOjN,GAAM,GAAKiN,EAAOhN,IAAMgN,EAmChDmf,OAjCW,CAACpsB,EAAGC,EAAGgN,IAAOjN,IAAMiN,EAAMhN,GAAM,GAAKgN,EAiCxCof,OAhCG,CAACrsB,EAAGC,EAAGgN,IAAOjN,GAAM,GAAKiN,EAAOhN,IAAMgN,EAgCjCqf,OA9BL,CAACtsB,EAAGC,EAAGgN,IAAOjN,GAAM,GAAKiN,EAAOhN,IAAOgN,EAAI,GA8B9Bsf,OA7Bb,CAACvsB,EAAGC,EAAGgN,IAAOjN,IAAOiN,EAAI,GAAQhN,GAAM,GAAKgN,EA8BvDuf,QA5BY,CAACC,EAAIxsB,IAAMA,EA4BdysB,QA3BG,CAAC1sB,EAAGksB,IAAOlsB,EA4BvB2sB,OA1BW,CAAC3sB,EAAGC,EAAGgN,IAAOjN,GAAKiN,EAAMhN,IAAO,GAAKgN,EA0BxC2f,OAzBG,CAAC5sB,EAAGC,EAAGgN,IAAOhN,GAAKgN,EAAMjN,IAAO,GAAKiN,EAyBhC4f,OAvBL,CAAC7sB,EAAGC,EAAGgN,IAAOhN,GAAMgN,EAAI,GAAQjN,IAAO,GAAKiN,EAuB/B6f,OAtBb,CAAC9sB,EAAGC,EAAGgN,IAAOjN,GAAMiN,EAAI,GAAQhN,IAAO,GAAKgN,EAuB3DtC,IApBA,SAAamhB,EAAIC,EAAIgB,EAAIC,GACrB,MAAM/sB,GAAK8rB,IAAO,IAAMiB,IAAO,GAC/B,MAAO,CAAEhtB,EAAI8rB,EAAKiB,GAAO9sB,EAAI,GAAK,GAAM,GAAM,EAAGA,EAAO,EAAJA,EACxD,EAiBSgtB,MAfK,CAAClB,EAAIiB,EAAIE,KAAQnB,IAAO,IAAMiB,IAAO,IAAME,IAAO,GAehDC,MAdF,CAACC,EAAKtB,EAAIiB,EAAIM,IAAQvB,EAAKiB,EAAKM,GAAOD,EAAM,GAAK,GAAM,GAAM,EAcrDE,MAbT,CAACvB,EAAIiB,EAAIE,EAAIK,KAAQxB,IAAO,IAAMiB,IAAO,IAAME,IAAO,IAAMK,IAAO,GAanDC,MAZhB,CAACJ,EAAKtB,EAAIiB,EAAIM,EAAII,IAAQ3B,EAAKiB,EAAKM,EAAKI,GAAOL,EAAM,GAAK,GAAM,GAAM,EAYhDM,MAVvB,CAACN,EAAKtB,EAAIiB,EAAIM,EAAII,EAAIE,IAAQ7B,EAAKiB,EAAKM,EAAKI,EAAKE,GAAOP,EAAM,GAAK,GAAM,GAAM,EAUlDQ,MAX9B,CAAC7B,EAAIiB,EAAIE,EAAIK,EAAIM,KAAQ9B,IAAO,IAAMiB,IAAO,IAAME,IAAO,IAAMK,IAAO,IAAMM,IAAO,IC1ClG,MAAOC,GAAWC,IAA6B,KAAOC,GAAIjO,MAAM,CAC5D,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,qBAClE,qBAAsB,qBAAsB,qBAAsB,sBACpEzT,KAAIvR,GAAK6E,OAAO7E,MArB6B,GAuBzCkzB,GAA6B,IAAIrxB,YAAY,IAC7CsxB,GAA6B,IAAItxB,YAAY,IAC5C,MAAMuxB,WAAentB,EACxB,WAAA3C,GACIG,MAAM,IAAK,GAAI,IAAI,GAKnBnB,KAAKyuB,GAAK,WACVzuB,KAAK0uB,IAAK,UACV1uB,KAAK0vB,IAAK,WACV1vB,KAAK2vB,IAAK,WACV3vB,KAAKgwB,GAAK,WACVhwB,KAAK6vB,IAAK,SACV7vB,KAAKowB,IAAK,WACVpwB,KAAKkwB,GAAK,WACVlwB,KAAKswB,GAAK,WACVtwB,KAAKwwB,IAAK,WACVxwB,KAAK+wB,IAAK,WACV/wB,KAAKgxB,GAAK,UACVhxB,KAAKixB,GAAK,UACVjxB,KAAKkxB,IAAK,SACVlxB,KAAKmxB,GAAK,WACVnxB,KAAKoxB,GAAK,SACb,CAED,GAAAnuB,GACI,MAAMwrB,GAAEA,EAAEC,GAAEA,EAAEgB,GAAEA,EAAEC,GAAEA,EAAEK,GAAEA,EAAEH,GAAEA,EAAEO,GAAEA,EAAEF,GAAEA,EAAEI,GAAEA,EAAEE,GAAEA,EAAEO,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,GAAOpxB,KAC3E,MAAO,CAACyuB,EAAIC,EAAIgB,EAAIC,EAAIK,EAAIH,EAAIO,EAAIF,EAAII,EAAIE,EAAIO,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EACvE,CAED,GAAA1vB,CAAI+sB,EAAIC,EAAIgB,EAAIC,EAAIK,EAAIH,EAAIO,EAAIF,EAAII,EAAIE,EAAIO,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,GAC5DpxB,KAAKyuB,GAAU,EAALA,EACVzuB,KAAK0uB,GAAU,EAALA,EACV1uB,KAAK0vB,GAAU,EAALA,EACV1vB,KAAK2vB,GAAU,EAALA,EACV3vB,KAAKgwB,GAAU,EAALA,EACVhwB,KAAK6vB,GAAU,EAALA,EACV7vB,KAAKowB,GAAU,EAALA,EACVpwB,KAAKkwB,GAAU,EAALA,EACVlwB,KAAKswB,GAAU,EAALA,EACVtwB,KAAKwwB,GAAU,EAALA,EACVxwB,KAAK+wB,GAAU,EAALA,EACV/wB,KAAKgxB,GAAU,EAALA,EACVhxB,KAAKixB,GAAU,EAALA,EACVjxB,KAAKkxB,GAAU,EAALA,EACVlxB,KAAKmxB,GAAU,EAALA,EACVnxB,KAAKoxB,GAAU,EAALA,CACb,CACD,OAAAxvB,CAAQP,EAAM+C,GAEV,IAAK,IAAIjC,EAAI,EAAGA,EAAI,GAAIA,IAAKiC,GAAU,EACnCwsB,GAAWzuB,GAAKd,EAAKgD,UAAUD,GAC/BysB,GAAW1uB,GAAKd,EAAKgD,UAAWD,GAAU,GAE9C,IAAK,IAAIjC,EAAI,GAAIA,EAAI,GAAIA,IAAK,CAE1B,MAAMkvB,EAA4B,EAArBT,GAAWzuB,EAAI,IACtBmvB,EAA4B,EAArBT,GAAW1uB,EAAI,IACtBovB,EAAMZ,GAAI5B,OAAOsC,EAAMC,EAAM,GAAKX,GAAI5B,OAAOsC,EAAMC,EAAM,GAAKX,GAAI/B,MAAMyC,EAAMC,EAAM,GACpFE,EAAMb,GAAI3B,OAAOqC,EAAMC,EAAM,GAAKX,GAAI3B,OAAOqC,EAAMC,EAAM,GAAKX,GAAI7B,MAAMuC,EAAMC,EAAM,GAEpFG,EAA0B,EAApBb,GAAWzuB,EAAI,GACrBuvB,EAA0B,EAApBb,GAAW1uB,EAAI,GACrBwvB,EAAMhB,GAAI5B,OAAO0C,EAAKC,EAAK,IAAMf,GAAI1B,OAAOwC,EAAKC,EAAK,IAAMf,GAAI/B,MAAM6C,EAAKC,EAAK,GAChFE,EAAMjB,GAAI3B,OAAOyC,EAAKC,EAAK,IAAMf,GAAIzB,OAAOuC,EAAKC,EAAK,IAAMf,GAAI7B,MAAM2C,EAAKC,EAAK,GAEhFG,EAAOlB,GAAIV,MAAMuB,EAAKI,EAAKf,GAAW1uB,EAAI,GAAI0uB,GAAW1uB,EAAI,KAC7D2vB,EAAOnB,GAAIR,MAAM0B,EAAMN,EAAKI,EAAKf,GAAWzuB,EAAI,GAAIyuB,GAAWzuB,EAAI,KACzEyuB,GAAWzuB,GAAY,EAAP2vB,EAChBjB,GAAW1uB,GAAY,EAAP0vB,CACnB,CACD,IAAIpD,GAAEA,EAAEC,GAAEA,EAAEgB,GAAEA,EAAEC,GAAEA,EAAEK,GAAEA,EAAEH,GAAEA,EAAEO,GAAEA,EAAEF,GAAEA,EAAEI,GAAEA,EAAEE,GAAEA,EAAEO,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,GAAOpxB,KAEzE,IAAK,IAAImC,EAAI,EAAGA,EAAI,GAAIA,IAAK,CAEzB,MAAM4vB,EAAUpB,GAAI5B,OAAOuB,EAAIE,EAAI,IAAMG,GAAI5B,OAAOuB,EAAIE,EAAI,IAAMG,GAAI1B,OAAOqB,EAAIE,EAAI,IAC/EwB,EAAUrB,GAAI3B,OAAOsB,EAAIE,EAAI,IAAMG,GAAI3B,OAAOsB,EAAIE,EAAI,IAAMG,GAAIzB,OAAOoB,EAAIE,EAAI,IAE/EyB,EAAQ3B,EAAKS,GAAQT,EAAKW,EAC1BiB,EAAQ1B,EAAKQ,GAAQR,EAAKU,EAG1BiB,EAAOxB,GAAIJ,MAAMa,EAAIY,EAASE,EAAMxB,GAAUvuB,GAAI0uB,GAAW1uB,IAC7DiwB,EAAMzB,GAAIN,MAAM8B,EAAMhB,EAAIY,EAASE,EAAMxB,GAAUtuB,GAAIyuB,GAAWzuB,IAClEkwB,EAAa,EAAPF,EAENG,EAAU3B,GAAI5B,OAAON,EAAIC,EAAI,IAAMiC,GAAI1B,OAAOR,EAAIC,EAAI,IAAMiC,GAAI1B,OAAOR,EAAIC,EAAI,IAC/E6D,EAAU5B,GAAI3B,OAAOP,EAAIC,EAAI,IAAMiC,GAAIzB,OAAOT,EAAIC,EAAI,IAAMiC,GAAIzB,OAAOT,EAAIC,EAAI,IAC/E8D,EAAQ/D,EAAKiB,EAAOjB,EAAKuB,EAAON,EAAKM,EACrCyC,EAAQ/D,EAAKiB,EAAOjB,EAAKmB,EAAOF,EAAKE,EAC3CsB,EAAU,EAALF,EACLG,EAAU,EAALF,EACLD,EAAU,EAALF,EACLG,EAAU,EAALF,EACLD,EAAU,EAALT,EACLU,EAAU,EAALR,IACF7tB,EAAG2tB,EAAI1tB,EAAG4tB,GAAOG,GAAIrjB,IAAS,EAAL8iB,EAAa,EAALF,EAAc,EAANkC,EAAe,EAANC,IACrDjC,EAAU,EAALJ,EACLE,EAAU,EAALL,EACLG,EAAU,EAALN,EACLG,EAAU,EAALF,EACLD,EAAU,EAALjB,EACLkB,EAAU,EAALjB,EACL,MAAMgE,EAAM/B,GAAIf,MAAMyC,EAAKE,EAASE,GACpChE,EAAKkC,GAAIb,MAAM4C,EAAKN,EAAKE,EAASE,GAClC9D,EAAW,EAANgE,CACR,GAEE/vB,EAAG8rB,EAAI7rB,EAAG8rB,GAAOiC,GAAIrjB,IAAc,EAAVtN,KAAKyuB,GAAkB,EAAVzuB,KAAK0uB,GAAa,EAALD,EAAa,EAALC,MAC3D/rB,EAAG+sB,EAAI9sB,EAAG+sB,GAAOgB,GAAIrjB,IAAc,EAAVtN,KAAK0vB,GAAkB,EAAV1vB,KAAK2vB,GAAa,EAALD,EAAa,EAALC,MAC3DhtB,EAAGqtB,EAAIptB,EAAGitB,GAAOc,GAAIrjB,IAAc,EAAVtN,KAAKgwB,GAAkB,EAAVhwB,KAAK6vB,GAAa,EAALG,EAAa,EAALH,MAC3DltB,EAAGytB,EAAIxtB,EAAGstB,GAAOS,GAAIrjB,IAAc,EAAVtN,KAAKowB,GAAkB,EAAVpwB,KAAKkwB,GAAa,EAALE,EAAa,EAALF,MAC3DvtB,EAAG2tB,EAAI1tB,EAAG4tB,GAAOG,GAAIrjB,IAAc,EAAVtN,KAAKswB,GAAkB,EAAVtwB,KAAKwwB,GAAa,EAALF,EAAa,EAALE,MAC3D7tB,EAAGouB,EAAInuB,EAAGouB,GAAOL,GAAIrjB,IAAc,EAAVtN,KAAK+wB,GAAkB,EAAV/wB,KAAKgxB,GAAa,EAALD,EAAa,EAALC,MAC3DruB,EAAGsuB,EAAIruB,EAAGsuB,GAAOP,GAAIrjB,IAAc,EAAVtN,KAAKixB,GAAkB,EAAVjxB,KAAKkxB,GAAa,EAALD,EAAa,EAALC,MAC3DvuB,EAAGwuB,EAAIvuB,EAAGwuB,GAAOT,GAAIrjB,IAAc,EAAVtN,KAAKmxB,GAAkB,EAAVnxB,KAAKoxB,GAAa,EAALD,EAAa,EAALC,IAC9DpxB,KAAK0B,IAAI+sB,EAAIC,EAAIgB,EAAIC,EAAIK,EAAIH,EAAIO,EAAIF,EAAII,EAAIE,EAAIO,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EACxE,CACD,UAAAtvB,GACI8uB,GAAW1uB,KAAK,GAChB2uB,GAAW3uB,KAAK,EACnB,CACD,OAAAkB,GACIpD,KAAKf,OAAOiD,KAAK,GACjBlC,KAAK0B,IAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EACzD,EAuEE,MAAMixB,GAAyBzyB,GAAgB,IAAM,IAAI4wB,KC9N1D8B,GAAsB,IAAI30B,WAAW,CAAC,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,IACzF40B,GAAqB50B,WAAWkH,KAAK,CAAEjH,OAAQ,KAAM,CAACkH,EAAGjD,IAAMA,IAErE,IAAI2wB,GAAO,CAACD,IACRE,GAAO,CAFgBF,GAAG5jB,KAAK9M,IAAO,EAAIA,EAAI,GAAK,MAGvD,IAAK,IAAIA,EAAI,EAAGA,EAAI,EAAGA,IACnB,IAAK,IAAI2D,IAAK,CAACgtB,GAAMC,IACjBjtB,EAAEmC,KAAKnC,EAAE3D,GAAG8M,KAAKtH,GAAMirB,GAAIjrB,MACnC,MAAMqrB,GAAyB,CAC3B,CAAC,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,GACtD,CAAC,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,GACtD,CAAC,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,GACtD,CAAC,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,GACtD,CAAC,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,IACxD/jB,KAAK9M,GAAM,IAAIlE,WAAWkE,KACtB8wB,GAA0BH,GAAK7jB,KAAI,CAAC+d,EAAK7qB,IAAM6qB,EAAI/d,KAAKnJ,GAAMktB,GAAO7wB,GAAG2D,OACxEotB,GAA0BH,GAAK9jB,KAAI,CAAC+d,EAAK7qB,IAAM6qB,EAAI/d,KAAKnJ,GAAMktB,GAAO7wB,GAAG2D,OACxEqtB,GAAqB,IAAI5zB,YAAY,CACvC,EAAY,WAAY,WAAY,WAAY,aAE9C6zB,GAAqB,IAAI7zB,YAAY,CACvC,WAAY,WAAY,WAAY,WAAY,IAG9C8zB,GAAO,CAACh0B,EAAMC,IAAWD,GAAQC,EAAUD,IAAU,GAAKC,EAEhE,SAASyN,GAAEumB,EAAO/oB,EAAG8G,EAAG+E,GACpB,OAAc,IAAVkd,EACO/oB,EAAI8G,EAAI+E,EACA,IAAVkd,EACG/oB,EAAI8G,GAAO9G,EAAI6L,EACR,IAAVkd,GACG/oB,GAAK8G,GAAK+E,EACH,IAAVkd,EACG/oB,EAAI6L,EAAM/E,GAAK+E,EAEhB7L,GAAK8G,GAAK+E,EACzB,CAEA,MAAMmd,GAAsB,IAAIh0B,YAAY,IACrC,MAAMi0B,WAAkB7vB,EAC3B,WAAA3C,GACIG,MAAM,GAAI,GAAI,GAAG,GACjBnB,KAAKyzB,GAAK,WACVzzB,KAAK0zB,IAAK,UACV1zB,KAAK2zB,IAAK,WACV3zB,KAAK4zB,GAAK,UACV5zB,KAAK6zB,IAAK,UACb,CACD,GAAA5wB,GACI,MAAMwwB,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,GAAO7zB,KAC/B,MAAO,CAACyzB,EAAIC,EAAIC,EAAIC,EAAIC,EAC3B,CACD,GAAAnyB,CAAI+xB,EAAIC,EAAIC,EAAIC,EAAIC,GAChB7zB,KAAKyzB,GAAU,EAALA,EACVzzB,KAAK0zB,GAAU,EAALA,EACV1zB,KAAK2zB,GAAU,EAALA,EACV3zB,KAAK4zB,GAAU,EAALA,EACV5zB,KAAK6zB,GAAU,EAALA,CACb,CACD,OAAAjyB,CAAQP,EAAM+C,GACV,IAAK,IAAIjC,EAAI,EAAGA,EAAI,GAAIA,IAAKiC,GAAU,EACnCmvB,GAAIpxB,GAAKd,EAAKgD,UAAUD,GAAQ,GAEpC,IAAI0vB,EAAe,EAAV9zB,KAAKyzB,GAAQM,EAAKD,EAAIE,EAAe,EAAVh0B,KAAK0zB,GAAQO,EAAKD,EAAIE,EAAe,EAAVl0B,KAAK2zB,GAAQQ,EAAKD,EAAIE,EAAe,EAAVp0B,KAAK4zB,GAAQS,EAAKD,EAAIE,EAAe,EAAVt0B,KAAK6zB,GAAQU,EAAKD,EAGvI,IAAK,IAAIhB,EAAQ,EAAGA,EAAQ,EAAGA,IAAS,CACpC,MAAMkB,EAAS,EAAIlB,EACbmB,EAAMtB,GAAGG,GAAQoB,EAAMtB,GAAGE,GAC1BpjB,EAAK4iB,GAAKQ,GAAQqB,EAAK5B,GAAKO,GAC5BtrB,EAAKirB,GAAQK,GAAQsB,EAAK1B,GAAQI,GACxC,IAAK,IAAInxB,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MAAM0yB,EAAMxB,GAAKS,EAAK/mB,GAAEumB,EAAOU,EAAIE,EAAIE,GAAMb,GAAIrjB,EAAG/N,IAAMsyB,EAAKzsB,EAAG7F,IAAMmyB,EAAM,EAC9ER,EAAKQ,EAAIA,EAAKF,EAAIA,EAAoB,EAAff,GAAKa,EAAI,IAASA,EAAKF,EAAIA,EAAKa,CAC1D,CAED,IAAK,IAAI1yB,EAAI,EAAGA,EAAI,GAAIA,IAAK,CACzB,MAAM2yB,EAAMzB,GAAKU,EAAKhnB,GAAEynB,EAAQP,EAAIE,EAAIE,GAAMd,GAAIoB,EAAGxyB,IAAMuyB,EAAKE,EAAGzyB,IAAMoyB,EAAM,EAC/ER,EAAKQ,EAAIA,EAAKF,EAAIA,EAAoB,EAAfhB,GAAKc,EAAI,IAASA,EAAKF,EAAIA,EAAKa,CAC1D,CACJ,CAED90B,KAAK0B,IAAK1B,KAAK0zB,GAAKQ,EAAKG,EAAM,EAAIr0B,KAAK2zB,GAAKS,EAAKG,EAAM,EAAIv0B,KAAK4zB,GAAKU,EAAKP,EAAM,EAAI/zB,KAAK6zB,GAAKC,EAAKG,EAAM,EAAIj0B,KAAKyzB,GAAKO,EAAKG,EAAM,EACtI,CACD,UAAAryB,GACIyxB,GAAIrxB,KAAK,EACZ,CACD,OAAAkB,GACIpD,KAAKzB,WAAY,EACjByB,KAAKf,OAAOiD,KAAK,GACjBlC,KAAK0B,IAAI,EAAG,EAAG,EAAG,EAAG,EACxB,EAME,MAAMqzB,GAA4B70B,GAAgB,IAAM,IAAIszB,yiBC5EnE,SAAAnzB,GAKgB,MAAAtC,EAAAirB,GAAAlrB,MAAAuC,GAMA,OAAA2oB,GAAAK,IAAA2L,EAAAj3B,GAMhB,SAKA,SAAAsC,0DC9CgB,SAAA6pB,GAAAjD,GAIhB,OAAA+B,GAAAkB,OAAAjD,EAcA,gqCCIgB,SAAAgO,GAQA,MAAAzT,EAAAwH,GAAAlrB,MAAAm3B,GAQhB,QAAA,GAaA,KAA4B,KAA5BzT,EAAAtjB,OAqBgB,KAAA,KAAAsjB,EAAAtjB,YAAWsjB,EACnB,GASR,OAAA,EAQA,UAAAA,EAAAtjB,QAA0C,IAAdsjB,EAAA,GAOZ,OAAA,6pBCkBhB,MAAA0T,GAAA,gBAA6BC,GACd,wBAWf,SAAAC,GAAAC,EAA6BC,EACtBC,EACLC,GAAc,GAeAC,GAAAJ,EAAAE,GAUhB,MAAA1jB,EAAAmX,GAAAlrB,+2GC1ICssB,MAAA,EAEesL,KAAA,2xBAoCA,SAAAC,EACdl2B,EAAMm2B,EAASC,GAKjB,MAAAC,KAAAA,EAAA1L,MAAAA,EAAAsL,WAA8CE,OAO9CG,EAAAC,MAkCAxU,EAAAyU,GAA4BN,GAAA,GACrBnwB,IAaS0wB,EAAAlN,GAAAtpB,IAAAq2B,GAAAx1B,OAWA41B,EAAA,CAAA,EAAA3U,EAAA4I,EAAA0L,EAAeJ,EAAOK,GAOtBK,EAAApN,GAAAS,KAAA0M,GAAA51B,iqBCxHhB,MAAgB81B,GAAA/W,GAAApH,gBAQAoe,GAAAD,GASA,SAAAE,GAAAhpB,GAiBhB,OAXgB,IAAA8oB,GAAA9oB,EAAAhD,EACbgD,EAAG8D,MAUNqC,UAeA,CAeA,SAAA8iB,GAAkBvlB,GAalB,MAAA1D,EAAA0D,EAcA,uBAAA1D,UACWA,GASK,iBAAAA,EAAAhD,GAET,iBADQgD,EACb8D,CAQF", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 18, 19, 20]}