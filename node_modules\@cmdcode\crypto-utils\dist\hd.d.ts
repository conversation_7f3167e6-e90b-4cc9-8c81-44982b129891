import { Buff, Bytes } from '@cmdcode/buff-utils';
import { <PERSON><PERSON><PERSON>, ExtKey } from './types.js';
type Tweak = [tweak: Buff, is_hardened: boolean];
export declare function derive(path: string, input_key: Bytes, chain_code?: Bytes, is_private?: boolean): HDKey;
export declare function parse_tweaks(keypath: string): Tweak[];
export declare function generate_code(chain: Bytes, data: Bytes): Buff[];
export declare function encode_extkey(hdkey: HDKey, key_prefix?: number): string;
export declare function parse_extkey(keystr: string, path?: string): HDKey;
export declare function decode_extkey(keystr: string): ExtKey;
export {};
//# sourceMappingURL=hd.d.ts.map