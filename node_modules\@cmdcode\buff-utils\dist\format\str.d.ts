import { type Endian } from '../types.js';
export declare function strToBytes(str: string): Uint8Array;
export declare function bytesToStr(bytes: Uint8Array): string;
export declare function hexToBytes(hexstr: string, size?: number, endian?: Endian): Uint8Array;
export declare function bytesToHex(bytes: Uint8Array): string;
export declare const Hex: {
    encode: typeof bytesToHex;
    decode: typeof hexToBytes;
};
export declare const Txt: {
    encode: typeof strToBytes;
    decode: typeof bytesToStr;
};
//# sourceMappingURL=str.d.ts.map