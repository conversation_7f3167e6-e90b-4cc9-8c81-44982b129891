import { Endian, Bytes } from './types.js';
export declare function random(size?: number): Uint8Array;
export declare function is_hex(input: string): boolean;
export declare function is_bytes(input: any): input is Bytes;
export declare function set_buffer(data: number[] | Uint8Array, size?: number, endian?: Endian): Uint8Array;
export declare function join_array(arr: Array<Uint8Array | number[]>): Uint8Array;
export declare function bigint_replacer(_: any, v: any): any;
export declare function bigint_reviver(_: any, v: any): any;
export declare function parse_data(data_blob: Uint8Array, chunk_size: number, total_size: number): Uint8Array[];
//# sourceMappingURL=utils.d.ts.map