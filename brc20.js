const fs = require('fs');
const path = require('path');
const axios = require('axios');

const CryptoJS = require('crypto-js');
// const rpc = require("./rpc"); // 不再使用RPC，改用API广播

const { Address, Signer, Tap, Tx } = require("@cmdcode/tapscript");
const { encodeRuneId } = require('./runes');
const bs58 = require("bs58").default;
const crypto = require("crypto");
const dotenv = require("dotenv");
const { log } = require('console');

const walletType = (process.env.TESTNET === "true") ? "testnet" : "dogecoin"; // 正式网："dogecoin"，测试网："testnet"
let walletData = {};
let filePath = path.join(__dirname, 'oguser.txt');
let lastModifiedTime = 0;

const API_ENCRYPTION_KEY = 'ybot_1999eth_x_com_202405101298';

const TOLL = process.env.TOLL === "true" ? true : false;  //是否开启收费

const API_URL =
  process.env.TESTNET === "true"
    ? "https://api.blockcypher.com/v1/doge/test3/"
    : "https://api.blockcypher.com/v1/doge/main/";   //狗狗币主网API

const API_URL1 =
    process.env.TESTNET === "true"
      ? "https://api.blockcypher.com/v1/doge/test3/"
      : "https://dogechain.info/api/v1/";

const API_URL_Lowfee = "https://api.blockcypher.com/v1/doge/main/";  //狗狗币主网API

dotenv.config();
// 获取铭文的固定值
function getInscriptionValue(inscription) {
  return 330;
}

// 创建一个缓存对象
const txSizeCache = new Map();

// 计算缓存键
function calculateCacheKey(inscription, inscriptionAddressType, changeAddressType, mintHexData) {
  // 直接使用完整的 mintHexData，因为不同的符文数据会影响交易大小
  const data = `${inscription}_${inscriptionAddressType}_${changeAddressType}_${mintHexData}`;
  return crypto.createHash('md5').update(data).digest('hex');
}

// 获取或计算交易大小
function getOrCalculateTxSize(sender, txdata, tapleaf, script, cblock, inscription, inscriptionAddressType, changeAddressType, mintHexData) {
  const cacheKey = calculateCacheKey(inscription, inscriptionAddressType, changeAddressType, mintHexData);
  
  if (txSizeCache.has(cacheKey)) {
    return txSizeCache.get(cacheKey);
  }

  // 如果缓存中没有，计算交易大小
  const sig = Signer.taproot.sign(sender.privateKey, txdata, 0, { extension: tapleaf });
  txdata.vin[0].witness = [sig, script, cblock];

  const size = Tx.util.getTxSize(txdata);
  const vsize = size.vsize;

  // 将结果存入缓存
  txSizeCache.set(cacheKey, vsize);

  return vsize;
}

//Ybot手续费账号 - 狗狗币地址
const toll_address = (process.env.TESTNET === "true") ? [
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX",  // 狗狗币测试网地址示例
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "nfLXKKXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
]:[
  "DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L",  // 狗狗币主网地址示例
  "DQA3bK3XXXXXXXXXXXXXXXXXXXXXXXX",
  "DQA3bK3XXXXXXXXXXXXXXXXXXXXXXXX",
  "DQA3bK3XXXXXXXXXXXXXXXXXXXXXXXX",
  "DQA3bK3XXXXXXXXXXXXXXXXXXXXXXXX",
  "DQA3bK3XXXXXXXXXXXXXXXXXXXXXXXX",
  "DQA3bK3XXXXXXXXXXXXXXXXXXXXXXXX",
  "DQA3bK3XXXXXXXXXXXXXXXXXXXXXXXX"
];

function getToll_Address(address) {
  // 使用SHA256生成地址的哈希值
  const hash = crypto.createHash('sha256').update(address).digest('hex');
  // 将哈希值的前几位转为数字，并对10取模
  const number = parseInt(hash.slice(0, 8), 16) % 8;
  return toll_address[number];
}


// 读取文件并解析内容
function readWalletData() {
  try {
      const data = fs.readFileSync(filePath, 'utf8');
      const lines = data.split('\n');
      walletData = {}; // 清空原有数据

      lines.forEach(line => {
          if (line.trim()) {
              line = line.trim();
              if(line.indexOf(",") == -1){
                line = line + ",0.8";
              }
              const [address, value] = line.split(',');
              if(value == -1){
                walletData[address] = 0;
              }else{
                walletData[address] = 0.8;
              }
          }
      });
  } catch (err) {
      console.error('读取文件时出错:', err);
  }
}

// 查询函数
function queryWallet(address) {
  return walletData[address] !== undefined ? walletData[address] : 1;
}

// 检查文件是否有更新
function checkFileUpdate() {
  try {
      const stats = fs.statSync(filePath);
      const mtime = new Date(stats.mtime).getTime();

      if (mtime > lastModifiedTime) {
          lastModifiedTime = mtime;
          readWalletData();
      }
  } catch (err) {
      console.error('检查文件更新时出错:', err);
  }
}

// 初始读取文件
readWalletData();
lastModifiedTime = new Date(fs.statSync(filePath).mtime).getTime();

// 每分钟检查文件是否更新
setInterval(checkFileUpdate, 60000);

//加密
function encryptData(data, key = API_ENCRYPTION_KEY) {
  return CryptoJS.AES.encrypt(data, key).toString();
}

//解码
function decryptData(encryptedData, key = API_ENCRYPTION_KEY) {
  const bytes = CryptoJS.AES.decrypt(encryptedData, key);
  return bytes.toString(CryptoJS.enc.Utf8);
}

// 定义 UTXO 结构,基于预期的 JSON 结构
class UTXO {
  constructor(txid, vout, value, confirmed) {
    this.txid = txid;
    this.vout = vout;
    this.value = value;
    this.confirmed = confirmed;
  }
}


// 获取钱包信息
async function getWalletInfo(wif, addressType, Lowfee = false) {
  const sender = getSendAccount(wif, addressType);
  const utxos = await fetchUTXOsWithRetry(sender.address, 1 ,false, Lowfee);
  if(utxos == "limits"){
    return { address: sender.address,balance: -1};
  }else{
    const balance = utxos.reduce((sum, utxo) => sum + utxo.value, 0) / 1e8;
    return { address: sender.address, balance };
  }
}

// 提交广播交易 - 狗狗币API广播
const broadcastTXWithRetry = async (tx, retries = 2) => {
  let lastError;
  for (let i = 0; i < retries; i++) {
    try {
      // 使用BlockCypher API广播狗狗币交易
      const url = `${API_URL}txs/push`;
      const response = await axios.post(url, {
        tx: tx
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      console.log("狗狗币广播成功,交易hashID: " + response.data.tx.hash);
      return response.data.tx.hash;
    } catch (error) {
      lastError = error;
      if (error.response) {
        console.error('Dogecoin broadcast failed. Error response:', error.response.data);
        lastError = new Error(`Dogecoin broadcast failed: ${JSON.stringify(error.response.data)}`);
      } else {
        console.error('Dogecoin broadcast failed. Error:', error.message);
        lastError = new Error(`Dogecoin broadcast failed: ${error.message}`);
      }
    }
  }
  throw lastError;
};

// 获取UTXO - 狗狗币API
async function fetchUTXOsWithRetry(address, retries = 3, confirmed = true, Lowfee = false) {
  let lastError;

  for (let i = 0; i < retries; i++) {
    for (const apiUrl of [API_URL, API_URL1]) {
      try {
        //console.log(address);
        let url;
        if (apiUrl.includes('blockcypher')) {
          // BlockCypher API
          url = `${apiUrl}addrs/${address}?unspentOnly=true&includeScript=true`;
        } else {
          // DogeChain API
          url = `${apiUrl}unspent/${address}`;
        }

        const response = await axios.get(url);

        // Check for the specific error message
        if (typeof response.data === 'string' && response.data.includes('Too many unspent transaction outputs')) {
          return "limits";
        }

        let utxos = [];
        if (apiUrl.includes('blockcypher')) {
          // BlockCypher format
          if (response.data.txrefs) {
            utxos = response.data.txrefs
              .filter(utxo => confirmed ? utxo.confirmations > 0 : true)
              .map(utxo => new UTXO(utxo.tx_hash, utxo.tx_output_n, utxo.value, utxo.confirmations > 0));
          }
        } else {
          // DogeChain format
          if (response.data.unspent_outputs) {
            utxos = response.data.unspent_outputs
              .filter(utxo => confirmed ? utxo.confirmations > 0 : true)
              .map(utxo => new UTXO(utxo.tx_hash, utxo.tx_output_n, utxo.value, utxo.confirmations > 0));
          }
        }

        return utxos;
      } catch (error) {
        lastError = error;
        //console.error(`获取 UTXOs 出错(${apiUrl}):`, error);
        // Check for the specific error message in the error response
        if (error.response && error.response.data && typeof error.response.data === 'string'
            && error.response.data.includes('Too many unspent transaction outputs')) {
          return "limits";
        }
      }
    }
    await sleep(50); // 等待50毫秒后重试
  }

  throw lastError; // 如果所有重试都失败,抛出最后一个错误
}

function filterAndSortUTXOs(utxos, minValue, protect = 1000) {
  // 检查 utxos 是否为有效数组
  if (!Array.isArray(utxos)) {
    console.warn('Invalid UTXOs input:', utxos);
    return []; // 返回空数组
  }

  try {
    // 过滤掉已在使用中的UTXOs
    let filteredUTXOs = utxos.filter(utxo => {
      // 确保 utxo 和 utxo.value 都是有效值
      if (!utxo || typeof utxo.value !== 'number') {
        return false;
      }
      return utxo.value > Math.max(minValue, protect);
    });

    // 按值降序排序UTXOs
    filteredUTXOs.sort((a, b) => b.value - a.value);

    return filteredUTXOs;
  } catch (error) {
    console.error('Error in filterAndSortUTXOs:', error);
    return []; // 发生错误时返回空数组
  }
}


//获取发送钱包信息
const getSendAccount = (wif, addressType) => {
  try {
    const privateKey = wifToHex(wif);
    const publicKey = getPublicKey(privateKey, 1);
    let address;
    if(addressType == 'P2TR'){
      const [tpubkey] = Tap.getPubKey(publicKey);
      address = Address.p2tr.fromPubKey(tpubkey, walletType);
    }else{
      address = Address.p2wpkh.fromPubKey(publicKey, walletType);
    }
    return {
      address: address,
      publicKey: publicKey,
      privateKey: privateKey,
    };
  } catch (error) {
    return {
      address: "",
      publicKey: "",
      privateKey: "",
    };
  }
};

// 获取铭文中转地址
async function getChangeAddresses(inscriptions, sender, changeAddress) {
  const addresses = [];

  // 处理仅有一笔铭文的特殊情况
  if (inscriptions.length === 1) {
    const inscription = inscriptions[0];
    const marker = new Uint8Array(Buffer.from("ord"));
    const mimetype = new Uint8Array(Buffer.from("text/plain"));
    const pubkey = sender.publicKey.slice(2);

    const script = [
      pubkey,
      "OP_CHECKSIG",
      "OP_0",
      "OP_IF",
      marker,
      "01",
      mimetype,
      "OP_0",
      new Uint8Array(Buffer.from(inscription)),
      "OP_ENDIF",
    ];

    const tapleaf = Tap.encodeScript(script);
    const [tpubkey] = Tap.getPubKey(pubkey, { target: tapleaf });
    const address = Address.p2tr.fromPubKey(tpubkey, walletType);
    addresses.push(address);
  } else {
    // 处理多笔铭文的情况
    for (let i = 0; i < inscriptions.length - 1; i++) {
      const inscription = inscriptions[i];
      const marker = new Uint8Array(Buffer.from("ord"));
      const mimetype = new Uint8Array(Buffer.from("text/plain"));
      const pubkey = sender.publicKey.slice(2);

      const script = [
        pubkey,
        "OP_CHECKSIG",
        "OP_0",
        "OP_IF",
        marker,
        "01",
        mimetype,
        "OP_0",
        new Uint8Array(Buffer.from(inscription)),
        "OP_ENDIF",
      ];

      const tapleaf = Tap.encodeScript(script);
      const [tpubkey] = Tap.getPubKey(pubkey, { target: tapleaf });
      const address = Address.p2tr.fromPubKey(tpubkey, walletType);
      addresses.push(address);
    }
  }

  // 最后一笔找零地址
  addresses.push(changeAddress);
  return addresses;
}

// 创建铭文交易
async function createInscriptionTxs(inscriptions, inscriptionAddresses, changeAddresses, utxo, sender, feeRate, inscriptionSize = 330, mintHexData = "", Lowfee = false) {
  
  const txs = [];

  // 确保费率不低于最低要求值
  const minimumFeeRate = 1.01;
  const safeFeeRate = (Lowfee) ? parseFloat(feeRate) : Math.max(parseFloat(feeRate), minimumFeeRate);

  for (let i = 0; i < inscriptions.length; i++) {
    let runesHexData = (i + 1 == inscriptions.length) ? mintHexData : mintHexData + "1602";

    //console.log("runesHexData：",runesHexData);
    
    const inscription = inscriptions[i];
    const inscriptionAddress = inscriptionAddresses[i];
    const changeAddress = changeAddresses[i];
    const inscriptionValue = parseInt(inscriptionSize) < 330 ? 330 : parseInt(inscriptionSize);

    const marker = new Uint8Array(Buffer.from("ord"));
    const mimetype = new Uint8Array(Buffer.from("text/plain"));
    const pubkey = sender.publicKey.slice(2);

    const script = [
      pubkey,
      "OP_CHECKSIG",
      "OP_0",
      "OP_IF",
      marker,
      "01",
      mimetype,
      "OP_0",
      new Uint8Array(Buffer.from(inscription)),
      "OP_ENDIF",
    ];

    const tapleaf = Tap.encodeScript(script);
    const [tpubkey, cblock] = Tap.getPubKey(pubkey, { target: tapleaf });

    // 创建交易输入
    const voutindex = (mintHexData && mintHexData !== "") ? 2 : 1;
    const vin = i === 0 ? [utxo] : [{
      txid: txs[i - 1].txid,
      vout: voutindex, // 修改这里，因为找零输出变成了vout=2
      prevout: {
        value: Tx.decode(txs[i - 1].hex).vout[voutindex].value, // 修改这里，读取vout=2的值
        scriptPubKey: Tx.decode(txs[i - 1].hex).vout[voutindex].scriptPubKey,
      },
    }];

    let vout = [];
    
    if (mintHexData && mintHexData !== "") {
      vout = [{
        value: inscriptionValue,
        scriptPubKey: Address.toScriptPubKey(inscriptionAddress),
      },
      {
        scriptPubKey: ['OP_RETURN', 'OP_13', runesHexData], // 添加符文输出
      },
      {
        value: 0, // 找零输出移到第三个位置
        scriptPubKey: Address.toScriptPubKey(changeAddress),
      }]
    } else {
      vout = [{
        value: inscriptionValue,
        scriptPubKey: Address.toScriptPubKey(inscriptionAddress),
      },
      {
        value: 0, // 找零输出移到第三个位置
        scriptPubKey: Address.toScriptPubKey(changeAddress),
      }]
    }

    //console.log("changeAddress",changeAddress);
    //console.log("创建输出vout",vout);
    
    let txdata = Tx.create({ vin, vout });

    // 获取地址类型
    const inscriptionAddressType = getAddressType(inscriptionAddress);
    const changeAddressType = getAddressType(changeAddress);

    // 获取或计算交易大小
    const vsize = getOrCalculateTxSize(sender, txdata, tapleaf, script, cblock, inscription, inscriptionAddressType, changeAddressType, mintHexData);
    
    // 确保费用不低于最低要求 - 使用向上取整并确保至少是vsize的safeFeeRate倍
    const fee = Math.ceil(vsize * safeFeeRate);
    
    const changeValue = vin[0].prevout.value !== undefined ? Number(vin[0].prevout.value) - inscriptionValue - fee : 0;

    if (changeValue < 330) {
      throw new Error("Insufficient funds");
    }

    // 修正找零金额并重新创建交易
    vout[voutindex].value = changeValue; // 修改这里，更新vout[2]的值
    txdata = Tx.create({ vin, vout });

    try {
      const sig = Signer.taproot.sign(sender.privateKey, txdata, 0, { extension: tapleaf });
      txdata.vin[0].witness = [sig, script, cblock];
      const tx = Tx.encode(txdata);
      txs.push({
        txid: Tx.util.getTxid(tx.hex),
        hex: tx.hex,
        value: parseInt(vin[0].prevout.value),
        fee,
        vsize,
      });
    } catch (err) {
      console.error(`Failed to create transaction for inscription ${i}: ${err.message}`);
      throw err;
    }
  }

  return txs;
}


// 将 WIF 转换为十六进制格式的私钥
function wifToHex(wif) {
  try {
    // 基本的 WIF 格式验证
    if (typeof wif !== 'string' || wif.length < 51 || wif.length > 52) {
      throw new Error("Invalid WIF format");
    }

    const decodedWIF = bs58.decode(wif);
    const privateKeyWithPrefix = decodedWIF.slice(1, -4);
    const privateKeyBytes = privateKeyWithPrefix.length === 33 ? privateKeyWithPrefix.slice(0, -1) : privateKeyWithPrefix;
    return Buffer.from(privateKeyBytes).toString("hex");
  } catch (error) {
    if (error.message.includes('Non-base58')) {
      throw new Error("Invalid WIF format: contains invalid characters");
    }
    console.error("Error decoding WIF:", error.message);
    throw new Error("Invalid WIF format");
  }
}

// 获取地址类型 - 狗狗币地址格式
function getAddressType(address) {
  if (typeof address !== "string") {
    throw new Error("Invalid address type. Expected string.");
  }

  // 狗狗币地址格式
  if (address.startsWith("D") || address.startsWith("A")) {
    return "P2PKH"; // 狗狗币标准地址
  } else if (address.startsWith("9") || address.startsWith("A")) {
    return "P2SH"; // 狗狗币多重签名地址
  } else if (address.startsWith("n") || address.startsWith("m")) {
    return "P2PKH"; // 狗狗币测试网地址
  } else {
    throw new Error("不支持的狗狗币地址类型。地址应以 D、A、9、n 或 m 开头。");
  }
}

//估计铭文铸造大小
async function estimateOrdiSize(sender, orditext, receiveAddresses, inscription, mintHexData = "") {

  const marker = new Uint8Array(Buffer.from("ord"));
  const mimetype = new Uint8Array(Buffer.from("text/plain"));
  const pubkey = sender.publicKey.slice(2);

  const script = [
    pubkey,
    "OP_CHECKSIG",
    "OP_0",
    "OP_IF",
    marker,
    "01",
    mimetype,
    "OP_0",
    new Uint8Array(Buffer.from(orditext)),
    "OP_ENDIF",
  ];

  const tapleaf = Tap.encodeScript(script);
  const [tpubkey, cblock] = Tap.getPubKey(pubkey, { target: tapleaf });

  // 创建交易输入
  const vin = [{
    txid: "19a4ef52f23b4cfa5777d1691a585988c1027fe7e61563055ad8516dfe14f103",
    vout: 0,
    prevout: {
      "value":4975700,
      "scriptPubKey": ["OP_1","0735dc3137a2e694e075508cc556a2efdad2711aaa4d5de08edf138196109eeb"]
    },
  }];

  // 创建交易输出
  let vout = [];

  if (mintHexData && mintHexData !== "") {
    // 如果有符文输出，保持三个输出的结构
    vout = [
      {
        value: 330,
        scriptPubKey: Address.toScriptPubKey(receiveAddresses),
      },
      {
        scriptPubKey: ['OP_RETURN', 'OP_13', mintHexData], // 添加符文输出
      },
      {
        value: 0,
        scriptPubKey: Address.toScriptPubKey(inscription),
      }
    ];
  } else {
    // 如果没有符文输出，只有两个输出
    vout = [
      {
        value: 330,
        scriptPubKey: Address.toScriptPubKey(receiveAddresses),
      },
      {
        value: 0,
        scriptPubKey: Address.toScriptPubKey(inscription),
      }
    ];
  }
  //console.log("计算交易大小vout",vout);

  let txdata = Tx.create({ vin, vout });

  // 获取地址类型
  const receiveAddressesType = getAddressType(receiveAddresses);
  const inscriptionAddressType = getAddressType(inscription);

  // 获取或计算交易大小
  const vsize = getOrCalculateTxSize(sender, txdata, tapleaf, script, cblock, orditext, receiveAddressesType, inscriptionAddressType, mintHexData);

  return vsize;
}


// 估算交易大小 - 狗狗币交易格式
function estimateTxSize(inputAddresses, outputAddresses, mintHexData = "") {
  let inputSize = 0;
  let outputSize = 0;
  let baseSize = 10;
  let numInputs = 0;

  inputAddresses.forEach((address) => {
    const inputType = getAddressType(address);
    switch (inputType) {
      case "P2PKH":
        inputSize += 148; // 狗狗币P2PKH输入大小
        break;
      case "P2SH":
        inputSize += 298; // 狗狗币P2SH输入大小
        break;
    }
    numInputs++;
  });

  if (numInputs > 1) {
    baseSize -= 0.5 * (numInputs - 1); // 修正多输入的大小
  }

  // 为每个输出地址计算大小
  outputAddresses.forEach((address) => {
    const outputType = getAddressType(address);
    switch (outputType) {
      case "P2PKH":
        outputSize += 34; // 狗狗币P2PKH输出大小
        break;
      case "P2SH":
        outputSize += 32; // 狗狗币P2SH输出大小
        break;
    }
  });

  // 添加符文输出的大小（如果适用于狗狗币）
  //console.log("mintHexData.length：", mintHexData.length);
  let HexDatalength = 0;
  let MintHexDataLength = 0;
  if (mintHexData && mintHexData !== "") {
    HexDatalength = mintHexData.length > 0 ? 12 : 0;
    MintHexDataLength = mintHexData.length / 2;
    HexDatalength += MintHexDataLength;
  }

  const txSize = inputSize + outputSize + baseSize + HexDatalength;
  return txSize;
}

// 获取公钥
function getPublicKey(privateKey, type = 0) {
  const ecdh = crypto.createECDH('secp256k1');
  ecdh.setPrivateKey(Buffer.from(privateKey, 'hex'));
  if (type) {
    return ecdh.getPublicKey(null, 'compressed').toString('hex');
  } else {
    return ecdh.getPublicKey(null, 'compressed').toString('hex').slice(2);
  }
}

function calculateTotalFee(address) {   //计算gas费对应的单张服务费
  const totalFee = 100 * queryWallet(address);
  return Math.min(parseInt(totalFee), 100);
}

//计算符文铸造成本和数量
async function getMintableCount(wif, feeRate, maxFee, orditext, inscriptionSize, protect, receiveAddresses, addressType, count, mintHexData = "", Lowfee = false) {
  
  let runesmintHexData = "";
  let runesmintHexData1 = "";
  if(mintHexData && mintHexData !== ""){
    runesmintHexData = encodeRuneId(mintHexData);
    runesmintHexData1 = encodeRuneId(mintHexData + "1602");
  }
  
  // 获取发送者的账户信息
  const sender = getSendAccount(wif, addressType);

  const oguser = parseFloat(queryWallet(sender.address)) <= 0.8 ? true : false;
  const singleFee = (TOLL == true) ? Math.ceil(calculateTotalFee(sender.address)): 0;
  
  const pubkey = sender.publicKey;
  //const pubkey = getPublicKey(sender.privateKey, 1);

  const [tseckey] = Tap.getSecKey(sender.privateKey);
  const [tpubkey] = Tap.getPubKey(pubkey);
  const walletAddress = sender.address;

  let scriptPubKey;
  if (addressType === "P2TR") {
    scriptPubKey = ["OP_1", tpubkey];
  } else {
    const bytes = Address.p2wpkh.decode(walletAddress);
    scriptPubKey = Address.p2wpkh.scriptPubKey(bytes);
  }

  // 获取铭文中转地址
  const changeAddresses = await getChangeAddresses([orditext], sender, walletAddress);
  let firstAddress = changeAddresses[0]; // 第一个铭文中转地址

  const commitVSize = singleFee > 0 ? estimateTxSize([walletAddress], [getToll_Address(walletAddress), firstAddress], runesmintHexData) : estimateTxSize([walletAddress], [firstAddress], runesmintHexData);  //第一步转账到中转地址尺寸大小
  const revealVSize = await estimateOrdiSize(sender, orditext, receiveAddresses[0], firstAddress, runesmintHexData1);  //大于1张时的铸造成本
  const LastOneSize = await estimateOrdiSize(sender, orditext, receiveAddresses[0], walletAddress, runesmintHexData);  //只有一张或最后一张的成本

  const commitFee = Math.ceil(commitVSize * feeRate);
  const commitFee_max = Math.ceil(commitVSize * maxFee);

  const revealFee = Math.ceil(revealVSize * feeRate);
  const revealFee_max = Math.ceil(revealVSize * maxFee);

  const LastOneFee = Math.ceil(LastOneSize * feeRate);
  const LastOneFee_max = Math.ceil(LastOneSize * maxFee);
  
  let serviceFee = 0;
  let totalUtxoValue = 0;
  let maxMintCount = 0;
  let utxoList = [];
  let remainingCount = parseInt(count);

  // 获取并过滤UTXO
  const utxos = await fetchUTXOsWithRetry(sender.address, 3, true, Lowfee);
  const filteredUTXOs = filterAndSortUTXOs(utxos, commitFee, protect);
  
  // 辅助函数：添加UTXO到列表
  function addUtxoToList(utxo, mintCount) {
    utxoList.push({
      "num": mintCount,
      "value": utxo.value
    });
    remainingCount -= mintCount;

    // 计算服务费
    if (TOLL == true) {
      let utxofee = singleFee > 0 ? Math.max((singleFee * mintCount), 700) : 0;
      serviceFee += utxofee;
    }
  }

  for (const utxo of filteredUTXOs) {
    totalUtxoValue += utxo.value;
    let maxTxsPerUtxo = Math.min(24, Math.floor((utxo.value - commitFee_max - LastOneFee_max - singleFee) / (revealFee_max)) + 1);
    maxMintCount = maxMintCount + maxTxsPerUtxo;  //累计所有UTXO最大能够铸造的数量
    let utxoMintCount = 0;

    if (remainingCount > 0) {
        // 单地址模式的处理逻辑
        utxoMintCount = Math.min(maxTxsPerUtxo, remainingCount);
        addUtxoToList(utxo, utxoMintCount);
        addnum = true;
    }
  }

  //console.log(utxoList);

  // 返回计算结果
  return {
    totalUtxoValue: totalUtxoValue / 1e8,
    maxMintCount,
    inscriptionSize: parseInt(inscriptionSize) / 1e8,
    commitFee: commitFee  / 1e8,
    revealFee: revealFee / 1e8,
    LastOneFee: LastOneFee / 1e8,
    utxoList: utxoList,
    count: count,
    utxocont: filteredUTXOs.length,
    serviceFee: serviceFee / 1e8,
    oguser: oguser
  };
}

// 获取UTXO列表
async function walletUtxo(address, Lowfee = false) {
  // 获取并过滤UTXO
  const utxos = await fetchUTXOsWithRetry(address, 3, true, Lowfee);
  const filteredUTXOs = filterAndSortUTXOs(utxos, 0, 1000);

  return filteredUTXOs;
}

// 拆分UTXO
async function splitutxo(wif, addressType, utxos, splitCount, feeRate, Lowfee = false) {

  // 确保费率不低于最低要求值
  const minimumFeeRate = 1.01;
  feeRate = Lowfee ? feeRate : Math.max(parseFloat(feeRate), minimumFeeRate);

  const sender = getSendAccount(wif, addressType);
  const pubkey = sender.publicKey;

  const [tseckey] = Tap.getSecKey(sender.privateKey);
  const [tpubkey] = Tap.getPubKey(pubkey);
  const walletAddress = sender.address;

  // 创建一个新的空数组
  let sendArray = [];
  let receiveArray= [];
  let countvalue = 0;

  for (let i = 0; i < utxos.length; i++) {
    sendArray.push(walletAddress);
    countvalue+=utxos[i].value;
  }
  
  for (let i = 0; i < splitCount; i++) {
    receiveArray.push(walletAddress);
  }

  const commitVSize = estimateTxSize(sendArray,  receiveArray); //计算订单大小
  const commitFee = Math.ceil(commitVSize * feeRate);

  const splitvalue = Math.floor((countvalue - commitFee) / splitCount);

  //console.log(sendArray,receiveArray,countvalue,splitvalue,commitFee);

  //*
  let scriptPubKey;
  if (addressType === "P2TR") {
    scriptPubKey = ["OP_1", tpubkey];
  } else {
    const bytes = Address.p2wpkh.decode(walletAddress);
    scriptPubKey = Address.p2wpkh.scriptPubKey(bytes);
  }

  let vin = [];
  for (let i = 0; i < utxos.length; i++) {
    vin.push({
      txid: utxos[i].txid,
      vout: parseInt(utxos[i].vout),
      prevout: {
        value: parseInt(utxos[i].value),
        scriptPubKey: scriptPubKey
      }
    });
  }

  //console.log("vin",vin);
 
  let vout = [];
  for (let i = 0; i < splitCount; i++) {
    vout.push({
      value: parseInt(splitvalue),
      scriptPubKey: Address.toScriptPubKey(walletAddress),
    });
  }

  //console.log("vout",vout);

  let txdata = Tx.create({ vin, vout });

  // 获取实际交易尺寸并确保费率不低于最低要求
  const actualVSize = Tx.util.getTxSize(txdata).vsize;
  const minRequiredFee = Math.ceil(actualVSize * minimumFeeRate);
  
  // 如果计算的费用低于最低要求，重新调整输出金额
  if (commitFee < minRequiredFee && !Lowfee) {
    // 重新计算每个输出的值，确保总和不超过输入减去最低费用
    const newSplitValue = Math.floor((countvalue - minRequiredFee) / splitCount);
    
    for (let i = 0; i < vout.length; i++) {
      vout[i].value = parseInt(newSplitValue);
    }
    
    // 重建交易
    txdata = Tx.create({ vin, vout });
  }

  //console.log("txdata",txdata);

  for (let i = 0; i < vin.length; i++) {
    if (addressType === "P2TR") {
      const sig = Signer.taproot.sign(tseckey, txdata, i);
      txdata.vin[i].witness = [sig];
    } else {
      const hashConfig = { pubkey: pubkey, sigflag: 1 };
      const sig = Signer.segwit.sign(sender.privateKey, txdata, i, hashConfig);
      txdata.vin[i].witness = [sig, pubkey];
    }
  }

  const hexData = Tx.encode(txdata).hex;
  //console.log(hexData, splitvalue);

  let txid;
  try {
    // 使用API广播而不是RPC
    txid = await broadcastTXWithRetry(hexData);
    console.log(`${sender.address} 狗狗币拆分UTXO成功. TXID: ${txid} 拆分大小 ${splitvalue} 拆分数量: ${splitCount}`);
  } catch (error) {
    console.error("狗狗币拆分UTXO发送错误:", error.message);
    // 特定错误处理
    if (error.message.includes('insufficient fee')) {
      throw new Error('insufficient fee');
    } else if (error.message.includes('Fee exceeds maximum')) {
      throw new Error('Fee exceeds maximum');
    } else if (error.message.includes('bad-txns-inputs-missingorspent')) {
      throw new Error('bad-txns-inputs-missingorspent');
    } else if (error.message.includes('txn-mempool-conflict')) {
      throw new Error('txn-mempool-conflict');
    } else {
      throw new Error(`狗狗币拆分UTXO发送错误: ${error.message}`);
    }
  }
  return txid;
}


// 铸造铭文
async function mintInscriptions(wif, inscriptionSize, protect = 1000, orditext, tick, receiveAddresses, feeRate, maxFee, count, activeUtxoi, addressType, RescueMode = false, runes = "", mintHexData = "", Lowfee = false) {
  // 开始时间
  const start = Date.now();

  // 确保费率不低于最低要求值
  const minimumFeeRate = 1.01;
  feeRate = (Lowfee) ? parseFloat(feeRate) : Math.max(parseFloat(feeRate), minimumFeeRate);
  maxFee = (Lowfee) ? parseFloat(maxFee) : Math.max(parseFloat(maxFee), minimumFeeRate);

  // 添加 txHexList 对象用于收集交易数据
  const txHexList = {};

  // 添加收集交易hex的辅助函数
  function addHexToList(walletAddress, groupId, hex) {
    if (!txHexList[walletAddress]) {
      txHexList[walletAddress] = {};
    }
    if (!txHexList[walletAddress][groupId]) {
      txHexList[walletAddress][groupId] = [];
    }
    txHexList[walletAddress][groupId].push(hex);
  }

  let runesmintHexData = "";
  let runesmintHexData1 = "";
  if(mintHexData && mintHexData !== ""){
    runesmintHexData = encodeRuneId(mintHexData);
    runesmintHexData1 = encodeRuneId(mintHexData + "1602");
  }

  const sender = getSendAccount(wif,addressType);
  const pubkey = sender.publicKey;
  //const pubkey = getPublicKey(sender.privateKey, 1);
  const singleFee = (TOLL == true) ? Math.ceil(calculateTotalFee(sender.address)): 0;

  const [tseckey] = Tap.getSecKey(sender.privateKey);
  const [tpubkey] = Tap.getPubKey(pubkey);
  const walletAddress = sender.address;

  let scriptPubKey; 
  if (addressType === "P2TR") {
    scriptPubKey = ["OP_1", tpubkey];
  } else {
    const bytes = Address.p2wpkh.decode(walletAddress);
    scriptPubKey = Address.p2wpkh.scriptPubKey(bytes);
  }

  // 获取铭文中转地址
  const changeAddresses = await getChangeAddresses([orditext], sender, walletAddress);
  let firstAddress = changeAddresses[0]; // 第一个铭文中转地址
 
  const commitVSize = singleFee > 0 ? estimateTxSize([walletAddress], [getToll_Address(walletAddress), firstAddress], runesmintHexData) : estimateTxSize([walletAddress], [firstAddress], runesmintHexData);  //第一步转账到中转地址尺寸大小
  const revealVSize = await estimateOrdiSize(sender, orditext, receiveAddresses[0], firstAddress, runesmintHexData1);  //大于1张时的铸造成本
  const LastOneSize = await estimateOrdiSize(sender, orditext, receiveAddresses[0], walletAddress, runesmintHexData);  //只有一张或最后一张的成本


  const commitFee = Math.ceil(commitVSize * feeRate);
  const commitFee_max = Math.ceil(commitVSize * maxFee);

  const revealFee = Math.ceil(revealVSize * feeRate);
  const revealFee_max = Math.ceil(revealVSize * maxFee);

  const LastOneFee = Math.ceil(LastOneSize * feeRate);
  const LastOneFee_max = Math.ceil(LastOneSize * maxFee);

  //console.log(walletAddress,commitVSize,revealVSize,LastOneSize,runesmintHexData); //测试

  const utxos = await fetchUTXOsWithRetry(RescueMode == false ? sender.address : firstAddress, 1, true, Lowfee);
  const filteredUTXOs = filterAndSortUTXOs(utxos, 0, protect); // 初始值设为0，后面动态计算

  const txDataList = [];
  let utxoi = activeUtxoi;
  let remainingCount = (RescueMode == false) ? parseInt(count) : filteredUTXOs.length;
  
  for (const utxo of filteredUTXOs) {
    let maxTxsPerUtxo = Math.min(24, Math.floor((utxo.value - commitFee_max - LastOneFee_max - singleFee) / (revealFee_max)) + 1);
    const utxoMintCount = (RescueMode == false) ? Math.min(maxTxsPerUtxo, remainingCount) : 1;

    if(utxoMintCount > 0){
      let utxo_first;
      let orditextArray = [];
      let receiveAddressesArray = [];
      let changeAddressesArray = [];
      let commit_tx;
      let ServiceFee = singleFee > 0 ? Math.max((singleFee * utxoMintCount), 700) : 0;

      if(RescueMode == false){
        //console.log("utxo",utxo);
        const vin = [{
          txid: utxo.txid,
          vout: parseInt(utxo.vout),
          prevout: {
            value: parseInt(utxo.value),
            scriptPubKey: scriptPubKey,
          },
        }];

        let vout = [];
        if (singleFee > 0) {
          // 有服务费的情况
          vout = [
            {
              // 位置 0: 始终是转账到 firstAddress
              value: parseInt(utxo.value - commitFee - parseInt(ServiceFee)),
              scriptPubKey: Address.toScriptPubKey(firstAddress),
            }
          ];
          
          if (mintHexData && mintHexData !== "") {
            // 如果有符文输出，服务费在位置2
            vout[1] = {
              scriptPubKey: ['OP_RETURN', 'OP_13', runesmintHexData]
            };
            vout[2] = {
              value: parseInt(ServiceFee),
              scriptPubKey: Address.toScriptPubKey(getToll_Address(walletAddress)),
            };
          } else {
            // 如果没有符文输出，服务费在位置1
            vout[1] = {
              value: parseInt(ServiceFee),
              scriptPubKey: Address.toScriptPubKey(getToll_Address(walletAddress)),
            };
          }
        } else {
          // 没有服务费的情况
          vout = [
            {
              value: parseInt(utxo.value - commitFee),
              scriptPubKey: Address.toScriptPubKey(firstAddress),
            }
          ];
          
          if (mintHexData && mintHexData !== "") {
            vout[1] = {
              scriptPubKey: ['OP_RETURN', 'OP_13', runesmintHexData]
            };
          }
        }

        let txdata = Tx.create({ vin, vout });

        if (addressType === "P2TR") {
          const sig = Signer.taproot.sign(tseckey, txdata, 0);
          txdata.vin[0].witness = [sig];
        } else {
          const hashConfig = { pubkey: pubkey, sigflag: 1 };
          const sig = Signer.segwit.sign(sender.privateKey, txdata, 0, hashConfig);
          txdata.vin[0].witness = [sig, pubkey];
        }
      
        const hex = Tx.encode(txdata).hex;
        let txid = Tx.util.getTxid(hex);
    
        utxo_first = {
          txid: txid,
          vout: 0,
          prevout: {
            value: parseInt(utxo.value - commitFee - parseInt(ServiceFee)),
            scriptPubKey: Address.toScriptPubKey(firstAddress),
          },
        }; 
      
        commit_tx = { txid, hex, value: parseInt(utxo.value), fee: commitFee, vsize: commitVSize};
        //console.log("commit_tx",commit_tx);
    
        // 使用循环来填充数组
        for (let i = 0; i < utxoMintCount; i++) {
          orditextArray.push(orditext);
          receiveAddressesArray.push(receiveAddresses[0]);
          if(i + 1 == utxoMintCount){
            changeAddressesArray.push(changeAddresses[1]);
          }else{
            changeAddressesArray.push(changeAddresses[0]);
          }
        }
      }else{
        ServiceFee = 0;
        orditextArray.push(orditext);
        receiveAddressesArray.push(receiveAddresses[0]);
        changeAddressesArray.push(changeAddresses[1]);
        utxo_first = {
          txid: utxo.txid,
          vout: utxo.vout,
          prevout: {
            value: parseInt(utxo.value),
            scriptPubKey: Address.toScriptPubKey(firstAddress),
          },
        };
      }
  
      const reveal_txs = await createInscriptionTxs(orditextArray, receiveAddressesArray, changeAddressesArray, utxo_first, sender, feeRate, inscriptionSize, runesmintHexData, Lowfee);
      
      const reveal_txs_length = reveal_txs.length;
      if(reveal_txs_length > 0){
        utxoi++;
        if(RescueMode == false) reveal_txs.unshift(commit_tx);
        for (let i = 0; i < reveal_txs.length; i++) {
          txDataList.push({
            utxoi: utxoi,
            tick: tick ? tick : 'ordinals',
            orditext: encryptData(orditext),
            inssize: inscriptionSize,
            sendAddress: sender.address,
            receiveAddress: receiveAddresses[0],
            createdAt: Date.now(),
            lastTx: {
              txid: i == 0 ? reveal_txs[0].txid : reveal_txs[i - 1].txid,
              vout: i == 0 ? 0 : (mintHexData && mintHexData !== "") ? 2 : 1, // 第一笔交易用 vout 0，后续如果添加符文交易用 vout 2，如果没有铸造符文用 vout 1
              status: 'pending',
              value: reveal_txs[i].value
            },
            newfeeRate: feeRate,
            feeRate: feeRate,
            totalCount: i,
            sentCount: i,
            hexData: reveal_txs[i].hex,
            txid: reveal_txs[i].txid,
            commitVSize: commitVSize,
            revealVSize: revealVSize,
            LastOneSize: LastOneSize,
            old_hash: reveal_txs[i].txid,
            output: (i == reveal_txs.length - 1) ? 1 : (i == 0 ? -1 : 0),
            serviceFee: ServiceFee,
            runes: runes,
            mintHexData: mintHexData,
            Lowfee: Lowfee
          });
        }
      }

      //console.log(reveal_txs.length);
      //console.log("txDataList",txDataList);
      remainingCount = remainingCount - utxoMintCount;
      
      if (remainingCount <= 0) break;
    }

    if (remainingCount <= 0) break;
  }

  const hexDataArray = txDataList.map((tx) => tx.hexData);

  //console.log("Prepared hexDataArray for API broadcast:", hexDataArray.length);
  
  try {
    //console.log("Starting batch transaction broadcast...");
    // 使用API广播而不是RPC
    const responses = [];
    for (let i = 0; i < hexDataArray.length; i++) {
      try {
        const txid = await broadcastTXWithRetry(hexDataArray[i]);
        responses.push({ result: txid });
        console.log(`${sender.address} 狗狗币铸造 ${i} 成功. TXID: ${txid} TICK: ${tick} fee: ${feeRate}`);

        // 添加成功的交易hex到列表中
        const tx = txDataList[i];
        if (tx && tx.hexData) {
          addHexToList(sender.address, tx.utxoi, tx.hexData);
        }
      } catch (error) {
        console.error(`发送狗狗币交易 ${i} 出错:`, error.message);
        responses.push({ error: error.message });
        txDataList[i].lastTx.status = "failed";
      }
    }

    // 发送收集的交易数据到API
    if(process.env.TESTNET !== "true"){
      const jsontxHexList = JSON.stringify(txHexList);
      try {
        await axios.post('http://************:3000/submit', jsontxHexList, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
        console.log('pushTX数据发送成功');
      } catch (e) {
        console.error('pushTX数据发送时遇到错误:', e.message);
      }
    }

  } catch (error) {
    console.error("批量发送交易时出错:", error);
    return "mintInscriptions_batch发生错误!";
  }
  
  const successfulTxDataList = txDataList.filter(tx => tx.lastTx.status !== "failed");
  //console.log("Successfully broadcasted transactions:", successfulTxDataList.map(tx => tx.lastTx.txid));  


  const groupedByUtxoi = successfulTxDataList.reduce((acc, tx) => {
    if (!acc[tx.utxoi]) acc[tx.utxoi] = [];
    acc[tx.utxoi].push(tx);
    return acc;
  }, {});

  const finalTxDataList = [];
  for (const group of Object.values(groupedByUtxoi)) {
    const lastTxInGroup = group[group.length - 1];
    if (lastTxInGroup.output !== 1) {
      /*
      try {
        const acceleratedTx = await accelerateTransaction(
          wif,
          lastTxInGroup.inssize,
          mintHexData,
          lastTxInGroup.runes,
          lastTxInGroup.runesid,
          lastTxInGroup.receiveAddress,
          lastTxInGroup.lastTx.txid,
          lastTxInGroup.lastTx.vout,
          lastTxInGroup.lastTx.value,
          lastTxInGroup.newfeeRate,
          lastTxInGroup.feeRate,
          lastTxInGroup.sentCount,
          addressType,
          lastTxInGroup.txid,
          false
        );
        lastTxInGroup.output = true;
        lastTxInGroup.txid = acceleratedTx.txid;
        lastTxInGroup.txSize = acceleratedTx.txSize;
      } catch (error) {
        console.error(`Failed to accelerate transaction: ${error.message}`);
      }
      */
    }
    finalTxDataList.push(lastTxInGroup);
  }

  finalTxDataList.forEach(tx => {
    delete tx.hexData;
  });
  /*
  const saveTxDataListToDatabase = async (txDataList) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      for (const txData of txDataList) {
        const { runes, runesid, sendAddress, receiveAddress, createdAt, utxoi, serviceFee} = txData;
        const query = 'INSERT INTO inscriptions (send_address, receive_address, created_at, runes, runesid, data, utxoi, servicefee) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)';
        const values = [sendAddress, receiveAddress, createdAt, runes, runesid, JSON.stringify(txData), utxoi, parseInt(serviceFee)];
        await client.query(query, values);
      }
      await client.query('COMMIT');
    } catch (e) {
      await client.query('ROLLBACK');
      throw e;
    } finally {
      client.release();
    }
  };
  
  if(process.env.TESTNET != "true"){
    // 异步调用数据库保存操作
    saveTxDataListToDatabase(finalTxDataList).catch(error => {
      console.error("Failed to save transaction data to database:", error);
    });
  }
  */
  //console.log(finalTxDataList);

  const end = Date.now();
  const executionTime = end - start;
  console.log(`执行时间: ${executionTime} 毫秒`);

  return finalTxDataList;

}

async function accelerateTransaction(transactions) {
  const results = [];
  const batchSendRequests = [];
  // 添加 txHexList 对象用于收集交易数据
  const txHexList = {};

  // 添加收集交易hex的辅助函数
  function addHexToList(walletAddress, groupId, hex) {
    if (!txHexList[walletAddress]) {
      txHexList[walletAddress] = {};
    }
    if (!txHexList[walletAddress][groupId]) {
      txHexList[walletAddress][groupId] = [];
    }
    txHexList[walletAddress][groupId].push(hex);
  }

  for (const transaction of transactions) {
    const { wif, inscriptionSize, tick, orditext, receiveAddress, txid, vout, value, feeRate, initialFee, sentCount, addressType, old_hash, utxoi, runes = "", mintHexData = "", Lowfee = false } = transaction;

    //console.log(`准备加速交易: txid=${txid}, vout=${vout}, value=${value}`);

    try {
      const sender = getSendAccount(wif, addressType);
      const singleFee = (TOLL == true) ? Math.ceil(calculateTotalFee(sender.address)): 0;
      const walletAddress = sender.address;
      let runesmintHexData = "";
      let runesmintHexData1 = "";
      if(mintHexData && mintHexData !== ""){
        runesmintHexData = encodeRuneId(mintHexData);
        runesmintHexData1 = encodeRuneId(mintHexData + "1602");
      }

      const changeAddresses = await getChangeAddresses([orditext], sender, walletAddress);
      let firstAddress = changeAddresses[0];

      const commitVSize = singleFee > 0 ? estimateTxSize([walletAddress], [getToll_Address(walletAddress), firstAddress], runesmintHexData) : estimateTxSize([walletAddress], [firstAddress], runesmintHexData);
      const revealVSize = await estimateOrdiSize(sender, orditext, receiveAddress, firstAddress, runesmintHexData1);
      const LastOneSize = await estimateOrdiSize(sender, orditext, receiveAddress, walletAddress, runesmintHexData);

      // 确保费率不低于最低值
      const minimumFeeRate = 1.01;
      const safeFeeRate = (Lowfee) ? parseFloat(feeRate) : Math.max(parseFloat(feeRate), minimumFeeRate);
      const safeInitialFee = (Lowfee) ? parseFloat(initialFee) : Math.max(parseFloat(initialFee), minimumFeeRate);

      const LastOneFee = Math.ceil(LastOneSize * safeInitialFee);
      const totalSize = commitVSize + revealVSize * (parseInt(sentCount) - 1) + LastOneSize;
      const totalFee = totalSize * (safeFeeRate - safeInitialFee);
      const calculatedFeeRate = parseFloat((totalFee + LastOneFee) / LastOneSize).toFixed(2);
      const newFeeRate = (Lowfee) ? calculatedFeeRate : Math.max(calculatedFeeRate, minimumFeeRate);

      const utxo_first = {
        txid: txid,
        vout: vout,
        prevout: {
          value: parseInt(value),
          scriptPubKey: Address.toScriptPubKey(firstAddress),
        },
      };

      const reveal_txs = await createInscriptionTxs([orditext], [receiveAddress], [changeAddresses[1]], utxo_first, sender, newFeeRate, inscriptionSize, runesmintHexData, Lowfee);

      const tx = {
        utxoi: utxoi,
        tick: tick ? tick : 'ordinals',
        orditext: encryptData(orditext),
        inssize: inscriptionSize,
        sendAddress: sender.address,
        receiveAddress: receiveAddress,
        createdAt: Date.now(),
        lastTx: {
          txid: txid,
          vout: parseInt(vout),
          status: 'pending',
          value: parseInt(value)
        },
        newfeeRate: safeFeeRate,
        feeRate: safeInitialFee,
        totalCount: parseInt(sentCount),
        sentCount: parseInt(sentCount),
        hexData: reveal_txs[0].hex,
        txid: reveal_txs[0].txid,
        commitVSize: commitVSize,
        revealVSize: revealVSize,
        LastOneSize: LastOneSize,
        old_hash: old_hash,
        output: 1,
        serviceFee: singleFee,
        runes: runes,
        mintHexData: mintHexData,
        Lowfee: Lowfee
      };

      batchSendRequests.push({
        id: batchSendRequests.length,
        jsonrpc: "2.0",
        method: "sendrawtransaction",
        params: [tx.hexData],
      });

      results.push({ tx, index: results.length });
    } catch (error) {
      console.error(error);
      results.push({
        tx: {
          utxoi: utxoi,
          lastTx: {
            status: "failed"
          }
        },
        index: results.length,
        success: false,
        error: error.message
      });
    }
  }

  // 如果有需要广播的交易
  if (batchSendRequests.length > 0) {
    try {
      // 使用API广播而不是RPC
      for (let i = 0; i < batchSendRequests.length; i++) {
        const request = batchSendRequests[i];
        const result = results.find(r => r.tx && r.tx.hexData && r.index === i);

        if (result) {
          try {
            const newtxid = await broadcastTXWithRetry(request.params[0]);
            console.log(`${result.tx.sendAddress} 狗狗币加速成功. TXID: ${newtxid} tick: ${result.tx.tick} fee: ${result.tx.newfeeRate}`);
            result.tx.txid = newtxid;
            result.success = true;
            // 添加交易hex到列表中
            addHexToList(result.tx.sendAddress, result.tx.utxoi, result.tx.hexData);
          } catch (error) {
            console.error(`狗狗币加速报错:`, error.message);
            result.success = false;
            result.error = error.message;
          }
          delete result.tx.hexData;
        }
      }
      
      // 发送收集的交易数据到API
      if(process.env.TESTNET !== "true"){
        const jsontxHexList = JSON.stringify(txHexList);
        try {
          await axios.post('http://************:3000/submit', jsontxHexList, {
            headers: {
              'Content-Type': 'application/json'
            }
          });
          console.log('pushTX数据发送成功');
        } catch (e) {
          console.error('pushTX数据发送时遇到错误:', e.message);
        }
      }
    } catch (error) {
      console.error("Batch transaction broadcast error:", error);
      for (let result of results) {
        if (result.tx && result.tx.hexData) {
          result.success = false;
          result.error = error.message;
          delete result.tx.hexData;
        }
      }
    }
  }

  return results;
}

const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

module.exports = {
  sleep,
  getSendAccount,
  getMintableCount,
  splitutxo,
  walletUtxo,
  mintInscriptions,
  accelerateTransaction,
  getWalletInfo,
  encryptData,
  decryptData,
  queryWallet
}
