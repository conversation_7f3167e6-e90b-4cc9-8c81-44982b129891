import { Buff } from '@cmdcode/buff-utils';
import { Field, Point } from './ecc.js';
import { get_pubkey } from './keys.js';
import { hash160, hmac512 } from './hash.js';
import * as assert from './assert.js';
const INT_REGEX = /^[0-9]{0,10}$/, STR_REGEX = /^[0-9a-zA-Z_&?=]{64}$/;
export function derive(path, input_key, chain_code, is_private = false) {
    assert.valid_chain(path, chain_code);
    const key = Buff.bytes(input_key);
    let code = (chain_code !== undefined)
        ? Buff.bytes(chain_code)
        : Buff.str('Bitcoin seed');
    let prev = null, seckey = null, pubkey;
    if (path.startsWith('m')) {
        const root = generate_code(code, key);
        code = root[1];
        seckey = root[0];
        pubkey = get_pubkey(seckey, false);
    }
    else if (is_private) {
        assert.size(input_key, 32);
        seckey = key;
        pubkey = get_pubkey(seckey, false);
    }
    else {
        assert.size(key, 33);
        pubkey = key;
    }
    const tweaks = parse_tweaks(path);
    for (const [tweak, is_hardened] of tweaks) {
        const bytes = (is_hardened && seckey !== null)
            ? Buff.join([0x00, seckey, tweak])
            : Buff.join([pubkey, tweak]);
        const [next_key, next_code] = generate_code(code, bytes);
        code = Buff.raw(next_code);
        prev = pubkey;
        if (seckey !== null) {
            seckey = Field.mod(seckey).add(next_key).buff;
            pubkey = get_pubkey(seckey, false);
            assert.in_field(seckey.big, true);
        }
        else {
            pubkey = Point.from_x(pubkey).add(next_key).buff;
            assert.on_curve(pubkey.slice(1).big, true);
        }
    }
    return { seckey, pubkey, code, path, prev };
}
export function parse_tweaks(keypath) {
    assert.valid_path(keypath);
    const tweaks = [];
    let paths = keypath.split('/');
    if (paths[0] === 'm' || paths[0] === '') {
        paths = paths.slice(1);
    }
    for (let path of paths) {
        let is_hardened = false;
        if (path.slice(-1) === '\'') {
            is_hardened = true;
            path = path.slice(0, -1);
        }
        if (path.match(INT_REGEX) !== null) {
            let index = parseInt(path, 10);
            assert.valid_index(index);
            if (is_hardened)
                index += 0x80000000;
            tweaks.push([Buff.num(index, 4), is_hardened]);
        }
        else if (path.match(STR_REGEX) !== null) {
            let index = Buff.str(path);
            if (is_hardened)
                index = index.prepend(0x80);
            tweaks.push([index.digest, is_hardened]);
        }
        else {
            throw new Error('Invalid path segment:' + path);
        }
    }
    return tweaks;
}
export function generate_code(chain, data) {
    const I = hmac512(chain, data), IL = I.slice(0, 32), IR = I.slice(32);
    return [IL, IR];
}
export function encode_extkey(hdkey, key_prefix) {
    const { seckey, pubkey, code, prev, path } = hdkey;
    const prefix = (typeof key_prefix === 'number')
        ? Buff.num(key_prefix, 4)
        : (seckey !== null) ? 0x0488ade4 : 0x0488b21e;
    const tweaks = parse_tweaks(path);
    const tprev = tweaks.at(-1);
    const depth = Buff.num(tweaks.length, 1);
    const fprint = (prev !== null) ? hash160(prev).slice(0, 4) : Buff.num(0, 4);
    const index = (tprev !== undefined) ? tprev[0].slice(-4, 4) : Buff.num(0, 4);
    const key = (seckey !== null) ? seckey.prepend(0x00) : pubkey;
    return Buff.join([prefix, depth, fprint, index, code, key]).to_b58chk();
}
export function parse_extkey(keystr, path = '') {
    const { code, type, key } = decode_extkey(keystr);
    const is_private = (type === 0);
    const input_key = (is_private) ? key : Buff.join([type, key]);
    return derive(path, input_key, code, is_private);
}
export function decode_extkey(keystr) {
    const buffer = Buff.b58chk(keystr).stream;
    const prefix = buffer.read(4).num, depth = buffer.read(1).num, fprint = buffer.read(4).num, index = buffer.read(4).num, code = buffer.read(32).hex, type = buffer.read(1).num, key = buffer.read(32).hex, seckey = (type === 0) ? key : undefined, pubkey = (type === 0) ? get_pubkey(key).hex : Buff.join([type, key]).hex;
    if (buffer.size > 0) {
        throw new TypeError('Unparsed data remaining in buffer!');
    }
    return { prefix, depth, fprint, index, code, type, key, seckey, pubkey };
}
//# sourceMappingURL=hd.js.map