{"version": 3, "file": "ecc.d.ts", "sourceRoot": "", "sources": ["../src/ecc.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAQ,qBAAqB,CAAA;AAEnD,OAAO,EAAE,aAAa,EAAE,MAAM,oCAAoC,CAAA;AAOlE,KAAK,OAAO,GAAM,aAAa,CAAC,MAAM,CAAC,CAAA;AACvC,KAAK,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,UAAU,GAAG,KAAK,CAAA;AAC/D,KAAK,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,UAAU,GAAG,KAAK,CAAA;AAK/D,eAAO,MAAM,EAAE,sJAAa,CAAA;AAE5B,qBAAa,KAAM,SAAQ,UAAU;IACnC,MAAM,CAAC,CAAC,SAAK;IAEb,MAAM,CAAC,GAAG,CAAE,CAAC,EAAG,UAAU,EAAE,GAAI,KAAK;IAIrC,MAAM,CAAC,GAAG,CAAE,CAAC,EAAG,UAAU,GAAI,KAAK;IAInC,MAAM,CAAC,GAAG,CAAE,CAAC,EAAG,UAAU,EAAE,GAAI,KAAK;IAIrC,MAAM,CAAC,QAAQ,CAAE,KAAK,EAAG,KAAK,EAAE,MAAO,CAAC,EAAE,OAAO,GAAI,OAAO;gBAK/C,CAAC,EAAG,UAAU;IAM3B,IAAI,IAAI,IAAM,IAAI,CAEjB;IAED,IAAI,GAAG,IAAM,UAAU,CAEtB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,KAAK,IAAM,KAAK,CAEnB;IAED,IAAI,OAAO,IAAM,OAAO,CAEvB;IAED,IAAI,OAAO,IAAM,KAAK,CAIrB;IAED,EAAE,CAAE,KAAK,EAAG,UAAU,GAAI,OAAO;IAKjC,EAAE,CAAE,KAAK,EAAG,UAAU,GAAI,OAAO;IAKjC,EAAE,CAAE,KAAK,EAAG,UAAU,GAAI,OAAO;IAKjC,EAAE,CAAE,KAAK,EAAG,UAAU,GAAI,OAAO;IAKjC,GAAG,CAAE,KAAK,EAAG,UAAU,GAAI,KAAK;IAMhC,GAAG,CAAE,KAAK,EAAG,UAAU,GAAI,KAAK;IAMhC,GAAG,CAAE,KAAK,EAAG,UAAU,GAAI,KAAK;IAMhC,GAAG,CAAE,KAAK,EAAG,UAAU,GAAI,KAAK;IAMhC,GAAG,CAAE,KAAK,EAAG,UAAU,GAAI,KAAK;IAMhC,MAAM,IAAM,KAAK;IAKjB,QAAQ,IAAM,KAAK;CAKpB;AAED,qBAAa,KAAK;IAChB,MAAM,CAAC,CAAC,SAAS;IACjB,MAAM,CAAC,CAAC,QAA4B;IACpC,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAAkB;IAC9B,MAAM,CAAC,IAAI,wBAAkC;IAE7C,MAAM,CAAC,MAAM,CACX,KAAK,EAAG,KAAK,EACb,MAAM,UAAQ,GACZ,KAAK;IAcT,MAAM,CAAC,QAAQ,CAAE,KAAK,EAAG,UAAU,GAAI,KAAK;IAM5C,MAAM,CAAC,GAAG,wBAAiB;IAE3B,MAAM,CAAC,MAAM,CAAE,KAAK,EAAG,KAAK,GAAG,OAAO,GAAI,KAAK;IAQ/C,QAAQ,CAAC,EAAE,EAAG,OAAO,CAAA;gBAGnB,CAAC,EAAG,MAAM,EACV,CAAC,EAAG,MAAM;IAMZ,IAAI,CAAC,IAAM,OAAO,CAEjB;IAED,IAAI,CAAC,IAAM,IAAI,CAEd;IAED,IAAI,CAAC,IAAM,IAAI,CAEd;IAED,IAAI,IAAI,IAAM,IAAI,CAEjB;IAED,IAAI,GAAG,IAAM,UAAU,CAEtB;IAED,IAAI,GAAG,IAAM,MAAM,CAElB;IAED,IAAI,QAAQ,IAAM,OAAO,CAExB;IAED,IAAI,OAAO,IAAM,OAAO,CAEvB;IAED,IAAI,OAAO,IAAM,KAAK,CAIrB;IAED,EAAE,CAAE,KAAK,EAAG,UAAU,GAAI,OAAO;IAKjC,GAAG,CAAE,CAAC,EAAG,UAAU,GAAI,KAAK;IAM5B,GAAG,CAAE,CAAC,EAAG,UAAU,GAAI,KAAK;IAM5B,GAAG,CAAE,KAAK,EAAG,UAAU,GAAI,KAAK;IAMhC,MAAM,IAAM,KAAK;CAGlB"}