import { Buff } from '@cmdcode/buff-utils';
import { get_pubkey } from './keys.js';
import { sign, verify } from './sig.js';
import * as assert from './assert.js';
import * as util from './util.js';
const PROOF_DEFAULTS = {
    kind: 20000,
    stamp: 0x00000000,
    tags: []
};
export function create_proof(seckey, data, params, options) {
    const { kind, stamp, tags } = parse_config(params ?? []);
    const content = util.stringify(data);
    const pub = get_pubkey(seckey, true).hex;
    const ref = Buff.str(content).digest;
    const img = [0, pub, stamp, kind, tags, content];
    const pid = Buff.json(img).digest;
    const sig = sign(pid, seckey, options);
    return Buff.join([ref, pub, pid, sig]).hex + encode_params(params);
}
export function parse_proof(proof) {
    const [hexstr, query] = proof.split('?');
    const stream = Buff.hex(hexstr).stream;
    assert.ok(stream.size === 160);
    return {
        ref: stream.read(32).hex,
        pub: stream.read(32).hex,
        pid: stream.read(32).hex,
        sig: stream.read(64).hex,
        params: decode_params(query)
    };
}
export function parse_proofs(proofs) {
    return proofs.map(e => parse_proof(e));
}
export function validate_proof(proof) {
    const regex = /^[0-9a-fA-F]{320}(?:\?[A-Za-z0-9_]+=[A-Za-z0-9_]+(?:&[A-Za-z0-9_]+=[A-Za-z0-9_]+)*)?$/;
    return regex.test(proof);
}
export function verify_proof(proof, data, options) {
    const { throws = false } = options ?? {};
    const { ref, pub, pid, sig, params } = parse_proof(proof);
    const { kind, stamp, tags } = parse_config(params);
    const content = util.stringify(data);
    const content_ref = Buff.str(content).digest.hex;
    if (content_ref !== ref) {
        return assert.fail('Content hash does not match reference hash!', throws);
    }
    const img = [0, pub, stamp, kind, tags, content];
    const proof_hash = Buff.json(img).digest;
    if (proof_hash.hex !== pid) {
        return assert.fail('Proof hash does not equal proof id!', throws);
    }
    if (!verify(sig, pid, pub)) {
        return assert.fail('Proof signature is invalid!', throws);
    }
    return true;
}
export function create_event(proof, data) {
    const content = util.stringify(data);
    const { pub, pid, sig, params } = parse_proof(proof);
    const { kind, stamp, tags } = parse_config(params);
    return { kind, content, tags, pubkey: pub, id: pid, sig, created_at: stamp };
}
export function encode_params(params = []) {
    const strings = params.map(e => [String(e[0]), String(e[1])]);
    return (params.length !== 0)
        ? '?' + new URLSearchParams(strings).toString()
        : '';
}
export function decode_params(str) {
    return (typeof str === 'string')
        ? [...new URLSearchParams(str)]
        : [];
}
export function parse_config(params = []) {
    const { kind, stamp, ...rest } = Object.fromEntries(params);
    return {
        tags: Object.entries(rest).map(([k, v]) => [k, String(v)]),
        kind: (kind !== undefined) ? Number(kind) : PROOF_DEFAULTS.kind,
        stamp: (stamp !== undefined) ? Number(stamp) : PROOF_DEFAULTS.stamp
    };
}
//# sourceMappingURL=proof.js.map