{"version": 3, "file": "module.mjs", "sources": ["../node_modules/@noble/hashes/esm/_assert.js", "../node_modules/@noble/hashes/esm/utils.js", "../node_modules/@noble/hashes/esm/_sha2.js", "../node_modules/@noble/hashes/esm/sha256.js", "../node_modules/@scure/base/lib/esm/index.js", "../src/encode.ts", "../src/assert.ts", "../src/format/big.ts", "../src/format/bin.ts", "../src/format/num.ts", "../src/format/str.ts", "../src/utils.ts", "../src/buff.ts"], "sourcesContent": ["function number(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error(`Wrong positive integer: ${n}`);\n}\nfunction bool(b) {\n    if (typeof b !== 'boolean')\n        throw new Error(`Expected boolean, not ${b}`);\n}\nfunction bytes(b, ...lengths) {\n    if (!(b instanceof Uint8Array))\n        throw new Error('Expected Uint8Array');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error(`Expected Uint8Array of length ${lengths}, not of length=${b.length}`);\n}\nfunction hash(hash) {\n    if (typeof hash !== 'function' || typeof hash.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.wrapConstructor');\n    number(hash.outputLen);\n    number(hash.blockLen);\n}\nfunction exists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\nfunction output(out, instance) {\n    bytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n    }\n}\nexport { number, bool, bytes, hash, exists, output };\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n//# sourceMappingURL=_assert.js.map", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nconst u8a = (a) => a instanceof Uint8Array;\n// Cast array to different type\nexport const u8 = (arr) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr) => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = (arr) => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => (word << (32 - shift)) | (word >>> shift);\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE)\n    throw new Error('Non little-endian hardware is not supported');\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n    if (!u8a(bytes))\n        throw new Error('Uint8Array expected');\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    const len = hex.length;\n    if (len % 2)\n        throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n    const array = new Uint8Array(len / 2);\n    for (let i = 0; i < array.length; i++) {\n        const j = i * 2;\n        const hexByte = hex.slice(j, j + 2);\n        const byte = Number.parseInt(hexByte, 16);\n        if (Number.isNaN(byte) || byte < 0)\n            throw new Error('Invalid byte sequence');\n        array[i] = byte;\n    }\n    return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => { };\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await nextTick();\n        ts += diff;\n    }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    if (!u8a(data))\n        throw new Error(`expected Uint8Array, got ${typeof data}`);\n    return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n    const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n    let pad = 0; // walk through each item, ensure they have proper type\n    arrays.forEach((a) => {\n        if (!u8a(a))\n            throw new Error('Uint8Array expected');\n        r.set(a, pad);\n        pad += a.length;\n    });\n    return r;\n}\n// For runtime check if class implements interface\nexport class Hash {\n    // Safe version that clones internal state\n    clone() {\n        return this._cloneInto();\n    }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n    if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n        throw new Error('Options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\nexport function wrapConstructor(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n    if (crypto && typeof crypto.getRandomValues === 'function') {\n        return crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map", "import { exists, output } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nexport class SHA2 extends Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = createView(this.buffer);\n    }\n    update(data) {\n        exists(this);\n        const { view, buffer, blockLen } = this;\n        data = toBytes(data);\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = createView(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        exists(this);\n        output(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        this.buffer.subarray(pos).fill(0);\n        // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = createView(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.length = length;\n        to.pos = pos;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n}\n//# sourceMappingURL=_sha2.js.map", "import { SHA2 } from './_sha2.js';\nimport { rotr, wrapConstructor } from './utils.js';\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => (a & b) ^ (a & c) ^ (b & c);\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */ new Uint32Array([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends SHA2 {\n    constructor() {\n        super(64, 32, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = IV[0] | 0;\n        this.B = IV[1] | 0;\n        this.C = IV[2] | 0;\n        this.D = IV[3] | 0;\n        this.E = IV[4] | 0;\n        this.F = IV[5] | 0;\n        this.G = IV[6] | 0;\n        this.H = IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n            const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n            const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n            const T2 = (sigma0 + Maj(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        SHA256_W.fill(0);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        this.buffer.fill(0);\n    }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n    constructor() {\n        super();\n        this.A = 0xc1059ed8 | 0;\n        this.B = 0x367cd507 | 0;\n        this.C = 0x3070dd17 | 0;\n        this.D = 0xf70e5939 | 0;\n        this.E = 0xffc00b31 | 0;\n        this.F = 0x68581511 | 0;\n        this.G = 0x64f98fa7 | 0;\n        this.H = 0xbefa4fa4 | 0;\n        this.outputLen = 28;\n    }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n//# sourceMappingURL=sha256.js.map", "/*! scure-base - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Utilities\n/**\n * @__NO_SIDE_EFFECTS__\n */\nexport function assertNumber(n) {\n    if (!Number.isSafeInteger(n))\n        throw new Error(`Wrong integer: ${n}`);\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n    // Wrap call in closure so JIT can inline calls\n    const wrap = (a, b) => (c) => a(b(c));\n    // Construct chain of args[-1].encode(args[-2].encode([...]))\n    const encode = Array.from(args)\n        .reverse()\n        .reduce((acc, i) => (acc ? wrap(acc, i.encode) : i.encode), undefined);\n    // Construct chain of args[0].decode(args[1].decode(...))\n    const decode = args.reduce((acc, i) => (acc ? wrap(acc, i.decode) : i.decode), undefined);\n    return { encode, decode };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(alphabet) {\n    return {\n        encode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('alphabet.encode input should be an array of numbers');\n            return digits.map((i) => {\n                assertNumber(i);\n                if (i < 0 || i >= alphabet.length)\n                    throw new Error(`Digit index outside alphabet: ${i} (alphabet: ${alphabet.length})`);\n                return alphabet[i];\n            });\n        },\n        decode: (input) => {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('alphabet.decode input should be array of strings');\n            return input.map((letter) => {\n                if (typeof letter !== 'string')\n                    throw new Error(`alphabet.decode: not string element=${letter}`);\n                const index = alphabet.indexOf(letter);\n                if (index === -1)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${alphabet}`);\n                return index;\n            });\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n    if (typeof separator !== 'string')\n        throw new Error('join separator should be string');\n    return {\n        encode: (from) => {\n            if (!Array.isArray(from) || (from.length && typeof from[0] !== 'string'))\n                throw new Error('join.encode input should be array of strings');\n            for (let i of from)\n                if (typeof i !== 'string')\n                    throw new Error(`join.encode: non-string input=${i}`);\n            return from.join(separator);\n        },\n        decode: (to) => {\n            if (typeof to !== 'string')\n                throw new Error('join.decode input should be string');\n            return to.split(separator);\n        },\n    };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n    assertNumber(bits);\n    if (typeof chr !== 'string')\n        throw new Error('padding chr should be string');\n    return {\n        encode(data) {\n            if (!Array.isArray(data) || (data.length && typeof data[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of data)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.encode: non-string input=${i}`);\n            while ((data.length * bits) % 8)\n                data.push(chr);\n            return data;\n        },\n        decode(input) {\n            if (!Array.isArray(input) || (input.length && typeof input[0] !== 'string'))\n                throw new Error('padding.encode input should be array of strings');\n            for (let i of input)\n                if (typeof i !== 'string')\n                    throw new Error(`padding.decode: non-string input=${i}`);\n            let end = input.length;\n            if ((end * bits) % 8)\n                throw new Error('Invalid padding: string should have whole number of bytes');\n            for (; end > 0 && input[end - 1] === chr; end--) {\n                if (!(((end - 1) * bits) % 8))\n                    throw new Error('Invalid padding: string has too much padding');\n            }\n            return input.slice(0, end);\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize(fn) {\n    if (typeof fn !== 'function')\n        throw new Error('normalize fn should be function');\n    return { encode: (from) => from, decode: (to) => fn(to) };\n}\n/**\n * Slow: O(n^2) time complexity\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix(data, from, to) {\n    // base 1 is impossible\n    if (from < 2)\n        throw new Error(`convertRadix: wrong from=${from}, base cannot be less than 2`);\n    if (to < 2)\n        throw new Error(`convertRadix: wrong to=${to}, base cannot be less than 2`);\n    if (!Array.isArray(data))\n        throw new Error('convertRadix: data should be array');\n    if (!data.length)\n        return [];\n    let pos = 0;\n    const res = [];\n    const digits = Array.from(data);\n    digits.forEach((d) => {\n        assertNumber(d);\n        if (d < 0 || d >= from)\n            throw new Error(`Wrong integer: ${d}`);\n    });\n    while (true) {\n        let carry = 0;\n        let done = true;\n        for (let i = pos; i < digits.length; i++) {\n            const digit = digits[i];\n            const digitBase = from * carry + digit;\n            if (!Number.isSafeInteger(digitBase) ||\n                (from * carry) / from !== carry ||\n                digitBase - digit !== from * carry) {\n                throw new Error('convertRadix: carry overflow');\n            }\n            carry = digitBase % to;\n            const rounded = Math.floor(digitBase / to);\n            digits[i] = rounded;\n            if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n                throw new Error('convertRadix: carry overflow');\n            if (!done)\n                continue;\n            else if (!rounded)\n                pos = i;\n            else\n                done = false;\n        }\n        res.push(carry);\n        if (done)\n            break;\n    }\n    for (let i = 0; i < data.length - 1 && data[i] === 0; i++)\n        res.push(0);\n    return res.reverse();\n}\nconst gcd = /* @__NO_SIDE_EFFECTS__ */ (a, b) => (!b ? a : gcd(b, a % b));\nconst radix2carry = /*@__NO_SIDE_EFFECTS__ */ (from, to) => from + (to - gcd(from, to));\n/**\n * Implemented with numbers, because BigInt is 5x slower\n * @__NO_SIDE_EFFECTS__\n */\nfunction convertRadix2(data, from, to, padding) {\n    if (!Array.isArray(data))\n        throw new Error('convertRadix2: data should be array');\n    if (from <= 0 || from > 32)\n        throw new Error(`convertRadix2: wrong from=${from}`);\n    if (to <= 0 || to > 32)\n        throw new Error(`convertRadix2: wrong to=${to}`);\n    if (radix2carry(from, to) > 32) {\n        throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n    }\n    let carry = 0;\n    let pos = 0; // bitwise position in current element\n    const mask = 2 ** to - 1;\n    const res = [];\n    for (const n of data) {\n        assertNumber(n);\n        if (n >= 2 ** from)\n            throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n        carry = (carry << from) | n;\n        if (pos + from > 32)\n            throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n        pos += from;\n        for (; pos >= to; pos -= to)\n            res.push(((carry >> (pos - to)) & mask) >>> 0);\n        carry &= 2 ** pos - 1; // clean carry, otherwise it will cause overflow\n    }\n    carry = (carry << (to - pos)) & mask;\n    if (!padding && pos >= from)\n        throw new Error('Excess padding');\n    if (!padding && carry)\n        throw new Error(`Non-zero padding: ${carry}`);\n    if (padding && pos > 0)\n        res.push(carry >>> 0);\n    return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n    assertNumber(num);\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix.encode input should be Uint8Array');\n            return convertRadix(Array.from(bytes), 2 ** 8, num);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix.decode input should be array of strings');\n            return Uint8Array.from(convertRadix(digits, num, 2 ** 8));\n        },\n    };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n    assertNumber(bits);\n    if (bits <= 0 || bits > 32)\n        throw new Error('radix2: bits should be in (0..32]');\n    if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n        throw new Error('radix2: carry overflow');\n    return {\n        encode: (bytes) => {\n            if (!(bytes instanceof Uint8Array))\n                throw new Error('radix2.encode input should be Uint8Array');\n            return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n        },\n        decode: (digits) => {\n            if (!Array.isArray(digits) || (digits.length && typeof digits[0] !== 'number'))\n                throw new Error('radix2.decode input should be array of strings');\n            return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction unsafeWrapper(fn) {\n    if (typeof fn !== 'function')\n        throw new Error('unsafeWrapper fn should be function');\n    return function (...args) {\n        try {\n            return fn.apply(null, args);\n        }\n        catch (e) { }\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction checksum(len, fn) {\n    assertNumber(len);\n    if (typeof fn !== 'function')\n        throw new Error('checksum fn should be function');\n    return {\n        encode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.encode: input should be Uint8Array');\n            const checksum = fn(data).slice(0, len);\n            const res = new Uint8Array(data.length + len);\n            res.set(data);\n            res.set(checksum, data.length);\n            return res;\n        },\n        decode(data) {\n            if (!(data instanceof Uint8Array))\n                throw new Error('checksum.decode: input should be Uint8Array');\n            const payload = data.slice(0, -len);\n            const newChecksum = fn(payload).slice(0, len);\n            const oldChecksum = data.slice(-len);\n            for (let i = 0; i < len; i++)\n                if (newChecksum[i] !== oldChecksum[i])\n                    throw new Error('Invalid checksum');\n            return payload;\n        },\n    };\n}\nexport const utils = { alphabet, chain, checksum, radix, radix2, join, padding };\n// RFC 4648 aka RFC 3548\n// ---------------------\nexport const base16 = /* @__PURE__ */ chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\nexport const base32 = /* @__PURE__ */ chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), padding(5), join(''));\nexport const base32hex = /* @__PURE__ */ chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), padding(5), join(''));\nexport const base32crockford = /* @__PURE__ */ chain(radix2(5), alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'), join(''), normalize((s) => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1')));\nexport const base64 = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\nexport const base64url = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\nexport const base64urlnopad = /* @__PURE__ */ chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), join(''));\n// base58 code\n// -----------\nconst genBase58 = (abc) => chain(radix(58), alphabet(abc), join(''));\nexport const base58 = /* @__PURE__ */ genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\nexport const base58flickr = /* @__PURE__ */ genBase58('123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ');\nexport const base58xrp = /* @__PURE__ */ genBase58('rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz');\n// xmr ver is done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n// Block encoding significantly reduces quadratic complexity of base58.\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\nexport const base58xmr = {\n    encode(data) {\n        let res = '';\n        for (let i = 0; i < data.length; i += 8) {\n            const block = data.subarray(i, i + 8);\n            res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length], '1');\n        }\n        return res;\n    },\n    decode(str) {\n        let res = [];\n        for (let i = 0; i < str.length; i += 11) {\n            const slice = str.slice(i, i + 11);\n            const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n            const block = base58.decode(slice);\n            for (let j = 0; j < block.length - blockLen; j++) {\n                if (block[j] !== 0)\n                    throw new Error('base58xmr: wrong padding');\n            }\n            res = res.concat(Array.from(block.slice(block.length - blockLen)));\n        }\n        return Uint8Array.from(res);\n    },\n};\nexport const base58check = /* @__PURE__ */ (sha256) => chain(checksum(4, (data) => sha256(sha256(data))), base58);\nconst BECH_ALPHABET = /* @__PURE__ */ chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bech32Polymod(pre) {\n    const b = pre >> 25;\n    let chk = (pre & 0x1ffffff) << 5;\n    for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n        if (((b >> i) & 1) === 1)\n            chk ^= POLYMOD_GENERATORS[i];\n    }\n    return chk;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n    const len = prefix.length;\n    let chk = 1;\n    for (let i = 0; i < len; i++) {\n        const c = prefix.charCodeAt(i);\n        if (c < 33 || c > 126)\n            throw new Error(`Invalid prefix (${prefix})`);\n        chk = bech32Polymod(chk) ^ (c >> 5);\n    }\n    chk = bech32Polymod(chk);\n    for (let i = 0; i < len; i++)\n        chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n    for (let v of words)\n        chk = bech32Polymod(chk) ^ v;\n    for (let i = 0; i < 6; i++)\n        chk = bech32Polymod(chk);\n    chk ^= encodingConst;\n    return BECH_ALPHABET.encode(convertRadix2([chk % 2 ** 30], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n    const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n    const _words = radix2(5);\n    const fromWords = _words.decode;\n    const toWords = _words.encode;\n    const fromWordsUnsafe = unsafeWrapper(fromWords);\n    function encode(prefix, words, limit = 90) {\n        if (typeof prefix !== 'string')\n            throw new Error(`bech32.encode prefix should be string, not ${typeof prefix}`);\n        if (!Array.isArray(words) || (words.length && typeof words[0] !== 'number'))\n            throw new Error(`bech32.encode words should be array of numbers, not ${typeof words}`);\n        const actualLength = prefix.length + 7 + words.length;\n        if (limit !== false && actualLength > limit)\n            throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n        const lowered = prefix.toLowerCase();\n        const sum = bechChecksum(lowered, words, ENCODING_CONST);\n        return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n    }\n    function decode(str, limit = 90) {\n        if (typeof str !== 'string')\n            throw new Error(`bech32.decode input should be string, not ${typeof str}`);\n        if (str.length < 8 || (limit !== false && str.length > limit))\n            throw new TypeError(`Wrong string length: ${str.length} (${str}). Expected (8..${limit})`);\n        // don't allow mixed case\n        const lowered = str.toLowerCase();\n        if (str !== lowered && str !== str.toUpperCase())\n            throw new Error(`String must be lowercase or uppercase`);\n        str = lowered;\n        const sepIndex = str.lastIndexOf('1');\n        if (sepIndex === 0 || sepIndex === -1)\n            throw new Error(`Letter \"1\" must be present between prefix and data only`);\n        const prefix = str.slice(0, sepIndex);\n        const _words = str.slice(sepIndex + 1);\n        if (_words.length < 6)\n            throw new Error('Data must be at least 6 characters long');\n        const words = BECH_ALPHABET.decode(_words).slice(0, -6);\n        const sum = bechChecksum(prefix, words, ENCODING_CONST);\n        if (!_words.endsWith(sum))\n            throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n        return { prefix, words };\n    }\n    const decodeUnsafe = unsafeWrapper(decode);\n    function decodeToBytes(str) {\n        const { prefix, words } = decode(str, false);\n        return { prefix, words, bytes: fromWords(words) };\n    }\n    return { encode, decode, decodeToBytes, decodeUnsafe, fromWords, fromWordsUnsafe, toWords };\n}\nexport const bech32 = /* @__PURE__ */ genBech32('bech32');\nexport const bech32m = /* @__PURE__ */ genBech32('bech32m');\nexport const utf8 = {\n    encode: (data) => new TextDecoder().decode(data),\n    decode: (str) => new TextEncoder().encode(str),\n};\nexport const hex = /* @__PURE__ */ chain(radix2(4), alphabet('0123456789abcdef'), join(''), normalize((s) => {\n    if (typeof s !== 'string' || s.length % 2)\n        throw new TypeError(`hex.decode: expected string, got ${typeof s} with length ${s.length}`);\n    return s.toLowerCase();\n}));\n// prettier-ignore\nconst CODERS = {\n    utf8, hex, base16, base32, base64, base64url, base58, base58xmr\n};\nconst coderTypeError = 'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\nexport const bytesToString = (type, bytes) => {\n    if (typeof type !== 'string' || !CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (!(bytes instanceof Uint8Array))\n        throw new TypeError('bytesToString() expects Uint8Array');\n    return CODERS[type].encode(bytes);\n};\nexport const str = bytesToString; // as in python, but for bytes only\nexport const stringToBytes = (type, str) => {\n    if (!CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (typeof str !== 'string')\n        throw new TypeError('stringToBytes() expects string');\n    return CODERS[type].decode(str);\n};\nexport const bytes = stringToBytes;\n", null, null, null, null, null, null, null, null], "names": ["fmt.strToBytes", "fmt.bytesToStr", "util.is_bytes", "util.is_hex", "util.random", "fmt.buffer_data", "fmt.bytesToNum"], "mappings": "AAQA,SAAS,KAAK,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE;AAC9B,IAAI,IAAI,EAAE,CAAC,YAAY,UAAU,CAAC;AAClC,QAAQ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC/C,IAAI,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;AACzD,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/F,CAAC;AAOD,SAAS,MAAM,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAI,EAAE;AAChD,IAAI,IAAI,QAAQ,CAAC,SAAS;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,IAAI,IAAI,aAAa,IAAI,QAAQ,CAAC,QAAQ;AAC1C,QAAQ,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;AACjE,CAAC;AACD,SAAS,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE;AAC/B,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;AACnC,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,sDAAsD,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACxF,KAAK;AACL;;AChCA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,UAAU,CAAC;AAI3C;AACO,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AAC5F;AACO,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAC/E;AACA;AACO,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACrF,IAAI,CAAC,IAAI;AACT,IAAI,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAoDnE;AACA;AACA;AACO,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,IAAI,EAAE;AAC9B,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;AAChC,QAAQ,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;AAClB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AAeD;AACO,MAAM,IAAI,CAAC;AAClB;AACA,IAAI,KAAK,GAAG;AACZ,QAAQ,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;AACjC,KAAK;AACL,CAAC;AAQM,SAAS,eAAe,CAAC,QAAQ,EAAE;AAC1C,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACpE,IAAI,MAAM,GAAG,GAAG,QAAQ,EAAE,CAAC;AAC3B,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;AACpC,IAAI,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;AAClC,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,QAAQ,EAAE,CAAC;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB;;AC7HA;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;AACrD,IAAI,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,UAAU;AAC/C,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1D,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAC5B,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,QAAQ,CAAC,CAAC;AAClD,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;AACxC,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC7C,CAAC;AACD;AACO,MAAM,IAAI,SAAS,IAAI,CAAC;AAC/B,IAAI,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE;AACtD,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACxB,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACrB,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC/B,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,CAAC,IAAI,EAAE;AACjB,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,QAAQ,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAChD,QAAQ,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;AAChC,QAAQ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG;AACtC,YAAY,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,YAAY,IAAI,IAAI,KAAK,QAAQ,EAAE;AACnC,gBAAgB,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AAClD,gBAAgB,OAAO,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI,QAAQ;AAC7D,oBAAoB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AAChD,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AACjE,YAAY,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;AAC7B,YAAY,GAAG,IAAI,IAAI,CAAC;AACxB,YAAY,IAAI,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE;AACvC,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,gBAAgB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,UAAU,CAAC,GAAG,EAAE;AACpB,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,QAAQ,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AACtD,QAAQ,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3B;AACA,QAAQ,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA,QAAQ,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,GAAG,EAAE;AAC7C,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAClC,YAAY,GAAG,GAAG,CAAC,CAAC;AACpB,SAAS;AACT;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE;AAC3C,YAAY,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1B;AACA;AACA;AACA,QAAQ,YAAY,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACxE,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC9B,QAAQ,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC;AACnB,YAAY,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC3E,QAAQ,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;AAC/B,QAAQ,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAClE,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;AACvC,YAAY,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,GAAG;AACb,QAAQ,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;AAC3C,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAChC,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;AACvB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AACL,IAAI,UAAU,CAAC,EAAE,EAAE;AACnB,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAC5C,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B,QAAQ,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAC5E,QAAQ,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC;AACrB,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC/B,QAAQ,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;AACjC,QAAQ,IAAI,MAAM,GAAG,QAAQ;AAC7B,YAAY,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK;AACL;;AC9GA;AACA;AACA;AACA,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C;AACA,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA,MAAM,QAAQ,mBAAmB,IAAI,WAAW,CAAC;AACjD,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,CAAC,CAAC,CAAC;AACH;AACA;AACA,MAAM,EAAE,mBAAmB,IAAI,WAAW,CAAC;AAC3C,IAAI,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;AAClG,CAAC,CAAC,CAAC;AACH;AACA;AACA,MAAM,QAAQ,mBAAmB,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;AACrD,MAAM,MAAM,SAAS,IAAI,CAAC;AAC1B,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAChC;AACA;AACA,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,GAAG,GAAG;AACV,QAAQ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AAChD,QAAQ,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC,KAAK;AACL;AACA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1B;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;AAChD,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxD,QAAQ,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACtC,YAAY,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,YAAY,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACjE,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;AAC7E,SAAS;AACT;AACA,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;AAC9C,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;AACrC,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACnF,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAClE,YAAY,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AACnD,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC7B,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAC9B,SAAS;AACT;AACA,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7B,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,UAAU,GAAG;AACjB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,KAAK;AACL,CAAC;AAgBD;AACA;AACA;AACA;AACO,MAAM,MAAM,mBAAmB,eAAe,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;;AC3HzE;AACA;AAGG;AAKH;AACA;AACA;AACA,SAAS,KAAK,CAAC,GAAG,IAAI,EAAE;AACxB;AACA,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C;AACA,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACnC,SAAS,OAAO,EAAE;AAClB,SAAS,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAC/E;AACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AAC9F,IAAI,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,QAAQ,CAAC,QAAQ,EAAE;AAC5B,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,MAAM,KAAK;AAC5B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1F,gBAAgB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;AACvF,YAAY,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AAErC,gBAAgB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,MAAM;AACjD,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACzG,gBAAgB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACnC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,KAAK,KAAK;AAC3B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACvF,gBAAgB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACpF,YAAY,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AACzC,gBAAgB,IAAI,OAAO,MAAM,KAAK,QAAQ;AAC9C,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACrF,gBAAgB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACvD,gBAAgB,IAAI,KAAK,KAAK,CAAC,CAAC;AAChC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzF,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,SAAS,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE;AAC9B,IAAI,IAAI,OAAO,SAAS,KAAK,QAAQ;AACrC,QAAQ,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AAC3D,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,IAAI,KAAK;AAC1B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACpF,gBAAgB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AAChF,YAAY,KAAK,IAAI,CAAC,IAAI,IAAI;AAC9B,gBAAgB,IAAI,OAAO,CAAC,KAAK,QAAQ;AACzC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1E,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,EAAE,KAAK;AACxB,YAAY,IAAI,OAAO,EAAE,KAAK,QAAQ;AACtC,gBAAgB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AACtE,YAAY,OAAO,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACvC,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE;AAElC,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AACxD,IAAI,OAAO;AACX,QAAQ,MAAM,CAAC,IAAI,EAAE;AACrB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACpF,gBAAgB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACnF,YAAY,KAAK,IAAI,CAAC,IAAI,IAAI;AAC9B,gBAAgB,IAAI,OAAO,CAAC,KAAK,QAAQ;AACzC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC;AAC3C,gBAAgB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,MAAM,CAAC,KAAK,EAAE;AACtB,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACvF,gBAAgB,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACnF,YAAY,KAAK,IAAI,CAAC,IAAI,KAAK;AAC/B,gBAAgB,IAAI,OAAO,CAAC,KAAK,QAAQ;AACzC,oBAAoB,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,YAAY,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;AACnC,YAAY,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC;AAChC,gBAAgB,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;AAC7F,YAAY,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE,EAAE;AAC7D,gBAAgB,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AAC7C,oBAAoB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACpF,aAAa;AACb,YAAY,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACvC,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AASD;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC;AACA,IAAI,IAAI,IAAI,GAAG,CAAC;AAChB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;AACxF,IAAI,IAAI,EAAE,GAAG,CAAC;AACd,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE,EAAE,CAAC,4BAA4B,CAAC,CAAC,CAAC;AACpF,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5B,QAAQ,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,QAAQ,OAAO,EAAE,CAAC;AAClB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AAE1B,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;AAC9B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,EAAE;AACjB,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;AACtB,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC;AACxB,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,YAAY,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACpC,YAAY,MAAM,SAAS,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC;AACnD,YAAY,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AAChD,gBAAgB,CAAC,IAAI,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;AAC/C,gBAAgB,SAAS,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,EAAE;AACpD,gBAAgB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAChE,aAAa;AACb,YAAY,KAAK,GAAG,SAAS,GAAG,EAAE,CAAC;AACnC,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC,CAAC;AACvD,YAAY,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAChC,YAAY,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,OAAO,GAAG,EAAE,GAAG,KAAK,KAAK,SAAS;AACpF,gBAAgB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAChE,YAAY,IAAI,CAAC,IAAI;AACrB,gBAAgB,SAAS;AACzB,iBAAiB,IAAI,CAAC,OAAO;AAC7B,gBAAgB,GAAG,GAAG,CAAC,CAAC;AACxB;AACA,gBAAgB,IAAI,GAAG,KAAK,CAAC;AAC7B,SAAS;AACT,QAAQ,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,QAAQ,IAAI,IAAI;AAChB,YAAY,MAAM;AAClB,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAC7D,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpB,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC;AACzB,CAAC;AACD,MAAM,GAAG,8BAA8B,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,MAAM,WAAW,6BAA6B,CAAC,IAAI,EAAE,EAAE,KAAK,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AACxF;AACA;AACA;AACA;AACA,SAAS,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE;AAChD,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5B,QAAQ,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AAC/D,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;AAC9B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;AAC1B,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;AACpC,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClH,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC7B,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;AAE1B,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAC1B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAClF,QAAQ,KAAK,GAAG,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;AACpC,QAAQ,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE;AAC3B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACrF,QAAQ,GAAG,IAAI,IAAI,CAAC;AACpB,QAAQ,OAAO,GAAG,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE;AACnC,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,GAAG,EAAE,CAAC,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC;AAC3D,QAAQ,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC;AACzC,IAAI,IAAI,CAAC,OAAO,IAAI,GAAG,IAAI,IAAI;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK;AACzB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACtD,IAAI,IAAI,OAAO,IAAI,GAAG,GAAG,CAAC;AAC1B,QAAQ,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;AAC9B,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA,SAAS,KAAK,CAAC,GAAG,EAAE;AAEpB,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,KAAK,KAAK;AAC3B,YAAY,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC;AAC9C,gBAAgB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AAC3E,YAAY,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AAChE,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,MAAM,KAAK;AAC5B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1F,gBAAgB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AACjF,YAAY,OAAO,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtE,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAAS,MAAM,CAAC,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE;AAE1C,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE;AAC9B,QAAQ,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;AAC7D,IAAI,IAAI,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE;AAC9D,QAAQ,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAClD,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,CAAC,KAAK,KAAK;AAC3B,YAAY,IAAI,EAAE,KAAK,YAAY,UAAU,CAAC;AAC9C,gBAAgB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;AAC5E,YAAY,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,CAAC;AAC1E,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,MAAM,KAAK;AAC5B,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AAC1F,gBAAgB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;AAClF,YAAY,OAAO,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;AAC/E,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,SAAS,aAAa,CAAC,EAAE,EAAE;AAC3B,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU;AAChC,QAAQ,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AAC/D,IAAI,OAAO,UAAU,GAAG,IAAI,EAAE;AAC9B,QAAQ,IAAI;AACZ,YAAY,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE,GAAG;AACrB,KAAK,CAAC;AACN,CAAC;AACD;AACA;AACA;AACA,SAAS,QAAQ,CAAC,GAAG,EAAE,EAAE,EAAE;AAE3B,IAAI,IAAI,OAAO,EAAE,KAAK,UAAU;AAChC,QAAQ,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AAC1D,IAAI,OAAO;AACX,QAAQ,MAAM,CAAC,IAAI,EAAE;AACrB,YAAY,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;AAC7C,gBAAgB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC/E,YAAY,MAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACpD,YAAY,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;AAC1D,YAAY,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,YAAY,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC3C,YAAY,OAAO,GAAG,CAAC;AACvB,SAAS;AACT,QAAQ,MAAM,CAAC,IAAI,EAAE;AACrB,YAAY,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;AAC7C,gBAAgB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC/E,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,MAAM,WAAW,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1D,YAAY,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AACjD,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AACxC,gBAAgB,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC;AACrD,oBAAoB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACxD,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AAQM,MAAM,MAAM,mBAAmB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,kEAAkE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACpJ,MAAM,SAAS,mBAAmB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,kEAAkE,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAE9J;AACA;AACA,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9D,MAAM,MAAM,mBAAmB,SAAS,CAAC,4DAA4D,CAAC,CAAC;AA+BvG,MAAM,WAAW,mBAAmB,CAAC,MAAM,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAClH,MAAM,aAAa,mBAAmB,KAAK,CAAC,QAAQ,CAAC,kCAAkC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACpG,MAAM,kBAAkB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AACxF;AACA;AACA;AACA,SAAS,aAAa,CAAC,GAAG,EAAE;AAC5B,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;AACxB,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxD,QAAQ,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAChC,YAAY,GAAG,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC;AACzC,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACD;AACA;AACA;AACA,SAAS,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,GAAG,CAAC,EAAE;AACxD,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AAC9B,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAChB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClC,QAAQ,MAAM,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAChC,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;AACjE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK;AACvB,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC9B,QAAQ,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,IAAI,GAAG,IAAI,aAAa,CAAC;AACzB,IAAI,OAAO,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9E,CAAC;AACD;AACA;AACA;AACA,SAAS,SAAS,CAAC,QAAQ,EAAE;AAC7B,IAAI,MAAM,cAAc,GAAG,QAAQ,KAAK,QAAQ,GAAG,CAAC,GAAG,UAAU,CAAC;AAClE,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAClC,IAAI,MAAM,eAAe,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC;AACrD,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE;AAC/C,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ;AACtC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,2CAA2C,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3F,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACnF,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oDAAoD,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AACnG,QAAQ,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AAC9D,QAAQ,IAAI,KAAK,KAAK,KAAK,IAAI,YAAY,GAAG,KAAK;AACnD,YAAY,MAAM,IAAI,SAAS,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACjF,QAAQ,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;AAC7C,QAAQ,MAAM,GAAG,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;AACjE,QAAQ,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,KAAK,GAAG,EAAE,EAAE;AACrC,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ;AACnC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,0CAA0C,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AACvF,QAAQ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;AACrE,YAAY,MAAM,IAAI,SAAS,CAAC,CAAC,qBAAqB,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACvG;AACA,QAAQ,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAQ,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,WAAW,EAAE;AACxD,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,qCAAqC,CAAC,CAAC,CAAC;AACrE,QAAQ,GAAG,GAAG,OAAO,CAAC;AACtB,QAAQ,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAQ,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC7C,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,uDAAuD,CAAC,CAAC,CAAC;AACvF,QAAQ,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC9C,QAAQ,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC/C,QAAQ,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;AACvE,QAAQ,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAChE,QAAQ,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;AAChE,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;AACjC,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,QAAQ,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACjC,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AAC/C,IAAI,SAAS,aAAa,CAAC,GAAG,EAAE;AAChC,QAAQ,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACrD,QAAQ,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC;AAChG,CAAC;AACM,MAAM,MAAM,mBAAmB,SAAS,CAAC,QAAQ,CAAC,CAAC;AACnD,MAAM,OAAO,mBAAmB,SAAS,CAAC,SAAS,CAAC;;gBCnatC;;;;;;;;;;;;;;AAgBN,QAAA,QAAA,EAAA,MAAA,CAAA;wBAOK,CAAA,SAAA;;;;;;;;AAYL,KAAA;;;;;;SAcd;;;;;;;;AC7DD,SAAA,WAA2B,CAAA,IAAA,EAAA,IACrB,EAAA;AAQN,IAAA,IAAA,IAAA,CAAA,MAAA,GAAA;AASA,QAAA,MAAA,IAAA,2CAMC,EAAA,IAAA,CAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAED,KAAA;AAMA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChChB,MAAA,GAAS,GAAK,MAAA,CAAA,CAAM,CAAE,CAAA;AAyBtB,MAAA,KAAA,GAAA,MAAA,CAAA,GAAgB;AAuBhB,MAAA,KAAA,GAAA,MAAA,CAAA,GAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChDhB,SAAA,mBAA4B;AAoB5B,IAAA,MAAA,IAAA,GAAA,MAAA,CAAgB,UAAU,GAAE,CAAA,MAAkB,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN9C,SAAA,QAAA,CAAA,GAAA,EAAA;AAsBA,IAAA,IAAA,GAAA,IAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9BA,MAAA,EAAA,GAAA,IAAA;AAIA,MAAA,EAAA,GAAA,IAAA;AAiBA,SAAA;AAsBA,IAAA,OAAA,EAAA,CAAA,MAAA,CAAA,GAAgB;AAQhB,CAAA;;;CAGC;AAED,SAAA,eAAgB,EAAA,IAAA,EAAA;;;IAGf,IAAA,IAAA,KAAA,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5DD,MAAA,EAAA,eAAA,WAAwB,IAAS,qBAKhC,MAAA,CAAA,MAAA,CAAA;AAED,SAAA,MAAA,CAAA,OAAsB,EAAE,EAAA;AAQxB,IAAgB,IAAA,OAAA,eAAA,KAAe,UAAU,EAAA;AAsBzC,QAAA,OAAA,MAAA,CAAA,eACE,CAAA,IAAe,UAAK,CAAU,IAAA,CAAA,CAAA,CAAA;AAYhC,KAAA;AAcA,IAAgB,MAAA,IAAA,KAAA,CAAA,wCAAwC,CAIvD,CAAA;AAED,CAAgB;AAMhB,SAAA,MAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtEQ,UAAqB,SAAA,UAAA,CAAA;IAC3B,aAA2B,CAAA,GAAA,GAAA,SAAA,CAAA,EAAA;IAC3B,aAA2B,CAAA,GAAA,GAAA,SAAA,CAAA,EAAA;IAC3B,aAA2B,CAAA,GAAA,GAAA,SAAA,CAAA,EAAA;IAC3B,aAA2B,CAAA,GAAA,GAAA,SAAA,CAAA,EAAA;IAC3B,aAAwB,CAAA,GAAA,GAAA,SAAA,CAAA,EAAA;IACxB,aAA4B,CAAA,GAAA,GAAA,SAAA,CAAA,EAAA;IAC5B,aAAa,CAAiB,KAAA,GAAA,MAAA,CAAA,EAAA;IAC9B,aAAa,CAAiB,IAAA,GAAA,UAAA,CAAA,EAAA;IAC9B,aAAa,CAAiB,MAAA,GAAA,YAAA,CAAA,EAAA;IAC9B,cAAc,MAAiB,GAAA,YAAA,CAAA,EAAA;IAC/B,aAAa,CAAiB,MAAA,GAAA,YAAA,CAAA,EAAA;IAC9B,aAAa,CAAmB,OAAA,GAAA,aAAA,CAAA,EAAA;IAChC,aAAa,CAAmB,MAAA,GAAA,YAAA,CAAA,EAAA;IAChC,aAA4B,CAAA,MAAA,GAAAA,UAAA,CAAA,EAAA;IAC5B,oBAA+B,GAAAC,UAAA,CAAA,EAAA;IAC/B,aAAa,CAAgB,KAAA,GAAA,UAAA,CAAA,EAAA;AAE7B,IAAA,SAAa,IAAA,CAAE,QAAI,GAAAC,QAAa,CAAA,EAAA;AAKhC,IAAA,SAAU,IAAM,CAAA,MAAA,GAAAC,MAAY,CAAA,EAAA;AAM1B,IAAA,OAAA,MAAA,CAAA,IAAU,GAAK,EAAA,EAAA;AAejB,QAAI,MAAG,IAAY,GAAAC,MAElB,CAAA,IAAA,CAAA,CAAA;QAEG,OAAS,IAAA,IAEZ,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;KAEG;IAIJ,OAAO,GAAM,CAAA,IAAA,GAEZ,CAAA,EAAA;QAEG,MAAS,KAAA,GAEZ,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,GAAA,IAAA,CAAA,CAAA;QAEG,OAAS,IAAA,IAAA,CAAA,KAEZ,EAAA,IAAA,CAAA,CAAA;KAEG;IAIJ,WAAgB,CAAA,IAAA,EAAA,IAEf,EAAA,MAAA,EAAA;QAEG,IAAA,IAAY,YAEf,IAAA;YAEG,IAAY,KAAA,SAEf,EAAA;YAEG,OAAY,IAEf,CAAA;SAEG;QAIA,MAAM,MAAM,GAAAC,WAEf,CAAA,IAAA,EAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AAED,QAAA,KAAQ,CAAA,MAAS,CAAA,CAAA;AAOjB,KAAA;AAOA,IAAA,IAAA,MAAY;AAIZ,QAAA,OAAa,CAAI,GAAA,IAAA,CAAA,CAAA;KAKV;IAQP,IAAS,GAAA,GACP;QAQQ,OACR,IAAM,CAAA,MAAS,EAAA,CAAA;AAQjB,KAAA;AACA,IAAA,IAAA,MAAe;AACf,QAAA,WAAyB,CAAA,MAAA,EAAA,CAAA;AACzB,KAAA;AACA,IAAA,IAAA,GAAA;AACA,QAAA,WAAqB,CAAA,MAAA,EAAA,CAAA;AAErB,KAAA;AAIA,IAAA,IAAA,GAAO,GAAE;AAIT,QAAA,OAAa,IAAI,CAAA,MAAA,EAAA,CAAA;KAKZ;AAKL,IAAA,IAAK,GAAA,GAAQ;QAIL,OAAQ,IAAC,UAAU,CAAA,IAAK,CAAE,CAAM;KAKnC;AAKL,IAAA,IAAA,GAAA,GAAU;QAKJ,OAAK,IAAM,CAAA;KAIX;IAIN,IAAM,SAAO;AAMb,QAAA,OAAW,IAAK,UAAY,EAAA,CAAA;AAM5B,KAAA;AAaD,IAAA,IAAA,MAAA,GAAA;AAED,QAAA,OAAA,cACE,EAAA,CAAA;AAOF,KAAA;AAQA,IAAA,IAAA,MAAA,GAAS;AAQT,QAAA,OAAA,cACE,EAAI,CAAM;AAOZ,KAAA;AAQA,IAAA,IAAA,MAAA,GAAS;AAQT,QAAA,OAAA,IAAmB,CAAA,OAAA,EAAG,CAAA;AAWtB,KAAA;AAMA,IAAA,IAAA,EAAA,GAAA;AAMA,QAAA,OAAA,cAAqB,CACnB,GAAA,CAAI;AAaN,KAAA;AAcA,IAAA,IAAA,MAAA,GAAS;AAMT,QAAA,OAAA,eACE,CAAA,CAAA;AASF,KAAA;IACS,MAAO,OAAM,GAAA,IAAA,EAAA;QACT,cAAa,CAAA,MAAA,KAAA,IAAA;AAEX,cAAA,IAAA,CAAA,OAAY,EAAA;AAKzB,cAAa,IAAA,CAAA;AAOb,QAAI,OAASC,UAAc,CAAA,KAAA,CAAA,CAAA;AAO3B,KAAA;AAeD,IAAA,MAAA,CAAA,MAAA,GAAA,IAAA,EAAA;AAED,QAAgB,MAAA,KAAA,GAAA,CAAA,MAAA,KACT,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4]}