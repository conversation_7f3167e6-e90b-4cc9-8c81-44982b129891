{"version": 3, "file": "hd.js", "sourceRoot": "", "sources": ["../src/hd.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAS,MAAQ,qBAAqB,CAAA;AACnD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAO,UAAU,CAAA;AACxC,OAAO,EAAE,UAAU,EAAE,MAAS,WAAW,CAAA;AAGzC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAA;AAE5C,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAIrC,MAAM,SAAS,GAAG,eAAe,EAC3B,SAAS,GAAG,uBAAuB,CAAA;AAEzC,MAAM,UAAU,MAAM,CACpB,IAAoB,EACpB,SAAmB,EACnB,UAAmB,EACnB,UAAU,GAAI,KAAK;IAGnB,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;IAEpC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;IAEjC,IAAI,IAAI,GAAG,CAAC,UAAU,KAAK,SAAS,CAAC;QACnC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;QACxB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;IAE5B,IAAI,IAAI,GAAmB,IAAI,EAC3B,MAAM,GAAiB,IAAI,EAC3B,MAAa,CAAA;IAEjB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QAExB,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QACrC,IAAI,GAAK,IAAI,CAAC,CAAC,CAAC,CAAA;QAChB,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;QAChB,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;KACnC;SAAM,IAAI,UAAU,EAAE;QAErB,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;QAC1B,MAAM,GAAG,GAAG,CAAA;QACZ,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;KACnC;SAAM;QAEL,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;QACpB,MAAM,GAAG,GAAG,CAAA;KACb;IAED,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;IAEjC,KAAK,MAAM,CAAE,KAAK,EAAE,WAAW,CAAE,IAAI,MAAM,EAAE;QAE3C,MAAM,KAAK,GAAG,CAAC,WAAW,IAAI,MAAM,KAAK,IAAI,CAAC;YAC5C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,IAAI,EAAE,MAAM,EAAE,KAAK,CAAE,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,MAAM,EAAE,KAAK,CAAE,CAAC,CAAA;QAEhC,MAAM,CAAE,QAAQ,EAAE,SAAS,CAAE,GAAG,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAE1D,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAE1B,IAAI,GAAG,MAAM,CAAA;QAEb,IAAI,MAAM,KAAK,IAAI,EAAE;YAEnB,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAA;YAC7C,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAClC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;SAClC;aAAM;YAEL,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAA;YAChD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;SAC3C;KACF;IAED,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAA;AAC7C,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,OAAgB;IAGhB,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;IAC1B,MAAM,MAAM,GAAa,EAAE,CAAA;IAE3B,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAE9B,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QACvC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;KACvB;IAED,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;QACpB,IAAI,WAAW,GAAG,KAAK,CAAA;QAEzB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC3B,WAAW,GAAG,IAAI,CAAA;YAClB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;SACzB;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAClC,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;YAC9B,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;YACzB,IAAI,WAAW;gBAAE,KAAK,IAAI,UAAU,CAAA;YACpC,MAAM,CAAC,IAAI,CAAC,CAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,WAAW,CAAE,CAAC,CAAA;SACjD;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YACzC,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC1B,IAAI,WAAW;gBAAE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC5C,MAAM,CAAC,IAAI,CAAC,CAAE,KAAK,CAAC,MAAM,EAAE,WAAW,CAAE,CAAC,CAAA;SAC3C;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAA;SAChD;KACF;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,KAAa,EACb,IAAa;IAGb,MAAM,CAAC,GAAI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EACzB,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EACnB,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;IAEtB,OAAO,CAAE,EAAE,EAAE,EAAE,CAAE,CAAA;AACnB,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,KAAa,EACb,UAAoB;IAEpB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAA;IAClD,MAAM,MAAM,GAAG,CAAC,OAAO,UAAU,KAAK,QAAQ,CAAC;QAC7C,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAA;IAC/C,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;IACjC,MAAM,KAAK,GAAI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5B,MAAM,KAAK,GAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;IACzC,MAAM,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC3E,MAAM,KAAK,GAAI,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC7E,MAAM,GAAG,GAAM,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;IAChE,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAE,CAAC,CAAC,SAAS,EAAE,CAAA;AAC3E,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,MAAe,EACf,OAAkB,EAAE;IAEpB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,CAAA;IACjD,MAAM,UAAU,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAA;IAC/B,MAAM,SAAS,GAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,IAAI,EAAE,GAAG,CAAE,CAAC,CAAA;IAChE,OAAO,MAAM,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,CAAA;AAClD,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,MAAe;IAKf,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAA;IAEzC,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAC3B,KAAK,GAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAC3B,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAC3B,KAAK,GAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAC3B,IAAI,GAAK,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAC5B,IAAI,GAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAC3B,GAAG,GAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,EAC5B,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EACvC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,IAAI,EAAE,GAAG,CAAE,CAAC,CAAC,GAAG,CAAA;IAEhF,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,EAAE;QACnB,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAA;KAC1D;IAED,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;AAC1E,CAAC"}