import { z } from 'zod';
export declare const Schema: {
    TxData: z.ZodObject<{
        version: z.ZodNumber;
        vin: z.<PERSON><z.ZodObject<{
            txid: z.ZodString;
            vout: z.<PERSON>odNumber;
            scriptSig: z.<PERSON><[z.Zod<PERSON>rray<z.ZodU<PERSON>n<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>;
            sequence: z.ZodNumber;
            prevout: z.Zod<PERSON>ptional<z.ZodObject<{
                value: z.ZodUnion<[z.ZodNumber, z.ZodBigInt]>;
                scriptPubKey: z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.<PERSON><PERSON>, z.<PERSON>, z.Zod<PERSON>tring, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.Zod<PERSON>tring, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>;
            }, "strip", z.ZodTypeAny, {
                value: number | bigint;
                scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            }, {
                value: number | bigint;
                scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            }>>;
            witness: z.ZodArray<z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">;
        }, "strip", z.ZodTypeAny, {
            vout: number;
            witness: (string | Uint8Array | (string | number | Uint8Array)[])[];
            txid: string;
            scriptSig: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            sequence: number;
            prevout?: {
                value: number | bigint;
                scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            } | undefined;
        }, {
            vout: number;
            witness: (string | Uint8Array | (string | number | Uint8Array)[])[];
            txid: string;
            scriptSig: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            sequence: number;
            prevout?: {
                value: number | bigint;
                scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            } | undefined;
        }>, "many">;
        vout: z.ZodArray<z.ZodObject<{
            value: z.ZodUnion<[z.ZodNumber, z.ZodBigInt]>;
            scriptPubKey: z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>;
        }, "strip", z.ZodTypeAny, {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        }, {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        }>, "many">;
        locktime: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        version: number;
        vin: {
            vout: number;
            witness: (string | Uint8Array | (string | number | Uint8Array)[])[];
            txid: string;
            scriptSig: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            sequence: number;
            prevout?: {
                value: number | bigint;
                scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            } | undefined;
        }[];
        vout: {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        }[];
        locktime: number;
    }, {
        version: number;
        vin: {
            vout: number;
            witness: (string | Uint8Array | (string | number | Uint8Array)[])[];
            txid: string;
            scriptSig: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            sequence: number;
            prevout?: {
                value: number | bigint;
                scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
            } | undefined;
        }[];
        vout: {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        }[];
        locktime: number;
    }>;
    TxInput: z.ZodObject<{
        txid: z.ZodString;
        vout: z.ZodNumber;
        scriptSig: z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>;
        sequence: z.ZodNumber;
        prevout: z.ZodOptional<z.ZodObject<{
            value: z.ZodUnion<[z.ZodNumber, z.ZodBigInt]>;
            scriptPubKey: z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>;
        }, "strip", z.ZodTypeAny, {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        }, {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        }>>;
        witness: z.ZodArray<z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">;
    }, "strip", z.ZodTypeAny, {
        vout: number;
        witness: (string | Uint8Array | (string | number | Uint8Array)[])[];
        txid: string;
        scriptSig: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        sequence: number;
        prevout?: {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        } | undefined;
    }, {
        vout: number;
        witness: (string | Uint8Array | (string | number | Uint8Array)[])[];
        txid: string;
        scriptSig: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        sequence: number;
        prevout?: {
            value: number | bigint;
            scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
        } | undefined;
    }>;
    TxOutput: z.ZodObject<{
        value: z.ZodUnion<[z.ZodNumber, z.ZodBigInt]>;
        scriptPubKey: z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>;
    }, "strip", z.ZodTypeAny, {
        value: number | bigint;
        scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
    }, {
        value: number | bigint;
        scriptPubKey: (string | Uint8Array | (string | number | Uint8Array)[]) & (string | Uint8Array | (string | number | Uint8Array)[] | undefined);
    }>;
    witness: z.ZodArray<z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">;
    script: z.ZodUnion<[z.ZodArray<z.ZodUnion<[z.ZodString, z.ZodNumber, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>, "many">, z.ZodString, z.ZodType<Uint8Array, z.ZodTypeDef, Uint8Array>]>;
    hexstr: z.ZodString;
    hash: z.ZodString;
    uint32: z.ZodNumber;
    uint64: z.ZodBigInt;
};
//# sourceMappingURL=check.d.ts.map