import { Bytes } from '@cmdcode/buff-utils';
export declare function ok(value: unknown, message?: string): asserts value;
export declare function fail(error: string, throws?: boolean): boolean;
export declare function size(input: Bytes, size: number, throws?: boolean): boolean;
export declare function exists<T>(input?: T | null): asserts input is NonNullable<T>;
export declare function on_curve(x: bigint, throws?: boolean): boolean;
export declare function in_field(x: bigint, throws?: boolean): boolean;
export declare function valid_chain(path: string, code?: Bytes): void;
export declare function valid_path(path: string): void;
export declare function valid_hash(hash: string): void;
export declare function valid_index(index: number): void;
export declare function valid_pubkey(pubkey: Bytes): void;
export declare function valid_derive_state(hardened: boolean, is_private: boolean): void;
//# sourceMappingURL=assert.d.ts.map