{"version": 3, "file": "bundle.js", "sources": ["../../src/ts/browser-base64.ts", "../../src/ts/index.ts"], "sourcesContent": ["export const base64Encode = (bytes: Uint8Array): string => {\n  const CHUNK_SIZE = 0x8000\n  const arr = []\n  for (let i = 0; i < bytes.length; i += CHUNK_SIZE) {\n    // @ts-expect-error\n    arr.push(String.fromCharCode.apply(null, bytes.subarray(i, i + CHUNK_SIZE)))\n  }\n  return btoa(arr.join(''))\n}\n\nexport const base64Decode = (encoded: string): Uint8Array => {\n  return new Uint8Array(\n    atob(encoded)\n      .split('')\n      .map((c) => c.charCodeAt(0))\n  )\n}\n", "/**\n * Base64url for both node.js and brwser javascript. It can work with ArrayBuffer|TypedArray|Buffer\n *\n * @remarks Bowser code obtained from https://github.com/panva/jose/blob/main/src/runtime/browser/base64url.ts\n * @packageDocumentation\n */\n\nimport { base64Encode, base64Decode } from './browser-base64.js'\n\n/**\n * A TypedArray object describes an array-like view of an underlying binary data buffer.\n */\nexport type TypedArray = Int8Array | Uint8Array | Uint8ClampedArray | Int16Array | Uint16Array | Int32Array | Uint32Array | Float32Array | Float64Array | BigInt64Array | BigUint64Array\n\n/**\n * Base64Url encoding of a buffer input or a string (UTF16 in browsers, UTF8 in node)\n * @param input\n * @param urlsafe - if true Base64 URL encoding is used ('+' and '/' are replaced by '-', '_')\n * @param padding - if false, padding (trailing '=') is removed\n * @returns a string with the base64-encoded representation of the input\n */\nexport function encode (input: ArrayBufferLike | TypedArray | Buffer | string, urlsafe: boolean = false, padding: boolean = true): string {\n  let base64 = ''\n  if (IS_BROWSER) {\n    const bytes = (typeof input === 'string')\n      ? (new TextEncoder()).encode(input)\n      : new Uint8Array(input)\n    base64 = base64Encode(bytes)\n  } else {\n    const bytes = (typeof input === 'string')\n      ? Buffer.from(input, 'utf8')\n      : Buffer.from(input)\n    base64 = bytes.toString('base64')\n  }\n  if (urlsafe) base64 = base64ToBase64url(base64)\n  if (!padding) base64 = removeBase64Padding(base64)\n  return base64\n}\n\n/**\n * Base64url decoding (binary output) of base64url-encoded string\n * @param base64 - a base64 string\n * @param stringOutput - if true a UTF16 (browser) or UTF8 (node) string is returned\n * @returns a buffer or unicode string\n */\nexport function decode (base64: string): Uint8Array\nexport function decode (base64: string, stringOutput: undefined): Uint8Array\nexport function decode (base64: string, stringOutput: false): Uint8Array\nexport function decode (base64: string, stringOutput: true): string\nexport function decode (base64: string, stringOutput: boolean): Uint8Array | string\nexport function decode (base64: string, stringOutput: undefined | boolean = false): Uint8Array | string {\n  if (IS_BROWSER) {\n    let urlsafe = false\n    if (/^[0-9a-zA-Z_-]+={0,2}$/.test(base64)) {\n      urlsafe = true\n    } else if (!/^[0-9a-zA-Z+/]*={0,2}$/.test(base64)) {\n      throw new Error('Not a valid base64 input')\n    }\n    if (urlsafe) base64 = base64urlToBase64(base64)\n    const bytes = base64Decode(base64)\n    return stringOutput\n      ? (new TextDecoder()).decode(bytes)\n      : bytes\n  } else {\n    const buffer = Buffer.from(base64, 'base64')\n    return stringOutput\n      ? buffer.toString('utf8')\n      : new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.length)\n  }\n}\n\nfunction base64ToBase64url (base64: string): string {\n  return base64.replace(/\\+/g, '-').replace(/\\//g, '_')\n}\n\nfunction base64urlToBase64 (base64url: string): string {\n  return base64url.replace(/-/g, '+').replace(/_/g, '/').replace(/=/g, '')\n}\n\nfunction removeBase64Padding (str: string): string {\n  return str.replace(/=/g, '')\n}\n"], "names": [], "mappings": "AAAO,MAAM,YAAY,GAAG,CAAC,KAAiB,KAAY;IACxD,MAAM,UAAU,GAAG,MAAM,CAAA;IACzB,MAAM,GAAG,GAAG,EAAE,CAAA;AACd,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,UAAU,EAAE;QAEjD,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;AAC7E,KAAA;IACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC3B,CAAC,CAAA;AAEM,MAAM,YAAY,GAAG,CAAC,OAAe,KAAgB;AAC1D,IAAA,OAAO,IAAI,UAAU,CACnB,IAAI,CAAC,OAAO,CAAC;SACV,KAAK,CAAC,EAAE,CAAC;AACT,SAAA,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAC/B,CAAA;AACH,CAAC;;ACKK,SAAU,MAAM,CAAE,KAAqD,EAAE,OAAmB,GAAA,KAAK,EAAE,OAAA,GAAmB,IAAI,EAAA;IAC9H,IAAI,MAAM,GAAG,EAAE,CAAA;AACf,IAAgB;AACd,QAAA,MAAM,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,QAAQ;cACpC,CAAC,IAAI,WAAW,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC;AACnC,cAAE,IAAI,UAAU,CAAC,KAAK,CAAC,CAAA;AACzB,QAAA,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;AAC7B,KAKA;AACD,IAAA,IAAI,OAAO;AAAE,QAAA,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;AAC/C,IAAA,IAAI,CAAC,OAAO;AAAE,QAAA,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAA;AAClD,IAAA,OAAO,MAAM,CAAA;AACf,CAAC;SAae,MAAM,CAAE,MAAc,EAAE,eAAoC,KAAK,EAAA;AAC/E,IAAgB;QACd,IAAI,OAAO,GAAG,KAAK,CAAA;AACnB,QAAA,IAAI,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACzC,OAAO,GAAG,IAAI,CAAA;AACf,SAAA;AAAM,aAAA,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACjD,YAAA,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;AAC5C,SAAA;AACD,QAAA,IAAI,OAAO;AAAE,YAAA,MAAM,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;AAC/C,QAAA,MAAM,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;AAClC,QAAA,OAAO,YAAY;cACf,CAAC,IAAI,WAAW,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC;cACjC,KAAK,CAAA;AACV,KAKA;AACH,CAAC;AAED,SAAS,iBAAiB,CAAE,MAAc,EAAA;AACxC,IAAA,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AACvD,CAAC;AAED,SAAS,iBAAiB,CAAE,SAAiB,EAAA;IAC3C,OAAO,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;AAC1E,CAAC;AAED,SAAS,mBAAmB,CAAE,GAAW,EAAA;IACvC,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;AAC9B;;;;"}