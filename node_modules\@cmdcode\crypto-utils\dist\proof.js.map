{"version": 3, "file": "proof.js", "sourceRoot": "", "sources": ["../src/proof.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAS,MAAO,qBAAqB,CAAA;AAClD,OAAO,EAAE,UAAU,EAAE,MAAQ,WAAW,CAAA;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,UAAU,CAAA;AAGvC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,IAAI,MAAQ,WAAW,CAAA;AAQnC,MAAM,cAAc,GAAG;IACrB,IAAI,EAAI,KAAK;IACb,KAAK,EAAG,UAAU;IAClB,IAAI,EAAI,EAAiB;CAC1B,CAAA;AAED,MAAM,UAAU,YAAY,CAC1B,MAAgB,EAChB,IAAY,EACZ,MAAsB,EACtB,OAAsB;IAEtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;IAExD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAEpC,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,CAAA;IAExC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAA;IAEpC,MAAM,GAAG,GAAG,CAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAE,CAAA;IAElD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAA;IAEjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;IAEtC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE,CAAC,CAAC,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAA;AACtE,CAAC;AAED,MAAM,UAAU,WAAW,CAAE,KAAc;IACzC,MAAM,CAAE,MAAM,EAAE,KAAK,CAAE,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAA;IACtC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,CAAA;IAC9B,OAAO;QACL,GAAG,EAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;QAC5B,GAAG,EAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;QAC5B,GAAG,EAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;QAC5B,GAAG,EAAM,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG;QAC5B,MAAM,EAAG,aAAa,CAAC,KAAK,CAAC;KAC9B,CAAA;AACH,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,MAAiB;IAEjB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;AACxC,CAAC;AAED,MAAM,UAAU,cAAc,CAAE,KAAc;IAG5C,MAAM,KAAK,GAAG,uFAAuF,CAAA;IACrG,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAC1B,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,KAAiB,EACjB,IAAY,EACZ,OAAsB;IAEtB,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,OAAO,IAAI,EAAE,CAAA;IAExC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAA;IAEzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;IAElD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAEpC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAA;IAEhD,IAAI,WAAW,KAAK,GAAG,EAAE;QACvB,OAAO,MAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE,MAAM,CAAC,CAAA;KAC1E;IAED,MAAM,GAAG,GAAG,CAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAE,CAAA;IAElD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAA;IAExC,IAAI,UAAU,CAAC,GAAG,KAAK,GAAG,EAAE;QAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAA;KAClE;IAED,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;QAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAA;KAC1D;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,KAAc,EACd,IAAS;IAGT,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;IAEpC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,CAAA;IAEpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAA;IAElD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE,CAAA;AAC9E,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,SAAuB,EAAE;IAGzB,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAA;IAE/D,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,GAAG,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE;QAC/C,CAAC,CAAC,EAAE,CAAA;AACR,CAAC;AAED,MAAM,UAAU,aAAa,CAAE,GAAa;IAE1C,OAAO,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC;QAC9B,CAAC,CAAC,CAAE,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,CAAE;QACjC,CAAC,CAAC,EAAE,CAAA;AACR,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,SAAuB,EAAE;IAGzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IAE3D,OAAO;QACL,IAAI,EAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC,EAAE,CAAC,CAAE,EAAE,EAAE,CAAC,CAAE,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAE,CAAC;QAChE,IAAI,EAAI,CAAC,IAAI,KAAM,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC,cAAc,CAAC,IAAI;QACnE,KAAK,EAAG,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK;KACrE,CAAA;AACH,CAAC"}