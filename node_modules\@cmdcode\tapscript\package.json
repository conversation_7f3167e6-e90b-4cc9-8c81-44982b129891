{"name": "@cmdcode/tapscript", "version": "1.4.6", "description": "A basic library for working with Tapscript, signatures and Bitcoin transactions.", "type": "module", "main": "./dist/main.cjs", "unpkg": "./dist/bundle.min.js", "types": "./dist/types/index.d.ts", "exports": {".": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/module.mjs"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/main.cjs"}}}, "scripts": {"build": "rollup -c rollup.config.ts --configPlugin typescript", "clean": "rm -rf dist/* coverage .nyc_output", "start": "node --trace-deprecation --no-warnings --loader ts-node/esm", "scratch": "yarn start test/scratch.ts", "tape": "yarn start test/tape.ts", "test": "yarn tape | tap-spec", "types": "tsc", "release": "yarn test && yarn clean && yarn types && yarn build"}, "keywords": ["bitcoin", "transaction", "psbt", "serialize", "encode", "decode", "validate"], "author": "<PERSON>", "license": "CC-BY-1.0", "repository": {"type": "git", "url": "https://github.com/cmdruid/tapscript.git"}, "publishConfig": {"registry": "https://registry.npmjs.org", "access": "public"}, "files": ["README.md", "LICENSE", "dist"], "devDependencies": {"@cmdcode/buff-utils": "^1.9.7", "@cmdcode/crypto-utils": "^2.0.2", "@cmdcode/keylink": "^1.4.3", "@noble/curves": "^0.9.1", "@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^9.0.2", "@types/node": "^18.14.0", "@types/tape": "^4.13.2", "rollup": "^3.2.3", "tap-spec": "^5.0.0", "tape": "^5.6.1", "ts-node": "^10.9.1", "tslib": "^2.6.2", "typescript": "^5.1.6", "zod": "^3.20.6"}}