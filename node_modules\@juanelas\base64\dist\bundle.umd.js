!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).base64={})}(this,(function(e){"use strict";e.decode=function(e,t=!1){{let n=!1;if(/^[0-9a-zA-Z_-]+={0,2}$/.test(e))n=!0;else if(!/^[0-9a-zA-Z+/]*={0,2}$/.test(e))throw new Error("Not a valid base64 input");n&&(e=e.replace(/-/g,"+").replace(/_/g,"/").replace(/=/g,""));const o=new Uint8Array(atob(e).split("").map((e=>e.charCodeAt(0))));return t?(new TextDecoder).decode(o):o}},e.encode=function(e,t=!1,n=!0){let o="";return o=(e=>{const t=[];for(let n=0;n<e.length;n+=32768)t.push(String.fromCharCode.apply(null,e.subarray(n,n+32768)));return btoa(t.join(""))})("string"==typeof e?(new TextEncoder).encode(e):new Uint8Array(e)),t&&(o=function(e){return e.replace(/\+/g,"-").replace(/\//g,"_")}(o)),n||(o=o.replace(/=/g,"")),o}}));
