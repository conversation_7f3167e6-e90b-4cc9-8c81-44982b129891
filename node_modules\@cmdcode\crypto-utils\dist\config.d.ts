import { Bytes } from '@cmdcode/buff-utils';
export type SignOptions = Partial<SignConfig>;
export interface SignConfig {
    aux?: Bytes | null;
    adaptor?: Bytes;
    nonce?: Bytes;
    nonce_tweaks?: Bytes[];
    recovery?: Bytes;
    tweak?: Bytes;
    throws: boolean;
    xonly: boolean;
}
export declare function sign_config(config?: SignOptions): SignConfig;
//# sourceMappingURL=config.d.ts.map